/*
Copyright © 2024 NAME HERE <EMAIL ADDRESS>
*/
package cmd

import (
	"context"
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cobra"

	qlog "gitlab.qomolo.com/infrastructure/sw_architecture/middleware_abstraction/utility/qlog_golang"
)

// serviceCmd represents the service command
var serviceCmd = &cobra.Command{
	Use:   "service",
	Short: "service",
	Long:  `service`,
	Run: func(cmd *cobra.Command, _ []string) {
		qlog.Info("service called")

		port, _ := cmd.Flags().GetString("port")
		ip, _ := cmd.Flags().GetString("ip")
		runServer(ip, port)
	},
}

func init() {
	rootCmd.AddCommand(serviceCmd)

	serviceCmd.Flags().StringP("ip", "", "0.0.0.0", "ip")
	serviceCmd.Flags().StringP("port", "", "8765", "port")
}

type PullReq struct {
	Image string `json:"image"`
}

func runServer(ip, port string) {
	router := gin.Default()

	// gin.SetMode(gin.ReleaseMode)

	// server status
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"health": "ok",
		})
	})

	api := router.Group("/docker")

	api.POST("/pull", func(c *gin.Context) {
		var pullReq PullReq
		if err := c.ShouldBind(&pullReq); err == nil {
			if len(pullReq.Image) == 0 {
				c.JSON(http.StatusBadRequest, gin.H{
					"code": http.StatusBadRequest,
					"msg":  "image must not be empty",
				})
				return
			}
			ctx := context.Background()
			err := dockerPull(ctx, pullReq.Image)
			msg := fmt.Sprintf("pull image %s success", pullReq.Image)
			code := http.StatusOK
			if err != nil {
				msg = "docker pull err " + err.Error()
				code = http.StatusBadRequest
			}
			c.JSON(code, gin.H{
				"code": code,
				"msg":  msg,
			})
			return
		}

	})

	err := router.Run(ip + ":" + port)
	if err != nil {
		return
	}
}

func dockerPull(ctx context.Context, image string) error {
	qp := getQPackage()
	return getQPackage().DpkgUtil.DockerPull(ctx, qp.GetImageDomain(), image)
}
