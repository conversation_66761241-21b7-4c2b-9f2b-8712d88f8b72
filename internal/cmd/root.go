/*
Copyright © 2022 NAME HERE <EMAIL ADDRESS>
*/
package cmd

import (
	"fmt"
	"os"
	"slices"

	qlog "gitlab.qomolo.com/infrastructure/sw_architecture/middleware_abstraction/utility/qlog_golang"
	"gitlab.qomolo.com/infrastructure/sw_architecture/middleware_abstraction/utility/qlog_golang/config"
	"gitlab.qomolo.com/qomolo_utils/qomolo_get/internal/lib"
	"gitlab.qomolo.com/qomolo_utils/qomolo_get/pkg/qpackage"
	"gitlab.qomolo.com/qomolo_utils/qomolo_get/pkg/utils"

	"github.com/spf13/cobra"
	"github.com/spf13/viper"
	"go.uber.org/zap"
)

const (
	configFile                 string = "/etc/qomolo/profile/qomolo-get/profile.yaml"
	sourceBaseDir                     = "/tmp/qomolo-get"
	logFile                    string = "/var/log/qomolo-get/qomolo-get.log"
	historyFile                string = "/data/qomolo-get/history.log"
	qomoloGetDownloadHistoryDB string = "/data/qomolo-get/download.db"
	qomoloGetInstallHistoryDB  string = "/data/qomolo-get/install.db"
	qomoloGetModuleHistoryDB   string = "/data/qomolo-get/module.db"
	tmpDownloadDir             string = "/data/MP/tmp"
)

var sugarLogger *zap.SugaredLogger
var qPackage *qpackage.QPackage

// rootCmd represents the base command when called without any subcommands
var rootCmd = &cobra.Command{
	Use:          "qomolo-get",
	Short:        "qomolo-get",
	Long:         `qomolo-get`,
	SilenceUsage: true,
	// Uncomment the following line if your bare application
	// has an action associated with it:
	// Run: func(cmd *cobra.Command, args []string) { },
}

// Execute adds all child commands to the root command and sets flags appropriately.
// This is called by main.main(). It only needs to happen once to the rootCmd.
func Execute() {
	err := rootCmd.Execute()
	if err != nil {
		os.Exit(1)
	}
}

func init() {
	viper.SetDefault("logFile", logFile)
	cobra.OnInitialize(initConfig)
	rootCmd.PersistentFlags().BoolP("default-source", "", false, "use system default apt source(/etc/apt/*)")
	rootCmd.PersistentFlags().BoolP("local-repo", "", false, "use local repo list(/tmp/qomolo-get/local-source.list)")
	rootCmd.PersistentFlags().BoolP("local-repo-v2", "", false, "use local repo list(/tmp/qomolo-get/local-source.list)")
}

// initConfig reads in config file and ENV variables if set.
func initConfig() {
	viper.SetConfigFile(configFile)
	viper.AutomaticEnv() // read in environment variables that match
	debug := viper.GetBool("DEBUG")
	// If a config file is found, read it in.
	if err := viper.ReadInConfig(); err == nil {
		if debug {
			fmt.Fprintln(os.Stderr, "Using config file:", viper.ConfigFileUsed())
			qlog.SetLevel(config.LogLevelDebug)
		}
	}
	sugarLogger = lib.InitLogger(viper.GetString("logFile"), viper.GetBool("QLOG_STD_DISABLED"), debug)
	defer func(sugarLogger *zap.SugaredLogger) {
		_ = sugarLogger.Sync()
	}(sugarLogger)
}

func initQPackage() {
	_ = os.Setenv("QLOG_CMD_STDOUT", "true")
	opt := qpackage.Option{
		HostType:                  getHostType(),
		HostID:                    getHostID(),
		Project:                   getProjectName(),
		VehicleType:               getVehicleType(),
		Chassis:                   getChassis(),
		RepoURL:                   "",
		RepoRawPath:               "",
		RepoUsername:              "",
		RepoPassword:              "",
		RepoMode:                  "",
		ConfigPath:                configFile,
		SourceBaseDir:             sourceBaseDir,
		SourceType:                "",
		SkipCheckTarget:           true,
		DownloadHistoryDBFilepath: qomoloGetDownloadHistoryDB,
		InstallHistoryDBFilepath:  qomoloGetInstallHistoryDB,
		ModuleHistoryDBFilepath:   qomoloGetModuleHistoryDB,
		ImageDomain:               "",
		DevopsBaseUrl:             "",
	}
	isDefaultSource, _ := rootCmd.Flags().GetBool("default-source")
	isLocalRepo, _ := rootCmd.Flags().GetBool("local-repo")
	isLocalRepoV2, _ := rootCmd.Flags().GetBool("local-repo-v2")
	privateNetworkProject := viper.GetStringSlice("default_use_local_repo_project")
	// 私网场地默认使用 qmu repo
	if isLocalRepoV2 || isLocalRepo || (qpackage.CheckUseLocalRepo() && slices.Contains(privateNetworkProject, getProjectName())) {
		opt.RepoURL = viper.GetString("nexus.project.url")
		opt.RepoRawPath = viper.GetString("nexus.project.raw_path")
		opt.RepoMode = qpackage.RepoModeProjectOnly
		opt.ImageDomain = viper.GetString("harbor.project.domain")
	} else {
		opt.RepoURL = viper.GetString("nexus.wwl.url")
		opt.RepoRawPath = viper.GetString("nexus.wwl.raw_path")
		opt.RepoMode = qpackage.RepoModeWWLOnly
		opt.ImageDomain = viper.GetString("harbor.wwl.domain")
	}

	opt.DevopsBaseUrl = viper.GetString("devops.base_url")

	if isDefaultSource {
		opt.SourceType = qpackage.SourceFileTypeDefault
	} else {
		if isLocalRepoV2 || isLocalRepo {
			opt.SourceType = qpackage.SourceFileTypeProject
		} else {
			opt.SourceType = qpackage.SourceFileTypeWWL
		}
	}
	qlog.BuildLoggerWithConfig(
		config.Config{
			LogOut: []config.LogOut{
				{
					Type: config.LogOutFile,
					Path: logFile,
				},
				{
					Type: config.LogOutStdout,
				},
			},
		},
		qlog.WithNoStacktrace(true),
	)
	q, err := qpackage.New(opt)
	if err != nil {
		sugarLogger.Fatalf("init qpackage err: %s", err)
		return
	}
	qPackage = q
}

func getQPackage() *qpackage.QPackage {
	if qPackage == nil {
		initQPackage()
	}
	return qPackage
}

func getProjectName() string {
	name, _ := utils.GetProjectName()
	return name
}

func getVehicleType() utils.VehicleType {
	name, _ := utils.GetVehicleType()
	return name
}

func getChassis() string {
	name, _ := utils.GetChassis()
	return name
}

func getHostType() utils.HostType {
	name, _ := utils.GetHostType()
	return name
}

func getHostID() int {
	id, _ := utils.GetHostID()
	return id
}
