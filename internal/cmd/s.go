package cmd

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"math"
	"os"
	"strings"
	"time"

	qlog "gitlab.qomolo.com/infrastructure/sw_architecture/middleware_abstraction/utility/qlog_golang"
	"gitlab.qomolo.com/qomolo_utils/qomolo_get/internal/lib"
	"gitlab.qomolo.com/qomolo_utils/qomolo_get/pkg/qpackage/qnexus"
	"gitlab.qomolo.com/qomolo_utils/qomolo_get/pkg/utils"

	"github.com/fatih/color"
	"github.com/jedib0t/go-pretty/v6/table"
	"github.com/jedib0t/go-pretty/v6/text"
	"github.com/spf13/cobra"
	"go.uber.org/zap/buffer"
)

// sCmd represents the s command
var sCmd = &cobra.Command{
	Use:   `s [scheme_name] [version]`,
	Short: `qomolo scheme tools`,
	Long: `manage qomolo scheme tools
example:
 # list last 10 scheme
 qomolo-get s

 # list last all schemes
 qomolo-get s --all

 # list scheme_name last 10 versions
 qomolo-get s [scheme_name]

 # show scheme_name latest version
 qomolo-get s [scheme_name]  --lts

 # show scheme_name all versions
 qomolo-get s [scheme_name]  --all

 # show scheme_name version info
 qomolo-get s [scheme_name] [version]

 # install scheme_name version
 qomolo-get s [scheme_name] [version] -i [--ignore-depends]
 注：--ignore-depends参数，对于apt安装的，会直接按所填的包过滤掉；对于dpkg安装的，会原样传给dpkg作为命令参数

 # show scheme_name local version
 qomolo-get s [scheme_name] --cur [--zip-file] [--format json [--full]]

 # download scheme_name version to zip
 qomolo-get s [scheme_name] [version] --extract-to-zip

 # install scheme_name version to zip
 qomolo-get s [scheme_name] [version] --install-from-zip --zip-file /path/to/zip

 # install scheme_name version to env-path
 qomolo-get s [scheme_name] [version] -i --all-to-env [--env-path 0/1/2] [--ignore-depends pkg1,pkg2,pkg3]

`,
	Args: func(cmd *cobra.Command, args []string) error {
		if len(args) > 2 {
			return fmt.Errorf("accepts at most %d arg(s), received %d", 2, len(args))
		}
		p := parseParams(cmd, args)
		// 当前选项跟其他选项冲突
		if p.current {
			if p.isInstall || p.isRemove || p.schemeName == "" || p.version != "" {
				return errors.New(`
--cur option conflict with other option
correct --cur command: qomolo-get s [scheme_name] --cur
				`)
			}
		}

		if p.isInstall && p.isRemove {
			return errors.New("--install option conflict with --remove option")
		}

		if p.isInstall || p.isRemove {
			if p.schemeName == "" || p.version == "" {
				return errors.New("should provide scheme name and scheme version args")
			}
		}

		return nil
	},
	ValidArgsFunction: sArgsFunction,
	RunE: func(cmd *cobra.Command, args []string) error {
		length := len(args)
		p := parseParams(cmd, args)
		ctx := utils.SetCtxEnvPath(context.Background(), p.envPath)
		ctx = utils.SetCtxImageDomain(ctx, getQPackage().GetImageDomain())
		ctx = utils.SetCtxAllToEnvFlag(ctx, p.allToEnv)
		ctx = utils.SetCtxIgnoreDepends(ctx, p.ignoreDepends)
		ctx = utils.SetCtxIgnorePullImages(ctx, p.ignorePullImages)
		ctx = utils.SetCtxInstallDbgPackage(ctx, p.installDbgPackage)
		if p.current {
			if p.format != "" {
				_, _ = schemeCurrentLocalFormatJSON(ctx, p.schemeName, p.format, p.full, 0)
				return nil
			}
			schemeCurrentLocal(ctx, p.schemeName)
			return nil
		} else if p.isDiff {
			schemeDiff(ctx, p.schemeName)
			return nil
		}
		if length == 0 {
			// list  schemes
			if p.isAll {
				listScheme(ctx, math.MaxInt64)
				// list all schemes
			} else {
				// list last 10 schemes
				listScheme(ctx, 10)
			}
		} else if length == 1 {
			if p.isLts {
				listSchemeVersions(ctx, p.schemeName, 1)
			} else {
				if p.isAll {
					listSchemeVersions(ctx, p.schemeName, math.MaxInt64)
				} else {
					// list scheme_name last 10 versions
					listSchemeVersions(ctx, p.schemeName, 10)
				}
			}
		} else if length == 2 {
			if p.isDryRun {
				return installSchemeVersionDryRun(ctx, p.schemeName, p.version)
			}
			// show scheme_name version info
			if p.isInstall {
				if p.progress {
					err := installSchemeVersionWithProgress(ctx, p.schemeName, p.version)
					return err
				}
				err := utils.HasRootPrivilege()
				if err != nil {
					sugarLogger.Errorf("%v", err)
					return err
				}

				info := getQPackage().GetLocalSchemeInfo(ctx, p.schemeName)
				err = installSchemeVersion(ctx, p.schemeName, p.version)
				if err != nil {
					_ = writeHistory(installAction, string(qnexus.GroupTypeScheme), p.schemeName, p.version, info.Version, false)
					return err
				}
				_ = writeHistory(installAction, string(qnexus.GroupTypeScheme), p.schemeName, p.version, info.Version, true)
				return nil
			} else if p.isRemove {
				err := removeSchemeVersion(ctx, p.schemeName, p.version)
				if err != nil {
					_ = writeHistory(removeAction, string(qnexus.GroupTypeScheme), p.schemeName, "", p.version, false)
					return err
				}
				_ = writeHistory(removeAction, string(qnexus.GroupTypeScheme), p.schemeName, "", p.version, true)
			} else if p.isExtractToZip {
				err := utils.HasRootPrivilege()
				if err != nil {
					sugarLogger.Errorf("%v", err)
					return err
				}
				zipFile := p.zipFile
				if p.zipFile == "" {
					zipFile = fmt.Sprintf("%s_%s_%s.zip", p.schemeName, p.version, time.Now().Format("20060102150405"))
				}
				if zipFile == "" {
					return fmt.Errorf("need to specific --zipFile")
				}
				q := getQPackage()
				targetDir := fmt.Sprintf("%s/%s/%s", tmpDownloadDir, p.schemeName, p.version)
				err = utils.CleanTempDownloadFiles(targetDir)
				if err != nil {
					return err
				}
				err = q.SchemeDownload(ctx, p.schemeName, p.version, p.arch, targetDir)
				if err != nil {
					cmd.PrintErrln(err)
					return err
				}
				err = utils.PackToZip(zipFile, targetDir)
				if err != nil {
					cmd.PrintErrln(err)
					return err
				}
				_ = utils.CleanTempDownloadFiles(targetDir)
				_, _ = fmt.Fprintf(os.Stdout, "%s downloaded %s %s, has been saved to %s %s", colorGreen, p.schemeName, p.version, zipFile, colorReset)
				fmt.Println() //nolint
			} else if p.isInstallFromZip {
				err := utils.HasRootPrivilege()
				if err != nil {
					sugarLogger.Errorf("%v", err)
					return err
				}
				zipFile := p.zipFile
				if p.zipFile == "" {
					zipFile = fmt.Sprintf("%s_%s.zip", p.schemeName, p.version)
				}
				if zipFile == "" {
					return fmt.Errorf("need to specific --zipFile")
				}
				q := getQPackage()
				info := q.GetLocalSchemeInfo(ctx, p.schemeName)
				err = installSchemeVersionFromZip(ctx, p.schemeName, p.version, zipFile)
				if err != nil {
					_ = writeHistory(installAction, string(qnexus.GroupTypeScheme), p.schemeName, p.version, info.Version, false)
					return err
				}
				_ = writeHistory(installAction, string(qnexus.GroupTypeScheme), p.schemeName, p.version, info.Version, true)
			} else {
				showSchemeVersion(ctx, p.schemeName, p.version)
			}
		} else {
			cmd.PrintErrf("invalid args: %v", args)
		}
		return nil
	},
}

func init() {
	rootCmd.AddCommand(sCmd)

	// Here you will define your flags and configuration settings.

	// Cobra supports Persistent Flags which will work for this command
	// and all subcommands, e.g.:
	// sCmd.PersistentFlags().String("foo", "", "A help for foo")

	// Cobra supports local flags which will only run when this command
	// is called directly, e.g.:
	sCmd.Flags().BoolP("install", "i", false, "install specific version")
	sCmd.Flags().BoolP("remove", "r", false, "remove specific version")
	sCmd.Flags().BoolP("all", "", false, "list all scheme version")
	sCmd.Flags().BoolP("lts", "", false, "show latest scheme version")
	sCmd.Flags().BoolP("cur", "", false, "show local latest scheme version")
	sCmd.Flags().BoolP("install-from-zip", "x", false, "extract and install from zip")
	sCmd.Flags().BoolP("extract-to-zip", "c", false, "download and extract to zip")
	sCmd.Flags().StringP("zip-file", "z", "", "specific zip to install")
	sCmd.Flags().StringP("arch", "", "arm64", "download deb arch")
	sCmd.Flags().StringP("format", "", "", "use with --cur, output format json")
	sCmd.Flags().BoolP("full", "", false, "return full info for format")
	sCmd.Flags().BoolP("dry-run", "", false, "dry run install")
	sCmd.Flags().StringP("env-path", "", "0", "qomolo-service env path, default 0")
	sCmd.Flags().BoolP("diff", "", false, "check local install scheme version diff")
	sCmd.Flags().BoolP("progress", "p", false, "")
	sCmd.Flags().BoolP("all-to-env", "", false, "install all to env-path")
	sCmd.Flags().StringP("ignore-depends", "", "", "ignore dpkg package dependence")
	sCmd.Flags().BoolP("ignore-pull-images", "", false, "ignore pulling docker images during installation")
	sCmd.Flags().BoolP("dbg", "", false, "install dbg package")
}

type sParams struct {
	isInstall         bool
	isDryRun          bool
	isRemove          bool
	isLts             bool
	isAll             bool
	isDefaultSource   bool
	isLocalRepo       bool
	isLocalRepoV2     bool
	current           bool
	source            string
	schemeName        string
	version           string
	isInstallFromZip  bool
	isExtractToZip    bool
	zipFile           string
	arch              string
	format            string
	full              bool
	envPath           string
	isDiff            bool
	progress          bool
	allToEnv          bool
	ignoreDepends     string
	ignorePullImages  bool
	installDbgPackage bool
}

func parseParams(cmd *cobra.Command, args []string) sParams {
	length := len(args)
	isInstall, _ := cmd.Flags().GetBool("install")
	isDryRun, _ := cmd.Flags().GetBool("dry-run")
	isRemove, _ := cmd.Flags().GetBool("remove")
	isLts, _ := cmd.Flags().GetBool("lts")
	isAll, _ := cmd.Flags().GetBool("all")
	source, _ := cmd.Flags().GetString("source")
	current, _ := cmd.Flags().GetBool("cur")
	isDefaultSource, _ := cmd.Flags().GetBool("default-source")
	isLocalRepo, _ := cmd.Flags().GetBool("local-repo")
	isLocalRepoV2, _ := cmd.Flags().GetBool("local-repo-v2")
	isInstallFromZip, _ := cmd.Flags().GetBool("install-from-zip")
	isExtractToZip, _ := cmd.Flags().GetBool("extract-to-zip")
	isDiff, _ := cmd.Flags().GetBool("diff")
	zipFile, _ := cmd.Flags().GetString("zip-file")
	arch, _ := cmd.Flags().GetString("arch")
	format, _ := cmd.Flags().GetString("format")
	full, _ := cmd.Flags().GetBool("full")
	envPath, _ := cmd.Flags().GetString("env-path")
	progress, _ := cmd.Flags().GetBool("progress")
	allToEnv, _ := cmd.Flags().GetBool("all-to-env")
	ignoreDepends, _ := cmd.Flags().GetString("ignore-depends")
	ignorePullImages, _ := cmd.Flags().GetBool("ignore-pull-images")
	installDbgPackage, _ := cmd.Flags().GetBool("dbg")

	schemeName := ""
	version := ""
	if length == 1 {
		schemeName = args[0]
	} else if length == 2 {
		schemeName = args[0]
		version = args[1]
	}
	return sParams{
		isInstall:         isInstall,
		isDryRun:          isDryRun,
		isRemove:          isRemove,
		isLts:             isLts,
		isAll:             isAll,
		source:            source,
		current:           current,
		schemeName:        schemeName,
		version:           version,
		isDefaultSource:   isDefaultSource,
		isLocalRepo:       isLocalRepo,
		isLocalRepoV2:     isLocalRepoV2,
		isInstallFromZip:  isInstallFromZip,
		isExtractToZip:    isExtractToZip,
		zipFile:           zipFile,
		arch:              arch,
		format:            format,
		full:              full,
		envPath:           envPath,
		isDiff:            isDiff,
		progress:          progress,
		allToEnv:          allToEnv,
		ignoreDepends:     ignoreDepends,
		ignorePullImages:  ignorePullImages,
		installDbgPackage: installDbgPackage,
	}
}

func sArgsFunction(_ *cobra.Command, args []string, _ string) ([]string, cobra.ShellCompDirective) {
	var completion []string
	if len(args) == 0 {
		data, err := getQPackage().GetSchemeList(context.Background())
		if err != nil {
			return nil, cobra.ShellCompDirectiveNoFileComp
		}
		for _, scheme := range data {
			completion = append(completion, scheme.Name)
		}
	}
	if len(args) == 1 && !strings.HasPrefix(args[0], "--") {
		data, err := getQPackage().GetSchemeVersionList(context.Background(), args[0])
		if err != nil {
			return nil, cobra.ShellCompDirectiveNoFileComp
		}
		for _, scheme := range data {
			completion = append(completion, scheme.Version)
		}
	}
	return completion, cobra.ShellCompDirectiveNoFileComp
}

func listScheme(ctx context.Context, count int) {
	data, err := getQPackage().GetSchemeList(ctx)
	if err != nil {
		sugarLogger.Infof("%v %v", err, count)
		return
	}
	t := table.NewWriter()
	t.AppendHeader(table.Row{"scheme_name"})
	for _, v := range data {
		t.AppendRow(table.Row{v.Name})
	}
	sugarLogger.Info("scheme list\n", t.Render())
}

func listSchemeVersions(ctx context.Context, name string, count int) {
	data, err := getQPackage().GetSchemeVersionList(ctx, name)
	if err != nil {
		sugarLogger.Errorf("%v", err)
		return
	}
	t := table.NewWriter()
	t.AppendHeader(table.Row{"name", "version"})
	if count > len(data) {
		count = len(data)
	}
	for _, v := range data[0:count] {
		t.AppendRow(table.Row{v.Name, v.Version})
	}
	sugarLogger.Infof("scheme version\n %s", t.Render())
}

func showSchemeVersion(ctx context.Context, name, version string) {
	data, err := getQPackage().GetSchemeVersionInfo(ctx, name, version)
	if err != nil {
		sugarLogger.Errorf("%v", err)
		return
	}
	str := buffer.Buffer{}
	write := func(label, value string) {
		// nolint
		_, _ = str.WriteString(fmt.Sprintf("%s%s\n", text.AlignLeft.Apply(label, 14), text.AlignLeft.Apply(value, 20)))
	}
	write("name   :", data.Name)
	write("version:", data.Version)
	write("type   :", data.Type)
	write("release_note:", data.ReleaseNote)
	write("created:", time.Unix(data.CreateTime, 0).String())
	write("updated:", time.Unix(data.UpdateTime, 0).String())
	t := table.NewWriter()
	t.AppendHeader(table.Row{"module_name", "module_version"})
	for _, v := range data.Modules {
		t.AppendRow(table.Row{v.PkgName, v.Version})
	}
	write("modules:", "")
	_, _ = str.WriteString(t.Render())
	sugarLogger.Infof("scheme version\n%s", str.String())
}

func installSchemeVersion(ctx context.Context, name, version string) (err error) {
	_, err = getQPackage().SchemeInstallAll(ctx, name, version)
	return err
}

func installSchemeVersionDryRun(ctx context.Context, name, version string) (err error) {
	res, err := getQPackage().SchemeInstallAllDryRun(ctx, name, version)
	if err != nil {
		sugarLogger.Errorf("%v", err)
		return err
	}
	if len(res.Modules) == 0 {
		sugarLogger.Infof("scheme no module to install %s=%s", name, version)
		return nil
	}
	t := table.NewWriter()
	t.AppendHeader(table.Row{"module_name", "local", "version", "is_updated", "type"})
	str := buffer.Buffer{}

	for _, v := range res.Modules {
		typ := "apt"
		if !v.AptInstall() || utils.GetCtxAllToEnvFlag(ctx) {
			typ = "dpkg"
		}
		isUpdated := ""
		if v.IsUpdated() {
			isUpdated = "true"
		}
		t.AppendRow(table.Row{v.PkgName, v.LocalVersion, v.RequiredVersion, isUpdated, typ})
	}

	_, _ = str.WriteString(t.Render())
	sugarLogger.Infof("scheme version %s=%s \n%s", res.Name, res.RequiredVersion, str.String())
	if res.ShouldReboot() {
		sugarLogger.Infof("reboot required")
	}
	if res.ShouldMPRestart() {
		sugarLogger.Infof("mp restart required")
	}
	if res.ShouldGwReload() {
		sugarLogger.Infof("gateway reload required")
	}
	return nil
}

func removeSchemeVersion(ctx context.Context, name, version string) (err error) {
	err = utils.HasRootPrivilege()
	if err != nil {
		sugarLogger.Errorf("%v", err)
		return err
	}
	return getQPackage().SchemeRemoveAll(ctx, name, version)
}

func schemeCurrentLocal(ctx context.Context, scheme string) {
	local := getQPackage().GetLocalSchemeInfo(ctx, scheme)
	if local.Version == "" {
		sugarLogger.Infof("%s local version not found", scheme)
		return
	}

	info, err := getQPackage().GetSchemeVersionInfo(ctx, scheme, local.Version)
	if err != nil {
		sugarLogger.Errorf("%v", err)
		return
	}

	diff, err := getQPackage().SchemeInstallAllDryRun(ctx, scheme, local.Version)
	if err != nil {
		sugarLogger.Errorf("%s", err)
		return
	}

	t := table.NewWriter()
	t.AppendHeader(table.Row{"module_name", "module_version", "local_version", "diff"})
	str := buffer.Buffer{}
	write := func(label, value string) {
		// nolint
		_, _ = str.WriteString(fmt.Sprintf("%s%s\n", text.AlignLeft.Apply(label, 14), text.AlignLeft.Apply(value, 20)))
	}
	diffModule := make([]string, 0)
	for _, v := range diff.Modules {
		diffStr := ""
		if v.IsDiff {
			diffStr = color.RedString(fmt.Sprintf("%+v", v.IsDiff))
		}
		t.AppendRow(table.Row{v.PkgName, v.RequiredVersion, v.LocalVersion, diffStr})
		if v.IsDiff {
			diffModule = append(diffModule, v.PkgName)
		}
	}
	write("name   :", scheme)
	write("version:", local.Version)
	write("type   :", info.Type)
	write("created:", time.Unix(info.CreateTime, 0).String())
	write("updated:", time.Unix(info.UpdateTime, 0).String())

	write("modules:", "")
	_, _ = str.WriteString(t.Render())
	t.AppendHeader(table.Row{"module_name", "remote_version", "local_version", "diff"})
	sugarLogger.Infof("current scheme info\n%s", str.String())
	if len(diffModule) > 0 {
		sugarLogger.Error(color.RedString("scheme %s %s has diff\n", scheme, local.Version))
	}
}

func installSchemeVersionFromZip(ctx context.Context, name, version, file string) error {
	err2 := utils.HasRootPrivilege()
	if err2 != nil {
		sugarLogger.Errorf("%v", err2)
		return err2
	}
	return getQPackage().SchemeInstallFromZip(ctx, name, version, file)
}

func schemeCurrentLocalFormatJSON(ctx context.Context, scheme, format string, full bool, level int) (map[string]interface{}, error) {
	return getQPackage().SchemeInfo(ctx, scheme, format, full, level)
}

// nolint
// 0: 正常
// 1: warning，表示获取repo中的版本详情失败
// 2: 错误，表示校验失败
func schemeDiff(ctx context.Context, scheme string) {
	local := getQPackage().GetLocalSchemeInfo(ctx, scheme)
	if local.Version == "" {
		fmt.Println("本地未安装该 scheme")
		os.Exit(lib.ExitWarning)
		return
	}
	var diffModuleStr bytes.Buffer
	diff, err := getQPackage().SchemeInstallAllDryRun(ctx, scheme, local.Version)
	if err != nil {
		os.Exit(lib.ExitWarning)
		return
	}
	if !diff.HasDiff {
		return
	}
	var diffModule [][2]string

	for _, m := range diff.Modules {
		if !m.IsDiff {
			continue
		}
		if m.LocalVersion == "" {
			diffModuleStr.WriteString(fmt.Sprintf("%s=%s not install\n", m.PkgName, m.RequiredVersion))
			diffModule = append(diffModule, [2]string{m.PkgName, m.RequiredVersion})
		} else {
			diffModuleStr.WriteString(fmt.Sprintf("%s was changed，expected: %s actual: %s\n", m.PkgName, m.RequiredVersion, m.LocalVersion))
		}
	}
	if diffModuleStr.String() == "" {
		os.Exit(0)
		return
	}
	color.Magenta(diffModuleStr.String())
	fixCmdStr := "sudo qomolo-get apt "
	for i := range diffModule {
		fixCmdStr += fmt.Sprintf("%s=%s ", diffModule[i][0], diffModule[i][1])
	}
	fixCmdStr += "--install"
	fmt.Println("")

	fmt.Println("can use this cmd fix the error")
	color.Red(fixCmdStr)
	fmt.Println("")
	fmt.Println("or use this cmd reinstall the version")
	color.Red("sudo qomolo-get s %s %s --install\n", scheme, local.Version)
	os.Exit(lib.ExitError)
}

func installSchemeVersionWithProgress(ctx context.Context, name, version string) (err error) {
	err = utils.HasRootPrivilege()
	if err != nil {
		qlog.Errorf("%v", err)
		return err
	}
	qp := getQPackage()
	ticker := time.NewTicker(2 * time.Second)
	defer ticker.Stop()
	go func() {
		for range ticker.C {
			percent := qp.Downloader.GetPercent()
			imagePercent := qp.Downloader.GetImagePercent()
			fmt.Printf("scheme %s %s download percent %.2f %%\n", name, version, percent)
			imageFinish := 0
			for i, p := range imagePercent {
				fmt.Printf("image %s download percent %.2f %%\n", i, p)
				if p == 100 {
					imageFinish++
				}
			}
			if percent == 100 && len(imagePercent) == imageFinish {
				return
			}
			ticker.Reset(2 * time.Second)
		}
	}()
	err = qp.DownloadSchemeWithProgress(ctx, name, version, "")
	if err != nil {
		return err
	}
	// sleep for log write
	time.Sleep(2 * time.Second)
	_, err = getQPackage().SchemeInstallAll(ctx, name, version)
	return err
}
