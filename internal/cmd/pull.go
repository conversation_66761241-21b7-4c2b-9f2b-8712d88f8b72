/*
Copyright © 2022 NAME HERE <EMAIL ADDRESS>
*/
package cmd

import (
	"errors"
	"fmt"
	"io"
	"net/http"
	"os"
	"path"
	"path/filepath"
	"strings"
	"time"

	"gitlab.qomolo.com/qomolo_utils/qomolo_get/internal/lib"

	"github.com/pkg/sftp"
	"github.com/schollz/progressbar/v3"
	"github.com/spf13/cobra"
	"github.com/spf13/viper"
)

// pullCmd represents the pull command
var pullCmd = &cobra.Command{
	Use: `pull repo raw [subpath] [file1 file2 ...]
	      pull nas [file1|dir1(full path) file2|dir2(full path) ...]`,
	Short: "download file from repo raw or nas to current dir",
	Long: `download file from repo.qomolo.com raw or nas to current dir
example:
	qomolo-get pull repo raw openvpn/CN CN_XM.conf CN_XM_1.conf
	qomolo-get pull nas /data/qpilot_log/igv/2022/8/7/hr6-105 /data/qpilot_log/igv/2022/8/10/hr5-105/hr5-105_2022-08-10-1714_log_bag.tar.gz`,
	Run: func(cmd *cobra.Command, args []string) {
		if args[0] == "repo" && args[1] == "raw" {
			PullRepo(args[2:])
		}

		if args[0] == "nas" {
			PullNas(args[1:])
		}

		if args[0] != "repo" && args[0] != "nas" {
			_ = cmd.Usage()
			os.Exit(1)
		}
	},
}

func init() {
	//rootCmd.AddCommand(pullCmd)

	// Here you will define your flags and configuration settings.

	// Cobra supports Persistent Flags which will work for this command
	// and all subcommands, e.g.:
	// pullCmd.PersistentFlags().String("foo", "", "A help for foo")

	// Cobra supports local flags which will only run when this command
	// is called directly, e.g.:
	pullCmd.Flags().StringP("nasUser", "", "", "username")
	pullCmd.Flags().StringP("nasPass", "", "", "password")

	_ = viper.BindPFlag("nasUser", pullCmd.Flags().Lookup("nasUser"))
	_ = viper.BindPFlag("nasPass", pullCmd.Flags().Lookup("nasPass"))
}

func PullRepo(args []string) {

	repoURL := "https://repo.qomolo.com/repository/raw/"
	dirPath := args[0]
	for _, v := range args[1:] {
		fileURL := repoURL + dirPath + "/" + v
		fileName := filepath.Base(fileURL)

		sugarLogger.Info("Try downloading " + fileURL)

		err := DownloadFile(fileName, fileURL)
		if err != nil {
			sugarLogger.Error(err)
			return
		}
		sugarLogger.Info("Finished: " + fileName)
	}
}

func PullNas(args []string) {

	nasUser := viper.GetString("nasUser")
	nasPass := viper.GetString("nasPass")

	if nasUser == "" || nasPass == "" {
		sugarLogger.Error("Must set nasUser and nasPass")
		os.Exit(1)
	}

	if len(args) < 1 {
		sugarLogger.Error("remote file path not given")
		os.Exit(1)
	}

	// nolint
	sftpClient, err := lib.SftpConnect(nasUser, nasPass, "nas-1.qomolo.com", 22)
	// sftpClient, err := lib.SftpConnect(nasUser, nasPass, "127.0.0.1", 22)
	if err != nil {
		sugarLogger.Fatal(err)
	}
	defer func() {
		_ = sftpClient.Close()
	}()

	// remoteFilePath := "/data/qpilot_log/igv/2022/8/10/hr5-105/hr5-105_2022-08-10-1714_log_bag.tar.gz"

	sugarLogger.Info("Download List: " + strings.Join(args, " "))

	for _, remoteFilePath := range args {

		checkFile, err := sftpClient.Lstat(remoteFilePath)
		if err != nil {
			sugarLogger.Fatal(err)
		}
		if checkFile.IsDir() {
			sp := path.Dir(remoteFilePath)
			err = WalkDirsAndDownload(remoteFilePath, sftpClient, sp)

			if err != nil {
				sugarLogger.Fatal(err)
			}
		} else {

			srcFile, err := sftpClient.Open(remoteFilePath)
			if err != nil {
				sugarLogger.Fatal(err)
			}
			defer func(srcFile *sftp.File) {
				_ = srcFile.Close()
			}(srcFile)

			fileName := filepath.Base(remoteFilePath)

			sugarLogger.Info("Start downloading: " + fileName)
			out, err := os.Create(fileName)
			if err != nil {
				sugarLogger.Fatal(err)
			}

			finfo, err := srcFile.Stat()

			if err != nil {
				_ = out.Close()
				sugarLogger.Fatal(err)
			}

			bar := Bar(finfo.Size(), "Downloading... ")

			_, err = srcFile.WriteTo(io.MultiWriter(out, bar))

			if err != nil {
				_ = out.Close()
				sugarLogger.Fatal(err)
			}
			sugarLogger.Info("Finished: " + fileName)
			_ = out.Close()
		}
	}

}

func WalkDirsAndDownload(srcDir string, sftpClient *sftp.Client, sp string) error {
	files, err := sftpClient.ReadDir(srcDir)
	if err != nil {
		return err
	}

	_ = os.Mkdir(strings.TrimPrefix(srcDir, sp+"/"), 0755)

	for _, v := range files {
		if v.IsDir() {
			tmpdir := srcDir + "/" + v.Name()
			localDir := strings.TrimPrefix(tmpdir, sp+"/")
			_ = os.Mkdir(localDir, 0755)
			err = WalkDirsAndDownload(tmpdir, sftpClient, sp)
			if err != nil {
				sugarLogger.Fatal(err)
			}
		} else {
			srcFile := srcDir + "/" + v.Name()
			localFile := strings.TrimPrefix(srcDir+"/"+v.Name(), sp+"/")
			sftpFile, _ := sftpClient.Open(srcFile)
			sugarLogger.Info("Start downloading: " + localFile)
			out, err := os.Create(localFile)
			if err != nil {
				_ = out.Close()
				sugarLogger.Fatal(err)
			}
			bar := Bar(v.Size(), "Downloading... ")
			_, err = sftpFile.WriteTo(io.MultiWriter(out, bar))
			if err != nil {
				_ = out.Close()
				sugarLogger.Fatal(err)
			}
			sugarLogger.Info("Finished: " + v.Name())
			_ = out.Close()
		}
	}
	return nil

}

func DownloadFile(filepath string, url string) error {

	// Get the data
	resp, err := http.Get(url)
	if err != nil {
		return err
	}
	defer func() {
		_ = resp.Body.Close()
	}()

	// Create the file
	if resp.StatusCode == http.StatusOK {
		out, err := os.Create(filepath)
		if err != nil {
			return err
		}
		defer func(out *os.File) {
			_ = out.Close()
		}(out)

		// bar := progressbar.DefaultBytes(resp.ContentLength, "Downloading... ")
		bar := Bar(resp.ContentLength, "Downloading... ")

		// Write the body to file
		_, err = io.Copy(io.MultiWriter(out, bar), resp.Body)
		return err
	}

	if resp.StatusCode == http.StatusNotFound {
		return errors.New("404, file not found")
	}

	return nil
}

func Bar(maxBytes int64, description ...string) *progressbar.ProgressBar {
	return progressbar.NewOptions64(
		maxBytes,
		progressbar.OptionSetDescription(description[0]),
		progressbar.OptionSetWriter(os.Stderr),
		progressbar.OptionShowBytes(true),
		progressbar.OptionSetWidth(10),
		progressbar.OptionThrottle(65*time.Millisecond),
		progressbar.OptionShowCount(),
		progressbar.OptionOnCompletion(func() {
			fmt.Fprint(os.Stderr, "\n")
		}),
		// nolint
		progressbar.OptionSpinnerType(14),
		progressbar.OptionFullWidth(),
		progressbar.OptionSetRenderBlankState(true),
		progressbar.OptionUseANSICodes(true),
	)
}
