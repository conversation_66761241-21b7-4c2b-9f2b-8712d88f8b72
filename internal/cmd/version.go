/*
Copyright © 2022 NAME HERE <EMAIL ADDRESS>
*/
package cmd

import (
	"fmt"
	"runtime"
	"runtime/debug"

	"github.com/spf13/cobra"

	"gitlab.qomolo.com/qomolo_utils/qomolo_get/internal/lib"
)

// versionCmd represents the version command
var versionCmd = &cobra.Command{
	Use:   "version",
	Short: "version",
	Long:  `display version`,
	Run: func(_ *cobra.Command, _ []string) {

		var commit, commitTime = func() (c string, cT string) {
			if info, ok := debug.ReadBuildInfo(); ok {
				for _, setting := range info.Settings {
					if setting.Key == "vcs.revision" {
						c = setting.Value
					}
					if setting.Key == "vcs.time" {
						cT = setting.Value
					}
				}
			}
			return c, cT
		}()

		fmt.Println("Version: " + lib.Version)         // nolint
		fmt.Println("Commit: " + commit)               // nolint
		fmt.Println("CommitTime: " + commitTime)       // nolint
		fmt.Println("GoVersion: " + runtime.Version()) // nolint
	},
}

func init() {
	rootCmd.AddCommand(versionCmd)
}
