/*
Copyright © 2024 NAME HERE <EMAIL ADDRESS>
*/
package cmd

import (
	"context"
	"fmt"
	"os"
	"time"

	"github.com/pborman/uuid"
	"github.com/spf13/cobra"
	"gitlab.qomolo.com/qomolo_utils/qomolo_get/internal/lib"
	"gitlab.qomolo.com/qomolo_utils/qomolo_get/pkg/qpilot"
	"gitlab.qomolo.com/qomolo_utils/qomolo_get/pkg/utils"
)

// qpilotResourceCmd represents the resource command
var qpilotResourceCmd = &cobra.Command{
	Use:   "resource",
	Short: "qpilot resource manage",
	Long: `

 # download the qpilot resources using the qpilot profile
`,
	RunE: func(cmd *cobra.Command, _ []string) (err error) {
		isDownload, _ := cmd.Flags().GetBool("download")
		if isDownload {
			resourceDownload()
			return nil
		}
		isCheck, _ := cmd.Flags().GetBool("check")
		if isCheck {
			mapCheck()
			return nil
		}
		return resourceInfo()
	},
}

func init() {
	qpilotResourceCmd.Flags().BoolP("download", "d", false, "download qpilot resource")
	qpilotResourceCmd.Flags().BoolP("check", "c", false, "check qpilot resource")
	qpilotCmd.AddCommand(qpilotResourceCmd)
}

// 提供给 qpilot-setup 做 qpilot 启动资源下载
// 0: 表示成功（不管是没有资源要下载，还是有资源下载成功）
// 1: warning 请求服务器获取资源详情失败
// 2: error 表示有资源要下载，但下载失败
func resourceDownload() {
	res, err := qpilot.GetQpilotMap()
	if err != nil {
		sugarLogger.Errorf("获取地图资源信息失败 %s", err)
		os.Exit(lib.ExitError)
		return
	}
	ctx := context.Background()

	if res == nil {
		sugarLogger.Infof("地图参数配置为空，跳过下载")
		return
	}
	qp := getQPackage()
	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()
	go func() {
		for range ticker.C {
			fmt.Printf("\rdownload percent %.2f%%", qp.ModuleDownloader.GetPercent())
			if qp.ModuleDownloader.GetPercent() == 100 {
				fmt.Println()
				return
			}
			ticker.Reset(1 * time.Second)
		}
	}()

	// OsmMap
	_, err = qp.ModuleVersionInfo(ctx, res.OsmMap.ModuleName, res.OsmMap.Version)
	if err != nil {
		sugarLogger.Errorf("osm地图 %s %s 不存在, err: %v", res.OsmMap.ModuleName, res.OsmMap.Version, err)
		return
	}
	err = qp.DownloadModuleWithProgress(ctx, res.OsmMap.ModuleName, res.OsmMap.Version, uuid.New())
	if err != nil {
		sugarLogger.Errorf("osm地图 %s %s 下载错误, err: %v", res.OsmMap.ModuleName, res.OsmMap.Version, err)
		return
	}

	// PcdMap
	_, err1 := qp.ModuleVersionInfo(ctx, res.PcdMap.ModuleName, res.PcdMap.Version)
	if err1 != nil {
		sugarLogger.Errorf("pcd地图 %s %s 不存在, err: %v", res.PcdMap.ModuleName, res.PcdMap.Version, err)
		return
	}

	err1 = qp.DownloadModuleWithProgress(ctx, res.PcdMap.ModuleName, res.PcdMap.Version, uuid.New())
	if err1 != nil {
		sugarLogger.Errorf("pcd地图 %s %s 下载错误, err: %v", res.PcdMap.ModuleName, res.PcdMap.Version, err)
		return
	}
	sugarLogger.Info("地图资源下载成功")
}

func resourceInfo() error {
	res, err := qpilot.GetQpilotMap()
	if err != nil {
		sugarLogger.Errorf("获取地图资源信息失败 %s", err)
		return err
	}
	if res == nil {
		sugarLogger.Infof("地图参数配置为空，跳过下载")
		return nil
	}
	sugarLogger.Infof("获取地图资源信息成功 %s", res)
	return nil
}

func mapCheck() {
	res, err := qpilot.GetQpilotMap()
	if err != nil {
		sugarLogger.Errorf("获取地图资源信息失败 %s", err)
	}
	ok, err := utils.CheckAllFileHash(utils.ResourceDir(res.OsmMap.ModuleName, res.OsmMap.Version))
	if err != nil {
		sugarLogger.Errorf("osm地图 %s %s 校验失败, err: %v", res.OsmMap.ModuleName, res.OsmMap.Version, err)
		return
	}
	ok1, err := utils.CheckAllFileHash(utils.ResourceDir(res.PcdMap.ModuleName, res.PcdMap.Version))
	if err != nil {
		sugarLogger.Errorf("pcd地图 %s %s 校验失败, err: %v", res.PcdMap.ModuleName, res.PcdMap.Version, err)
		return
	}
	if !ok || !ok1 {
		sugarLogger.Warnf("地图资源校验不通过")
	} else {
		sugarLogger.Info("地图资源校验通过")
	}
}
