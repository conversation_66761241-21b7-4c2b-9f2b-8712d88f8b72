package cmd

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"reflect"
	"time"

	"github.com/jedib0t/go-pretty/v6/table"
	"github.com/spf13/cobra"
	qlog "gitlab.qomolo.com/infrastructure/sw_architecture/middleware_abstraction/utility/qlog_golang"
	"gitlab.qomolo.com/qomolo_utils/qomolo_get/pkg/qpackage"
	"gitlab.qomolo.com/qomolo_utils/qomolo_get/pkg/qpackage/devops"
	"gitlab.qomolo.com/qomolo_utils/qomolo_get/pkg/qpackage/qnexus"
	"gitlab.qomolo.com/qomolo_utils/qomolo_get/pkg/utils"
	"go.uber.org/zap/buffer"
)

const (
	DefaultPageNum  = 1
	DefaultPageSize = 1000
)

// sCmd represents the s command
var mCmd = &cobra.Command{
	Use:   `m [module_name] [version]`,
	Short: "qomolo module tools",
	Long: `manage qomolo module tools
example:
 # list module
 qomolo-get m
 # list module version
 qomolo-get m [module_name]
 # list module version info
 qomolo-get m [module_name] [version]
 # download module
 qomolo-get m [module_name] [version] -d
`,
	Args: func(_ *cobra.Command, args []string) error {
		if len(args) > 2 {
			return fmt.Errorf("accepts at most %d arg(s), received %d", 2, len(args))
		}
		return nil
	},
	ValidArgsFunction: mArgsFunction,
	RunE: func(cmd *cobra.Command, args []string) error {
		length := len(args)
		isAll, _ := cmd.Flags().GetBool("all")
		isDownload, _ := cmd.Flags().GetBool("download")
		isInstall, _ := cmd.Flags().GetBool("install")
		isDev, _ := cmd.Flags().GetBool("dev")
		count, _ := cmd.Flags().GetInt("count")
		arch, _ := cmd.Flags().GetString("arch")
		format, _ := cmd.Flags().GetString("format")
		installDbgPackage, _ := cmd.Flags().GetBool("dbg")
		ctx := context.Background()
		ctx = utils.SetCtxInstallDbgPackage(ctx, installDbgPackage)

		if isDev {
			if length == 0 {
				listDevModules(ctx, count)
				return nil
			} else if length == 1 {
				listDevModuleVersions(ctx, args[0], arch, count)
				return nil
			} else if length == 2 {
				if isInstall {
					installDevModuleVersion(ctx, args[0], args[1], arch)
					return nil
				}
				showDevModuleVersionInfo(ctx, args[0], args[1], arch, format)
				return nil
			}
		}

		if length == 0 {
			listModule(ctx)
			return nil
		} else if length == 1 {
			// list  schemes
			if isAll {
				listModuleVersions(ctx, args[0], math.MaxInt64)
				// list all schemes
			} else {
				// list last 10 schemes
				listModuleVersions(ctx, args[0], 10)
			}
			return nil
		} else if length == 2 {
			if isDownload {
				err := downloadModule(ctx, args[0], args[1])
				if err != nil {
					sugarLogger.Errorf("%v", err)
					return err
				}
				return nil
			}
			showModuleVersion(ctx, args[0], args[1])
			return nil
		}
		listModule(ctx)
		return nil
	},
}

func mArgsFunction(cmd *cobra.Command, args []string, _ string) ([]string, cobra.ShellCompDirective) {
	var completion []string
	isDev, _ := cmd.Flags().GetBool("dev")
	arch, _ := cmd.Flags().GetString("arch")
	if len(args) == 0 {
		if isDev {
			// nolint
			req := devops.ModuleListReq{PageNum: 1, PageSize: 1000}
			data, err := getQPackage().DevopsClient.ModuleList(req)
			if err != nil {
				return nil, cobra.ShellCompDirectiveNoFileComp
			}
			for _, module := range data {
				completion = append(completion, module.PkgName)
			}
		} else {
			data, err := getQPackage().ModuleList(context.Background())
			if err != nil {
				return nil, cobra.ShellCompDirectiveNoFileComp
			}
			for _, module := range data {
				completion = append(completion, module.Name)
			}
		}
	}
	if len(args) == 1 {
		if isDev {
			req := devops.ModuleVersionListReq{Arch: arch, RepoName: devops.RepoDev}
			if arch == "" {
				localArch, err := utils.GetArch()
				if err != nil {
					sugarLogger.Error(err)
				}
				req.Arch = localArch
			}
			if len(args[0]) > 0 {
				req.PkgName = args[0]
			}
			data, err := getQPackage().DevopsClient.ModuleVersionList(req)
			if err != nil {
				return nil, cobra.ShellCompDirectiveNoFileComp
			}
			for _, module := range data {
				completion = append(completion, module.Version)
			}
		} else {
			data, err := getQPackage().ModuleVersionList(context.Background(), args[0])
			if err != nil {
				return nil, cobra.ShellCompDirectiveNoFileComp
			}
			for _, md := range data {
				completion = append(completion, md.Version)
			}
		}
	}
	return completion, cobra.ShellCompDirectiveNoFileComp
}

func init() {
	rootCmd.AddCommand(mCmd)
	mCmd.Flags().BoolP("all", "", false, "list all")
	mCmd.Flags().BoolP("download", "d", false, "download module")
	mCmd.Flags().BoolP("dev", "", false, "dev repo")
	mCmd.Flags().BoolP("install", "i", false, "[flag with dev]install module")
	mCmd.Flags().IntP("count", "c", 0, "[flag with dev]list limit")
	mCmd.Flags().StringP("arch", "", "", "[flag with dev]package arch")
	mCmd.Flags().StringP("format", "f", "", "[flag with dev]format")
	mCmd.Flags().BoolP("dbg", "", false, "[flag with dev]install dbg package")
}

func listModule(ctx context.Context) {
	data, err := getQPackage().ModuleList(ctx)
	if err != nil {
		return
	}
	t := table.NewWriter()
	t.AppendHeader(table.Row{"module_name"})
	for _, v := range data {
		t.AppendRow(table.Row{v.Name})
	}
	sugarLogger.Info("module list\n", t.Render())
}

func listModuleVersions(ctx context.Context, name string, count int) {
	data, err := getQPackage().ModuleVersionList(ctx, name)
	if err != nil {
		sugarLogger.Errorf("%v", err)
		return
	}
	t := table.NewWriter()
	t.AppendHeader(table.Row{"name", "version"})
	if count > len(data) {
		count = len(data)
	}
	for _, v := range data[0:count] {
		t.AppendRow(table.Row{v.Name, v.Version})
	}
	sugarLogger.Infof("module version\n %s", t.Render())
}

func showModuleVersion(ctx context.Context, name, version string) {
	data, err := getQPackage().ModuleVersionInfo(ctx, name, version)
	if err != nil {
		sugarLogger.Errorf("%v", err)
		return
	}
	str := buffer.Buffer{}
	t := table.NewWriter()
	t.AppendHeader(table.Row{"name", "type"})
	for _, v := range data.Folders {
		t.AppendRow(table.Row{v.Path, "folder"})
	}
	for _, v := range data.Files {
		t.AppendRow(table.Row{v.Path, "file"})
	}
	_, _ = str.WriteString(t.Render())
	sugarLogger.Infof("module version\n%s", str.String())
}

func downloadModule(ctx context.Context, name, version string) (err error) {
	p1 := &qnexus.ModuleDownloadProgress{}
	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()
	go func() {
		for range ticker.C {
			fmt.Printf("%s=%s download percent: %v\n", p1.Name, p1.Version, p1.GetPercent())
			if p1.GetPercent() == 100 {
				fmt.Println("download success")
				return
			}
		}
	}()
	err = getQPackage().ModuleDownload(ctx, name, version, utils.ModuleDownloadLocalPath, p1)
	if err != nil {
		return err
	}
	return nil
}

func listDevModules(_ context.Context, count int) {
	req := devops.ModuleListReq{}
	if count > 0 {
		req.PageNum = 1
		req.PageSize = count
	}
	data, err := getQPackage().DevopsClient.ModuleList(req)
	if err != nil {
		sugarLogger.Info(err)
		return
	}
	t := table.NewWriter()
	t.AppendHeader(table.Row{"module_name"})
	for _, v := range data {
		t.AppendRow(table.Row{v.Name})
	}
	sugarLogger.Info("module list\n", t.Render())
}

func listDevModuleVersions(_ context.Context, name, arch string, count int) {
	req := devops.ModuleVersionListReq{
		PkgName:  name,
		RepoName: devops.RepoDev,
	}
	if count > 0 {
		req.PageNum = 1
		req.PageSize = count
	}
	req.Arch = arch
	if arch == "" {
		localArch, err := utils.GetArch()
		if err != nil {
			sugarLogger.Error(err)
		}
		req.Arch = localArch
	}

	data, err := getQPackage().DevopsClient.ModuleVersionList(req)
	if err != nil {
		sugarLogger.Info(err)
		return
	}
	t := table.NewWriter()
	t.AppendHeader(table.Row{"pkg_name", "version", "arch", "repo"})
	for _, v := range data {
		t.AppendRow(table.Row{v.PkgName, v.Version, v.Arch, v.RepoName})
	}
	sugarLogger.Info("dev package version list\n", t.Render())
}

func installDevModuleVersion(ctx context.Context, name, version, arch string) {
	detailInfo, err := devModuleVersionInfo(ctx, name, version, arch)
	if err != nil {
		sugarLogger.Info(err)
		return
	}
	sugarLogger.Infof("[to install] %s %s", detailInfo.PkgName, detailInfo.Version)
	pkgs := make([]qpackage.PkgInfo, 0)
	for _, dep := range detailInfo.Modules {
		sugarLogger.Infof("[dependence] %s %s", dep.PkgName, dep.Version)
		pkgs = append(pkgs, qpackage.PkgInfo{
			PkgName:         dep.PkgName,
			RequiredVersion: dep.Version,
			Latest:          false,
			Arch:            qnexus.ArchType(dep.Arch),
			RepoName:        dep.RepoName,
		})
	}
	pkgs = append(pkgs, qpackage.PkgInfo{
		PkgName:         detailInfo.PkgName,
		RequiredVersion: detailInfo.Version,
		Latest:          false,
		Arch:            qnexus.ArchType(detailInfo.Arch),
		RepoName:        detailInfo.RepoName,
	})
	pkgsCopy := pkgs
	if utils.GetCtxInstallDbgPackage(ctx) {
		req := devops.ModuleVersionListReq{
			PkgName:  "-dbg",
			RepoName: "dev",
			PageNum:  DefaultPageNum,
			PageSize: DefaultPageSize,
		}
		dbgModules, err := getQPackage().DevopsClient.ModuleVersionList(req)
		if err != nil {
			qlog.Warnf("Failed to get all -dbg module version list: %v", err)
		} else {
			dbgModuleMap := make(map[string]struct{})
			for _, mod := range dbgModules {
				dbgModuleMap[mod.PkgName] = struct{}{}
			}
			for _, pkg := range pkgsCopy {
				dbgPkgName := pkg.PkgName + "-dbg"
				if _, ok := dbgModuleMap[dbgPkgName]; ok {
					sugarLogger.Infof("[dbg] %s %s", dbgPkgName, pkg.RequiredVersion)
					pkgs = append(pkgs, qpackage.PkgInfo{
						PkgName:         dbgPkgName,
						RequiredVersion: pkg.RequiredVersion,
						Latest:          false,
						Arch:            pkg.Arch,
						RepoName:        "dev",
					})
				}
			}
		}
	}
	err = getQPackage().AptInstallAll(ctx, pkgs)
	if err != nil {
		sugarLogger.Info(err)
		return
	}
}

func devModuleVersionInfo(_ context.Context, name, version, arch string) (devops.ModuleVersion, error) {
	req := devops.ModuleVersionListReq{
		PkgName:  name,
		Version:  version,
		RepoName: devops.RepoDev,
	}
	req.Arch = arch
	if arch == "" {
		localArch, err := utils.GetArch()
		if err != nil {
			sugarLogger.Error(err)
		}
		req.Arch = localArch
	}
	if arch == "" {
		localArch, err := utils.GetArch()
		if err != nil {
			sugarLogger.Error(err)
		}
		req.Arch = localArch
	}
	infos, err := getQPackage().DevopsClient.ModuleVersionList(req)
	if err != nil {
		return devops.ModuleVersion{}, err
	}
	detailInfo, err := getQPackage().DevopsClient.ModuleVersionInfo(int64(infos[0].Id))
	if err != nil {
		return devops.ModuleVersion{}, err
	}
	return detailInfo, nil
}

func showDevModuleVersionInfo(ctx context.Context, name, version, arch, format string) {
	detailInfo, err := devModuleVersionInfo(ctx, name, version, arch)
	if err != nil {
		sugarLogger.Info(err)
		return
	}
	b, err := json.Marshal(detailInfo)
	if err != nil {
		sugarLogger.Info(err)
		return
	}
	if format == "json" {
		fmt.Println(string(b))
		return
	}
	v := reflect.ValueOf(detailInfo)
	t := v.Type()

	for i := 0; i < v.NumField(); i++ {
		field := t.Field(i)
		value := v.Field(i)
		if field.Name == "Modules" {
			continue
		}
		fmt.Printf("%v: %v\n", field.Name, value.Interface())
	}
}
