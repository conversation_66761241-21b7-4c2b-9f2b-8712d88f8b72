package cmd

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"math"
	"os"
	"strings"
	"time"

	"gitlab.qomolo.com/qomolo_utils/qomolo_get/internal/lib"

	"gitlab.qomolo.com/qomolo_utils/qomolo_get/pkg/qpackage"
	"gitlab.qomolo.com/qomolo_utils/qomolo_get/pkg/utils"

	"gitlab.qomolo.com/qomolo_utils/qomolo_get/pkg/qpackage/qnexus"

	"github.com/fatih/color"
	"github.com/jedib0t/go-pretty/v6/table"
	"github.com/jedib0t/go-pretty/v6/text"
	"github.com/spf13/cobra"
	"go.uber.org/zap/buffer"
)

const (
	colorReset = "\033[0m"

	colorRed    = "\033[31m"
	colorGreen  = "\033[32m"
	colorYellow = "\033[33m"
	colorBlue   = "\033[34m"
	colorPurple = "\033[35m"
	colorCyan   = "\033[36m"
	colorWhite  = "\033[37m"
)

// sCmd represents the s command
var gCmd = &cobra.Command{
	Use:   `g [group_name] [version]`,
	Short: "qomolo group tools",
	Long: `manage qomolo scheme group tools
example:
 # list last 10 scheme group
 qomolo-get g

 # list last all groups
 qomolo-get g --all
s
 # list group_name last 10 versions
 qomolo-get g [group_name]

 # show group_name latest version
 qomolo-get g [group_name]  --lts

 # show group_name all versions
 qomolo-get g [group_name]  --all

 # show group_name version info
 qomolo-get g [group_name] [version]

 # install group_name version
 qomolo-get g [group_name] [version] -i [--ignore-depends]
 注：--ignore-depends参数，对于apt安装的，会直接按所填的包过滤掉；对于dpkg安装的，会原样传给dpkg作为命令参数

 # install group_name version to env-path
 qomolo-get g [group_name] [version] -i --all-to-env [--env-path 0/1/2] [--ignore-depends pkg1,pkg2,pkg3]

 # show group_name local version
 qomolo-get g [group_name] --cur [--zip-file] [--format json [--full]]

 # diff group_name local module version
 qomolo_get g [group_name] --diff,return code 0:normal 1:warning 2:error

 # download group_name version to zip
 qomolo-get g [group_name] [version] --extract-to-zip [--use-apt-download]

 # install group_name version to zip
 qomolo-get g [group_name] [version] --install-from-zip --zip-file /path/to/zip
`,
	Args: func(cmd *cobra.Command, args []string) error {
		if len(args) > 2 {
			return fmt.Errorf("accepts at most %d arg(s), received %d", 2, len(args))
		}
		p := parseParams(cmd, args)
		// 当前选项跟其他选项冲突
		if p.current {
			if p.isInstall || p.isRemove || p.schemeName == "" || p.version != "" {
				return errors.New(`
--cur option conflict with other option
correct --cur command: qomolo-get g [group_name] --cur
				`)
			}
		}

		if p.isInstall && p.isRemove {
			return errors.New("--install option conflict with --remove option")
		}

		if p.isInstall || p.isRemove {
			if p.schemeName == "" || p.version == "" {
				return errors.New("should provide scheme name and scheme version args")
			}
		}

		return nil
	},
	ValidArgsFunction: gArgsFunction,
	RunE: func(cmd *cobra.Command, args []string) error {
		length := len(args)
		p := parseGroupParams(cmd, args)

		ctx := utils.SetCtxEnvPath(context.Background(), p.envPath)
		ctx = utils.SetCtxImageDomain(ctx, getQPackage().GetImageDomain())
		ctx = utils.SetCtxAllToEnvFlag(ctx, p.allToEnv)
		ctx = utils.SetCtxIgnoreDepends(ctx, p.ignoreDepends)
		ctx = utils.SetCtxIgnorePullImages(ctx, p.ignorePullImages)
		ctx = utils.SetCtxInstallDbgPackage(ctx, p.installDbgPackage)
		if p.current {
			if p.format != "" {
				if p.format == "json" || p.format == "json-pretty" {
					_, err := schemeGroupCurrentLocalFormatJSON(ctx, p.name, p.format, p.full, 0)
					return err
				}
			} else {
				return schemeGroupCurrentLocal(ctx, p.name, 0)
			}
		} else if p.isDiff && p.version == "" {
			schemeGroupDiff(ctx, p.name, "")
			return nil
		}
		if length == 0 {
			// list  schemes
			if p.isAll {
				listSchemeGroup(ctx, math.MaxInt64)
				// list all schemes
			} else {
				// list last 10 schemes
				listSchemeGroup(ctx, 10)
			}
		} else if length == 1 {
			if p.isLts {
				listSchemeGroupsVersions(ctx, p.name, 1)
			} else {
				if p.isAll {
					listSchemeGroupsVersions(ctx, p.name, math.MaxInt64)
				} else {
					// list group_name last 10 versions
					listSchemeGroupsVersions(ctx, p.name, 10)
				}
			}
		} else if length == 2 {
			if p.isDiff {
				schemeGroupDiff(ctx, p.name, p.version)
				return nil
			}
			if p.isDryRun {
				return installGroupVersionDryRun(ctx, p.name, p.version, 0)
			}
			// show group_name version info
			if p.isInstall {
				if p.progress {
					err := installGroupVersionWithProgress(ctx, p.name, p.version)
					return err
				}
				err := utils.HasRootPrivilege()
				if err != nil {
					sugarLogger.Errorf("%v", err)
					return err
				}
				info := getQPackage().GetLocalGroupInfo(ctx, p.name)
				err = installGroupVersion(ctx, p.name, p.version)
				if err != nil {
					_ = writeHistory(installAction, string(qnexus.GroupTypeGroup), p.name, p.version, info.Version, false)
					return err
				}
				_ = writeHistory(installAction, string(qnexus.GroupTypeGroup), p.name, p.version, info.Version, true)
				return nil
			} else if p.isRemove {
				err := removeSchemeGroupVersion(ctx, p.name, p.version)
				if err != nil {
					_ = writeHistory(removeAction, string(qnexus.GroupTypeGroup), p.name, "", p.version, false)
					return err
				}
				_ = writeHistory(removeAction, string(qnexus.GroupTypeGroup), p.name, "", p.version, true)
			} else if p.isExtractToZip {
				err := utils.HasRootPrivilege()
				if err != nil {
					sugarLogger.Errorf("%v", err)
					return err
				}
				zipFile := p.zipFile
				if p.zipFile == "" {
					zipFile = fmt.Sprintf("%s_%s_%s.zip", p.name, p.version, time.Now().Format("20060102150405"))
				}
				if zipFile == "" {
					return fmt.Errorf("need to specific --zipFile")
				}
				targetDir := fmt.Sprintf("%s/%s/%s", tmpDownloadDir, p.name, p.version)
				err = utils.CleanTempDownloadFiles(targetDir)
				if err != nil {
					return err
				}
				err = downloadSchemeGroupVersion(ctx, p.name, p.version, p.arch, targetDir)
				if err != nil {
					cmd.PrintErrln(err)
					return err
				}
				err = utils.PackToZip(zipFile, targetDir)
				if err != nil {
					cmd.PrintErrln(err)
					return err
				}
				_ = utils.CleanTempDownloadFiles(targetDir)
				_, _ = fmt.Fprintf(os.Stdout, "%s downloaded %s %s, has been saved to %s %s", colorGreen, p.name, p.version, zipFile, colorReset)
				fmt.Println()
			} else if p.isInstallFromZip {
				err := utils.HasRootPrivilege()
				if err != nil {
					sugarLogger.Errorf("%v", err)
					return err
				}
				zipFile := p.zipFile
				if p.zipFile == "" {
					zipFile = fmt.Sprintf("%s_%s.zip", p.name, p.version)
				}
				if zipFile == "" {
					return fmt.Errorf("need to specific --zipFile")
				}
				info := getQPackage().GetLocalGroupInfo(ctx, p.name)
				err = installSchemeGroupVersionFromZip(ctx, p.name, p.version, zipFile)
				if err != nil {
					_ = writeHistory(installAction, string(qnexus.GroupTypeGroup), p.name, p.version, info.Version, false)
					return err
				}
				_ = writeHistory(installAction, string(qnexus.GroupTypeGroup), p.name, p.version, info.Version, true)

			} else {
				showSchemeGroupVersion(ctx, p.name, p.version)
			}
		} else {
			cmd.PrintErrf("invalid args: %v", args)
		}
		return nil
	},
}

func init() {
	rootCmd.AddCommand(gCmd)

	// Here you will define your flags and configuration settings.

	// Cobra supports Persistent Flags which will work for this command
	// and all subcommands, e.g.:
	// sCmd.PersistentFlags().String("foo", "", "A help for foo")

	// Cobra supports local flags which will only run when this command
	// is called directly, e.g.:
	gCmd.Flags().BoolP("install", "i", false, "install specific version")
	gCmd.Flags().BoolP("all", "", false, "list all scheme version")
	gCmd.Flags().BoolP("lts", "", false, "show latest scheme version")
	gCmd.Flags().BoolP("cur", "", false, "show local latest scheme version")
	gCmd.Flags().BoolP("diff", "", false, "check local install group version diff")
	gCmd.Flags().BoolP("install-from-zip", "x", false, "extract and install from zip")
	gCmd.Flags().BoolP("extract-to-zip", "c", false, "download and extract to zip")
	gCmd.Flags().StringP("zip-file", "z", "", "specific zip to install")
	gCmd.Flags().StringP("arch", "", "arm64", "download deb arch")
	gCmd.Flags().StringP("format", "", "", "use with --cur, output format")
	gCmd.Flags().BoolP("full", "", false, "return full info for format")
	gCmd.Flags().BoolP("dry-run", "", false, "dry run install")
	gCmd.Flags().StringP("env-path", "", "0", "qomolo-service env path, default 0")
	gCmd.Flags().BoolP("progress", "", false, "")
	gCmd.Flags().BoolP("all-to-env", "", false, "install all to env-path")
	gCmd.Flags().StringP("ignore-depends", "", "", "ignore package dependence")
	gCmd.Flags().BoolP("ignore-pull-images", "", false, "ignore pulling docker images during installation")
	gCmd.Flags().BoolP("dbg", "", false, "install dbg package")
}

type groupParams struct {
	isInstall         bool
	isDryRun          bool
	isRemove          bool
	isLts             bool
	isAll             bool
	isDiff            bool
	isDefaultSource   bool
	isLocalRepo       bool
	isLocalRepoV2     bool
	current           bool
	name              string
	version           string
	isInstallFromZip  bool
	isExtractToZip    bool
	zipFile           string
	arch              string
	format            string
	full              bool
	envPath           string
	progress          bool
	allToEnv          bool
	ignoreDepends     string
	ignorePullImages  bool
	installDbgPackage bool
}

func parseGroupParams(cmd *cobra.Command, args []string) groupParams {
	length := len(args)
	isInstall, _ := cmd.Flags().GetBool("install")
	isDryRun, _ := cmd.Flags().GetBool("dry-run")
	isRemove, _ := cmd.Flags().GetBool("remove")
	isLts, _ := cmd.Flags().GetBool("lts")
	isAll, _ := cmd.Flags().GetBool("all")
	isDiff, _ := cmd.Flags().GetBool("diff")
	current, _ := cmd.Flags().GetBool("cur")
	isDefaultSource, _ := cmd.Flags().GetBool("default-source")
	isLocalRepo, _ := cmd.Flags().GetBool("local-repo")
	isLocalRepoV2, _ := cmd.Flags().GetBool("local-repo-v2")
	isInstallFromZip, _ := cmd.Flags().GetBool("install-from-zip")
	isExtractToZip, _ := cmd.Flags().GetBool("extract-to-zip")
	zipFile, _ := cmd.Flags().GetString("zip-file")
	arch, _ := cmd.Flags().GetString("arch")
	format, _ := cmd.Flags().GetString("format")
	full, _ := cmd.Flags().GetBool("full")
	envPath, _ := cmd.Flags().GetString("env-path")
	progress, _ := cmd.Flags().GetBool("progress")
	allToEnv, _ := cmd.Flags().GetBool("all-to-env")
	ignoreDepends, _ := cmd.Flags().GetString("ignore-depends")
	ignorePullImages, _ := cmd.Flags().GetBool("ignore-pull-images")
	installDbgPackage, _ := cmd.Flags().GetBool("dbg")

	groupName := ""
	version := ""
	if length == 1 {
		groupName = args[0]
	} else if length == 2 {
		groupName = args[0]
		version = args[1]
	}

	if allToEnv && groupName == "welldrive-group" && ignoreDepends == "" {
		ignoreDepends = "qomolo-journal,acl,qomolo-yq,unzip"
	}
	return groupParams{
		isInstall:         isInstall,
		isDryRun:          isDryRun,
		isRemove:          isRemove,
		isLts:             isLts,
		isAll:             isAll,
		isDiff:            isDiff,
		current:           current,
		name:              groupName,
		version:           version,
		isDefaultSource:   isDefaultSource,
		isLocalRepo:       isLocalRepo,
		isLocalRepoV2:     isLocalRepoV2,
		isInstallFromZip:  isInstallFromZip,
		isExtractToZip:    isExtractToZip,
		zipFile:           zipFile,
		arch:              arch,
		format:            format,
		full:              full,
		envPath:           envPath,
		progress:          progress,
		allToEnv:          allToEnv,
		ignoreDepends:     ignoreDepends,
		ignorePullImages:  ignorePullImages,
		installDbgPackage: installDbgPackage,
	}
}

func gArgsFunction(_ *cobra.Command, args []string, _ string) ([]string, cobra.ShellCompDirective) {
	var completion []string
	if len(args) == 0 {
		data, err := getQPackage().GetGroupList(context.Background())
		if err != nil {
			return nil, cobra.ShellCompDirectiveNoFileComp
		}
		for _, group := range data {
			completion = append(completion, group.Name)
		}
	}
	if len(args) == 1 && !strings.HasPrefix(args[0], "--") {
		data, err := getQPackage().GetGroupVersionList(context.Background(), args[0])
		if err != nil {
			return nil, cobra.ShellCompDirectiveNoFileComp
		}
		for _, group := range data {
			completion = append(completion, group.Version)
		}
	}
	return completion, cobra.ShellCompDirectiveNoFileComp
}

func listSchemeGroup(ctx context.Context, count int) {
	data, err := getQPackage().GetGroupList(ctx)
	if err != nil {
		sugarLogger.Infof("%v %v", err, count)
		return
	}
	t := table.NewWriter()
	t.AppendHeader(table.Row{"group_name", "project", "vehicle_type"})
	for _, v := range data {
		project := make([]string, 0)
		for i := range v.Project {
			project = append(project, v.Project[i].Value)
		}
		vehicleType := make([]string, 0)
		for i := range v.VehicleType {
			vehicleType = append(vehicleType, v.VehicleType[i].Value)
		}
		t.AppendRow(table.Row{v.Name, project, vehicleType})
	}
	sugarLogger.Info("scheme list\n", t.Render())
}

func listSchemeGroupsVersions(ctx context.Context, name string, count int) {
	data, err := getQPackage().GetGroupVersionList(ctx, name)
	if err != nil {
		sugarLogger.Errorf("%v", err)
		return
	}
	t := table.NewWriter()
	t.AppendHeader(table.Row{"name", "version"})
	if count > len(data) {
		count = len(data)
	}
	for _, v := range data[0:count] {
		t.AppendRow(table.Row{v.Name, v.Version})
	}
	sugarLogger.Infof("scheme version\n %s", t.Render())
}

func showSchemeGroupVersion(ctx context.Context, name, version string) {
	data, err := getQPackage().GetGroupVersionInfo(ctx, name, version)
	if err != nil {
		sugarLogger.Errorf("%v", err)
		return
	}
	str := buffer.Buffer{}
	write := func(label, value string) {
		// nolint
		_, _ = str.WriteString(fmt.Sprintf("%s%s\n", text.AlignLeft.Apply(label, 14), text.AlignLeft.Apply(value, 20)))
	}
	write("name   :", data.Name)
	write("version:", data.Version)
	write("release_note:", data.ReleaseNote)
	write("created:", time.Unix(data.CreateTime, 0).String())
	write("updated:", time.Unix(data.UpdateTime, 0).String())
	t := table.NewWriter()
	t.AppendHeader(table.Row{"name", "type", "version", "release_note"})
	for _, v := range data.Schemes {
		t.AppendRow(table.Row{v.Name, v.Type, v.Version})
	}
	write("schemes:", "")
	_, _ = str.WriteString(t.Render())
	sugarLogger.Infof("scheme version\n%s", str.String())
}

func installGroupVersion(ctx context.Context, name, version string) (err error) {
	_, err = getQPackage().GroupInstallAll(ctx, name, version)
	return err
}

func installGroupVersionDryRun(ctx context.Context, name, version string, level int) (err error) {
	res, err := getQPackage().GroupInstallDryRunAll(ctx, name, version)
	if err != nil {
		sugarLogger.Errorf("dry run install group %s-%s failed: %v", name, version, err)
		return err
	}
	for _, scheme := range res.Schemes {
		err = installSchemeVersionDryRun(ctx, scheme.Name, scheme.RequiredVersion)
		if err != nil {
			return err
		}
	}
	for _, group := range res.Groups {
		err = installGroupVersionDryRun(ctx, group.Name, group.RequiredVersion, level+1)
		if err != nil {
			return err
		}
	}
	if level == 0 {
		if res.ShouldReboot() {
			sugarLogger.Infof("reboot required")
		}
		if res.ShouldMPRestart() {
			sugarLogger.Infof("mp restart required")
		}
		if res.ShouldGwReload() {
			sugarLogger.Infof("gateway reload required")
		}
	}
	return
}

func removeSchemeGroupVersion(ctx context.Context, name, version string) (err error) {
	err2 := utils.HasRootPrivilege()
	if err2 != nil {
		sugarLogger.Errorf("%v", err2)
		return err2
	}
	return getQPackage().GroupRemoveAll(ctx, name, version)
}

func schemeGroupCurrentLocal(ctx context.Context, group string, level int) error {
	local := getQPackage().GetLocalGroupInfo(ctx, group)
	if local.Version == "" {
		sugarLogger.Infof("%s local version not found", group)
		return nil
	}
	info, err := getQPackage().GetGroupVersionInfo(ctx, local.Name, local.Version)
	if err != nil {
		sugarLogger.Errorf("get version %v", err)
		return err
	}
	if level == 0 {
		var write buffer.Buffer
		_, _ = write.WriteString(fmt.Sprintf("\ngroup_name :%s\n", info.Name))
		_, _ = write.WriteString(fmt.Sprintf("version    :%s\n", info.Version))
		_, _ = write.WriteString(fmt.Sprintf("created    :%s\n", time.Unix(info.CreateTime, 0).String()))
		_, _ = write.WriteString(fmt.Sprintf("updated    :%s\n", time.Unix(info.UpdateTime, 0).String()))
		sugarLogger.Info(write.String())
	}
	for _, v := range info.Schemes {
		if v.Type == qnexus.GroupTypeScheme {
			schemeCurrentLocal(ctx, v.Name)
		} else if v.Type == qnexus.GroupTypeGroup {
			err = schemeGroupCurrentLocal(ctx, v.Name, level+1)
			if err != nil {
				return err
			}
		}
	}
	return nil
}

// 提供给 qpilot-setup 做 qpilot 启动校验
// 0: 正常
// 1: warning，表示获取repo中的版本详情失败
// 2: 错误，表示校验失败
func schemeGroupDiff(ctx context.Context, group, version string) {
	versionToCheck := version
	if version == "" {
		local := getQPackage().GetLocalGroupInfo(ctx, group)
		if local.Version == "" {
			fmt.Println("本地未安装该 group")
			os.Exit(lib.ExitWarning)
			return
		}
		versionToCheck = local.Version
	}
	var diffModuleStr bytes.Buffer
	diff, err := getQPackage().GroupInstallDryRunAll(ctx, group, versionToCheck)
	if err != nil {
		os.Exit(lib.ExitWarning)
		return
	}
	if !diff.HasDiff {
		return
	}
	printGroupDiff(diff, &diffModuleStr)
	if diffModuleStr.String() == "" {
		os.Exit(0)
		return
	}
	color.Magenta(diffModuleStr.String())
	fmt.Println("\nuse this cmd reinstall the version")
	color.Red("sudo qomolo-get g %s %s --install\n", group, versionToCheck)
	os.Exit(lib.ExitError)
}

func printGroupDiff(diff *qpackage.InstallDryRunRes, diffModuleStr *bytes.Buffer) {
	if !diff.HasDiff {
		return
	}
	printSchemeDiff(diff.Schemes, diffModuleStr)
	for _, v := range diff.Groups {
		printGroupDiff(&v, diffModuleStr)
	}
}

func printSchemeDiff(schemes []qpackage.InstallDryRunRes, diffModuleStr *bytes.Buffer) {
	for _, v := range schemes {
		if !v.HasDiff {
			continue
		}
		for _, m := range v.Modules {
			if !m.IsDiff {
				continue
			}
			if m.LocalVersion == "" {
				diffModuleStr.WriteString(fmt.Sprintf("%s=%s not install\n", m.PkgName, m.RequiredVersion))
			} else {
				diffModuleStr.WriteString(fmt.Sprintf("%s was changed，expected: %s actual: %s\n", m.PkgName, m.RequiredVersion, m.LocalVersion))
			}
		}
	}
}

func downloadSchemeGroupVersion(ctx context.Context, name, version, arch, path string) (err error) {
	err2 := utils.HasRootPrivilege()
	if err2 != nil {
		sugarLogger.Errorf("%v", err2)
		return err2
	}
	return getQPackage().GroupDownload(ctx, name, version, arch, path)
}

func installSchemeGroupVersionFromZip(ctx context.Context, name, version, file string) error {
	err2 := utils.HasRootPrivilege()
	if err2 != nil {
		sugarLogger.Errorf("%v", err2)
		return err2
	}
	return getQPackage().GroupInstallFromZip(ctx, name, version, file)
}

func schemeGroupCurrentLocalFormatJSON(ctx context.Context, group, format string, full bool, level int) (map[string]interface{}, error) {
	return getQPackage().GroupInfo(ctx, group, format, full, level)
}

func installGroupVersionWithProgress(ctx context.Context, name, version string) (err error) {
	qp := getQPackage()
	ticker := time.NewTicker(2 * time.Second)
	defer ticker.Stop()
	go func() {
		for range ticker.C {
			percent := qp.Downloader.GetPercent()
			imagePercent := qp.Downloader.GetImagePercent()
			fmt.Printf("group %s %s download percent %.2f %%\n", name, version, percent)
			imageFinish := 0
			for i, p := range imagePercent {
				fmt.Printf("image %s download percent %.2f %%\n", i, p)
				if p == 100 {
					imageFinish++
				}
			}
			if percent == 100 && len(imagePercent) == imageFinish {
				return
			}
			ticker.Reset(2 * time.Second)
		}
	}()
	err = qp.DownloadGroupWithProgress(ctx, name, version, "")
	if err != nil {
		return err
	}
	time.Sleep(2 * time.Second)
	_, err = getQPackage().GroupInstallAll(ctx, name, version)
	return err
}
