package cmd

import (
	"fmt"
	"os"
	"time"

	"github.com/spf13/cobra"

	"gitlab.qomolo.com/qomolo_utils/qomolo_get/internal/lib"
	"gitlab.qomolo.com/qomolo_utils/qomolo_get/pkg/utils"
)

// pullCmd represents the pull command
var historyCmd = &cobra.Command{
	Use:   `history`,
	Short: "history",
	Long: `
example:
  # show qomolo-get install history
  qomolo-get history`,
	RunE: func(_ *cobra.Command, _ []string) error {
		err := utils.HasRootPrivilege()
		if err != nil {
			sugarLogger.Errorf("%v", err)
			return err
		}
		return readHistory()
	},
}

var historyLog = lib.GetLogWriter(historyFile)

func init() {
	rootCmd.AddCommand(historyCmd)
}

type actionType string

const (
	installAction actionType = "install"
	removeAction  actionType = "remove"
)

func writeHistory(action actionType, typ, name, version, prevVersion string, success bool) error {
	// 写入数据
	data := fmt.Sprintf("time:%s action:%s type:%s name:%s version:%s prev:%s success:%+v \n", time.Now().Format(time.RFC3339), action, typ, name, version, prevVersion, success)
	_, err := historyLog.Write([]byte(data))
	return err
}

func readHistory() error {
	file, err := os.ReadFile(historyFile)
	if err != nil {
		return err
	}
	fmt.Println(string(file)) // nolint
	return nil
}
