package cmd

import (
	"context"
	"fmt"
	"os"
	"os/exec"
	"time"

	qlog "gitlab.qomolo.com/infrastructure/sw_architecture/middleware_abstraction/utility/qlog_golang"
	"gitlab.qomolo.com/qomolo_utils/qomolo_get/pkg/qpackage"
	"gitlab.qomolo.com/qomolo_utils/qomolo_get/pkg/utils"

	"github.com/jedib0t/go-pretty/v6/table"
	"github.com/spf13/cobra"
	"go.uber.org/zap/buffer"
)

// aptCmd represents the apt command
var aptCmd = &cobra.Command{
	Use: `apt -u/--update
	     apt -i/--install [pkg1 pkg2 ...]
	     apt -r/--remove [pkg1 pkg2 ...]
	     apt -q/--query [pkg]
		 apt --dpkg-install [pkg1 pkg2 ...]`,
	Short: "apt utils",
	Long:  `apt`,
	RunE: func(cmd *cobra.Command, args []string) error {
		err := utils.HasRootPrivilege()
		if err != nil {
			sugarLogger.Errorf("%v", err)
			return err
		}
		update, _ := cmd.Flags().GetBool("update")
		ctx := context.Background()
		if update {
			return getQPackage().AptUpdate(ctx, false)
		}

		install, _ := cmd.Flags().GetBool("install")
		if install {
			return getQPackage().AptInstallAll(ctx, qpackage.NewPkgInfoList(args))
		}

		remove, _ := cmd.Flags().GetBool("remove")
		if remove {
			return getQPackage().Remove(ctx, args)
		}

		envPath, _ := cmd.Flags().GetString("env-path")
		ignoreDepends, _ := cmd.Flags().GetString("ignore-depends")
		forceAll, _ := cmd.Flags().GetBool("force-all")
		query, _ := cmd.Flags().GetString("query")
		ignorePullImages, _ := cmd.Flags().GetBool("ignore-pull-images")
		ctx = utils.SetCtxEnvPath(context.Background(), envPath)
		ctx = utils.SetCtxIgnoreDepends(ctx, ignoreDepends)
		ctx = utils.SetForceAllFlag(ctx, forceAll)
		ctx = utils.SetCtxIgnorePullImages(ctx, ignorePullImages)

		if len(query) > 0 {
			return aptQuery(ctx, query)
		}

		dpkgInstall, _ := cmd.Flags().GetBool("dpkg-install")
		if dpkgInstall {
			pkgInfoList := qpackage.NewPkgInfoList(args)
			if len(pkgInfoList) != 1 {
				return fmt.Errorf("dpkg-install only support one pkg")
			}
			pkgInfo := pkgInfoList[0]
			dpkgDebDir, _ := cmd.Flags().GetString("dpkg-deb-dir")
			repo, _ := cmd.Flags().GetString("repo")
			arch, _ := cmd.Flags().GetString("arch")
			if arch == "" {
				arch, _ = utils.GetArch()
			}
			if dpkgDebDir == "" {
				dpkgDebDir = fmt.Sprintf("%s/%v", "/data/MP/tmp/tmp_debs", time.Now().Unix())
				_, err1 := os.Stat(dpkgDebDir)
				if os.IsNotExist(err1) {
					err = os.MkdirAll(dpkgDebDir, 0755)
					if err != nil {
						return err
					}
				}
			}
			if pkgInfo.RequiredVersion == "" {
				qlog.Infof("[--dpkg-install] %s 未指定版本，默认下载最新", pkgInfo.PkgName)
				_ = getQPackage().AptUpdate(ctx, false)
				latestVersion, _ := getQPackage().AptGetPkgLatestVersion(ctx, pkgInfo.PkgName)
				pkgInfo.RequiredVersion = latestVersion
			}
			err := getQPackage().DownloadDeb(ctx, pkgInfo.PkgName, pkgInfo.RequiredVersion, repo, arch, dpkgDebDir)
			if err != nil {
				return err
			}

			return getQPackage().DpkgInstall(ctx, []qpackage.PkgInfo{pkgInfo}, dpkgDebDir, false)
		}

		dpkgList, _ := cmd.Flags().GetBool("dpkg-list")
		if dpkgList {
			dpkgRoot := fmt.Sprintf("/data/qomolo_service/.%s/", envPath)
			cmdStr := fmt.Sprintf("dpkg --instdir=%s --admindir=%svar/lib/dpkg --root=%s --force-script-chrootless -l", dpkgRoot, dpkgRoot, dpkgRoot)
			output, err := exec.Command("bash", "-c", cmdStr).CombinedOutput()
			if err != nil {
				return fmt.Errorf("dpkg list error: %v, output: %s", err, string(output))
			}
			fmt.Printf("============== env-path %s | %s ==============\n", envPath, dpkgRoot)
			fmt.Println(string(output))
			return nil
		}

		dpkgRemove, _ := cmd.Flags().GetBool("dpkg-remove")
		if dpkgRemove {
			return getQPackage().DpkgUtil.Remove(ctx, args)
		}

		if len(args) < 1 && !update && !install && !remove {
			_ = cmd.Usage()
			os.Exit(1)
		}
		return nil
	},
}

func init() {
	rootCmd.AddCommand(aptCmd)

	// Here you will define your flags and configuration settings.

	// Cobra supports Persistent Flags which will work for this command
	// and all subcommands, e.g.:
	// aptCmd.PersistentFlags().StringP("source", "s", "", "specific one apt source file")

	// Cobra supports local flags which will only run when this command
	// is called directly, e.g.:
	aptCmd.Flags().BoolP("update", "u", false, "update apt cache")
	aptCmd.Flags().BoolP("install", "i", false, "install pkgs via apt")
	aptCmd.Flags().BoolP("remove", "r", false, "remove pkgs via apt")
	aptCmd.Flags().BoolP("dpkg-install", "", false, "install pkgs via custom dpkg")
	aptCmd.Flags().BoolP("dpkg-remove", "", false, "remove pkgs via custom dpkg")
	aptCmd.Flags().StringP("query", "q", "", "query pkg via dpkg")
	aptCmd.Flags().StringP("dpkg-deb-dir", "", "", "use with dpkg-install, default .")
	aptCmd.Flags().StringP("env-path", "", "0", "use with dpkg-install, default 0")
	aptCmd.Flags().StringP("repo", "", "alpha", "use with dpkg-install, default alpha")
	aptCmd.Flags().StringP("arch", "", "", "use with dpkg-install, default current arch")
	aptCmd.Flags().StringP("ignore-depends", "", "", "ignore dpkg package depends")
	aptCmd.Flags().BoolP("force-all", "", false, "use with dpkg-remove")
	aptCmd.Flags().BoolP("dpkg-list", "", false, "list dpkg packages in env")
	aptCmd.Flags().BoolP("ignore-pull-images", "", false, "ignore pulling docker images during installation")
}

func aptQuery(ctx context.Context, query string) error {
	results, err := getQPackage().Query(ctx, query, false)
	if err != nil {
		return err
	}
	str := buffer.Buffer{}
	t := table.NewWriter()
	t.AppendHeader(table.Row{"name", "version", "is_installed"})
	for _, v := range results {
		t.AppendRow(table.Row{v.PkgName, v.Version, v.IsInstalled})
	}
	_, _ = str.WriteString(t.Render())
	sugarLogger.Infof("scheme version\n%s", str.String())
	return nil
}
