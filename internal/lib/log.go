package lib

import (
	"os"
	"time"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

var sugarLogger *zap.SugaredLogger

func InitLogger(logFilePath string, stdoutDisabled, debug bool) *zap.SugaredLogger {
	var multiWriteSyncer zapcore.WriteSyncer
	if stdoutDisabled {
		multiWriteSyncer = zapcore.NewMultiWriteSyncer(
			GetLogWriter(logFilePath))
	} else {
		multiWriteSyncer = zapcore.NewMultiWriteSyncer(
			zapcore.AddSync(os.Stdout),
			GetLogWriter(logFilePath))
	}

	encoder := getEncoder()
	level := zapcore.InfoLevel
	if debug {
		level = zap.DebugLevel
	}
	core := zapcore.NewCore(encoder, multiWriteSyncer, level)
	var logger *zap.Logger
	opts := make([]zap.Option, 0)
	if debug {
		opts = append(opts, zap.AddCaller())
	}
	logger = zap.New(core, opts...)
	sugarLogger = logger.Sugar()
	return sugarLogger
}

func getEncoder() zapcore.Encoder {
	encoderConfig := zap.NewProductionEncoderConfig()
	// encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	encoderConfig.EncodeTime = zapcore.TimeEncoder(
		func(t time.Time, enc zapcore.PrimitiveArrayEncoder) {
			enc.AppendString(t.Local().Format("2006-01-02 15:04:05"))
		})
	encoderConfig.EncodeLevel = zapcore.CapitalLevelEncoder
	return zapcore.NewConsoleEncoder(encoderConfig)
}

func GetLogWriter(logFilePath string) zapcore.WriteSyncer {
	hook := lumberjack.Logger{
		Filename: logFilePath, // 日志文件路径
		// nolint
		MaxSize: 20, // megabytes
		// nolint
		MaxBackups: 7, // 最多保留3个备份
		// nolint
		MaxAge:   7,    //days
		Compress: true, // 是否压缩 disabled by default
	}
	return zapcore.AddSync(&hook)
}
