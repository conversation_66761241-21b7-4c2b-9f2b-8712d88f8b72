# README

## 编译

### 本机编译
1. 安装 golang 1.19
2. cd 到有go.mod 的目录
```bash
export GO111MODULE=on
export GOPROXY=https://proxy.golang.com.cn,direct
go get 
# 直接运行
go run main.go

# 调试运行
DEBUG=true go run main.go

# 编译
go build -o qomolo_get
# linux amd64 下生成 arm64 可执行文件
CGO_ENABLED=0 GOOS=linux GOARCH=arm64 go build -o qomolo_get
```
### 使用docker

```bash
docker pull golang:1.19
docker run --rm -it -v $(pwd):/src golang:1.19 bash
root@xxxxxxxxxxxx:/go# mkdir /build
root@xxxxxxxxxxxx:/go# cp -r /src/* /build/
root@xxxxxxxxxxxx:/build# export GO111MODULE=on
root@xxxxxxxxxxxx:/build# export GOPROXY=https://proxy.golang.com.cn,direct
root@xxxxxxxxxxxx:/build# go get 
root@xxxxxxxxxxxx:/build# go build
# linux amd64 下生成 arm64 可执行文件
root@xxxxxxxxxxxx:/build# CGO_ENABLED=0 GOOS=linux GOARCH=arm64 go build
```

## 使用文档

### cli

> 用了cobra-cli 生成，自动生成help，也可以直接运行 qomolo_get -h 查看详细用法
为了保证一致，直接看每个命令的 help 信息