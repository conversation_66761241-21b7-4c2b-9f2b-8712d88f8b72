#!/bin/bash
COLOR_RED="\e[31m"
COLOR_GREEN="\e[32m"
COLOR_CLEAR="\e[0m"

project_profile="/etc/qomolo/profile/project/profile.yaml"
veh_type_profile="/etc/qomolo/profile/vehicle_type/profile.yaml"
chassis_profile="/etc/qomolo/profile/chassis/profile.yaml"
global_profile="/etc/qomolo/profile/global/profile.yaml"
miivii_release="/etc/miivii_release"

test_pass=()
test_fail=()
useradd qprofile
check_in_docker() {
    if [ -f /.dockerenv ] || [ -f /run/.containerenv ]; then
        :
    elif [[ $(cat /proc/self/cgroup | grep -E "docker|containerd") ]]; then
        :
    else
        echo "Not in docker, script contains dangerous cmd, exiting."
        exit 1
    fi
}

check_by_cmd_exec_return_code() {
    local name=$1
    local ret_code=$2
    if [ $ret_code -eq 0 ]; then
        msg_pass="${COLOR_GREEN}[$name]__test_pass${COLOR_CLEAR}"
        echo -e $msg_pass
        test_pass=(${test_pass[*]} "$msg_pass")
    else
        msg_fail="${COLOR_RED}[$name]__test_fail${COLOR_CLEAR}"
        echo -e $msg_fail
        test_fail=(${test_fail[*]} "$msg_fail")
    fi
}

check_by_history_success_state() {
    local name=$1
    local state=$2
    if [[ $state == "true" ]]; then
        msg_pass="${COLOR_GREEN}[$name]__test_pass${COLOR_CLEAR}"
        echo -e $msg_pass
        test_pass=(${test_pass[*]} "$msg_pass")
    else
        msg_fail="${COLOR_RED}[$name]__test_fail${COLOR_CLEAR}"
        echo -e $msg_fail
        test_fail=(${test_fail[*]} "$msg_fail")
    fi
}

final_report() {
    echo ======== All Tests =======
    for i in ${test_pass[*]}; do
        echo -e "$i"
    done

    for j in ${test_fail[*]}; do
        echo -e "$j"
    done
    echo ==========================
}

upgrade_and_prepare() {
    # apt update -o Dir::Etc::sourcelist="/etc/apt/sources.list.d/qomolo-foxy.list" -o Dir::Etc::sourceparts="-" -o APT::Get::List-Cleanup="0"
    # apt install -y qomolo-get -o Dir::Etc::sourcelist="/etc/apt/sources.list.d/qomolo-foxy.list" -o Dir::Etc::sourceparts="-" -o APT::Get::List-Cleanup="0"
    sed -i 's@//.*archive.ubuntu.com@//mirrors.ustc.edu.cn@g' /etc/apt/sources.list || true
    sed -i -e 's@//ports.ubuntu.com/\? @//ports.ubuntu.com/ubuntu-ports @g' -e 's@//ports.ubuntu.com@//mirrors.ustc.edu.cn@g' /etc/apt/sources.list || true
    rm /etc/apt/sources.list.d/deadsnakes-ubuntu-ppa-focal.list || true
    rm /etc/apt/sources.list.d/ros2-latest.list || true
    rm /etc/apt/sources.list.d/bionic_sources.list || true
    apt-get update
    apt-get install -y qomolo-qprofile zip
    dpkg -i $(find ../debs -name '*.deb' | grep $PACKAGE_ARCHITECTURE | grep qomolo-get)
    rm *.zip *.deb
}

cleanup_install() {
    rm /var/lib/dpkg/info/qpilot-setup.postrm || true
    apt purge -y $(dpkg -l | grep -E "qomolo|qpilot" | grep -E -v "qomolo-get|qomolo-qprofile" | awk '{print $2}')
}

basic_apt-u() {
    qomolo-get apt -u
    check_by_cmd_exec_return_code $FUNCNAME $?
}

basic_apt-i() {
    qomolo-get apt -i qomolo-data
    check_by_cmd_exec_return_code $FUNCNAME $?
}

basic_apt-r() {
    qomolo-get apt -r qomolo-data
    check_by_cmd_exec_return_code $FUNCNAME $?
}

basic_s() {
    qomolo-get s | grep "scheme list"
    check_by_cmd_exec_return_code $FUNCNAME $?
}

basic_s_scheme() {
    qomolo-get s qpilot-scheme | grep qpilot-scheme
    check_by_cmd_exec_return_code $FUNCNAME $?
}

basic_s_scheme_version() {
    qomolo-get s qpilot-scheme 0.0.920 | grep -E "(qomolo|qpilot).*[0-9]\.[0-9]"
    check_by_cmd_exec_return_code $FUNCNAME $?
}

basic_s_scheme_install() {
    qomolo-get s qpilot-scheme 0.0.920 --install
    state=$(qomolo-get history | grep -E "scheme.*qpilot-scheme.*0.0.920.*" | awk -F":" '{print $NF}' | tail -n 1)
    check_by_history_success_state $FUNCNAME $state

    cleanup_install
}

basic_s_scheme_cur() {
    qomolo-get s qpilot-scheme --cur | grep -E "(qomolo|qpilot).*[0-9]\.[0-9].*"
    check_by_cmd_exec_return_code $FUNCNAME $?
}

basic_s_scheme_cur_format_json() {
    qomolo-get s qpilot-scheme --cur --format json | grep -E "qpilot-scheme.*[0-9]\.[0-9].*"
    check_by_cmd_exec_return_code $FUNCNAME $?
}

basic_s_scheme_extract_to_zip() {
    qomolo-get s qpilot-scheme 0.0.920 --extract-to-zip
    check_by_cmd_exec_return_code $FUNCNAME $?
}

basic_s_scheme_install_from_zip() {
    zip_file=$(ls | grep -E "qpilot-scheme_0.0.920.*zip")
    qomolo-get s qpilot-scheme 0.0.920 --install-from-zip --zip-file=$zip_file
    state=$(qomolo-get history | grep -E "scheme.*qpilot-scheme.*0.0.920.*" | awk -F":" '{print $NF}' | tail -n 1)
    check_by_history_success_state $FUNCNAME $state

    cleanup_install
}

basic_g() {
    qomolo-get g | grep "scheme list"
    check_by_cmd_exec_return_code $FUNCNAME $?
}

basic_g_group() {
    qomolo-get g qpilot-group | grep qpilot-group
    check_by_cmd_exec_return_code $FUNCNAME $?
}

basic_g_group_version() {
    qomolo-get g qpilot-group 0.0.985 | grep -E "(qomolo|qpilot).*[0-9]\.[0-9]"
    check_by_cmd_exec_return_code $FUNCNAME $?
}

basic_g_group_install() {
    qomolo-get g qpilot-group 0.0.985 --install
    state=$(qomolo-get history | grep -E "group.*qpilot-group.*0.0.985.*" | awk -F":" '{print $NF}' | tail -n 1)
    check_by_history_success_state $FUNCNAME $state

    cleanup_install
}

basic_g_group_cur() {
    qomolo-get g qpilot-group --cur | grep -E "(qomolo|qpilot).*[0-9]\.[0-9].*"
    check_by_cmd_exec_return_code $FUNCNAME $?
}

basic_g_group_cur_format_json() {
    qomolo-get g qpilot-group --cur --format json | grep -E "qpilot-group.*[0-9]\.[0-9].*"
    check_by_cmd_exec_return_code $FUNCNAME $?
}

basic_g_group_extract_to_zip() {
    qomolo-get g qpilot-group 0.0.985 --extract-to-zip
    check_by_cmd_exec_return_code $FUNCNAME $?
}

basic_g_group_install_from_zip() {
    zip_file=$(ls | grep -E "qpilot-group_0.0.985.*zip")
    qomolo-get g qpilot-group 0.0.985 --install-from-zip --zip-file=$zip_file
    state=$(qomolo-get history | grep -E "group.*qpilot-group.*0.0.985.*" | awk -F":" '{print $NF}' | tail -n 1)
    check_by_history_success_state $FUNCNAME $state

    cleanup_install
}

special_dpkg_install() {
    apt download qomolo-compose-demo=0.0.3-499801
    qomolo-get apt --dpkg-install qomolo-compose-demo=0.0.3-499801
    dpkg -l --root=/data/qomolo_service/.0/ | grep ii | grep -E "qomolo-compose-demo.*0.0.3-499801"
    check_by_cmd_exec_return_code $FUNCNAME $?
}

special_dpkg_remove() {
    qomolo-get apt --dpkg-remove qomolo-compose-demo
    dpkg -l --root=/data/qomolo_service/.0/ | grep rc | grep -E "qomolo-compose-demo.*0.0.3-499801"
    check_by_cmd_exec_return_code $FUNCNAME $?
}

special_dpkg_install_with_dpkg_dir_seq() {
    qomolo-get apt --dpkg-install qomolo-compose-demo=0.0.3-499801 --env-path=1
    dpkg -l --root=/data/qomolo_service/.1/ | grep ii | grep -E "qomolo-compose-demo.*0.0.3-499801"
    check_by_cmd_exec_return_code $FUNCNAME $?
}

special_dpkg_remove_with_dpkg_dir_seq() {
    qomolo-get apt --dpkg-remove qomolo-compose-demo --env-path=1
    dpkg -l --root=/data/qomolo_service/.1/ | grep rc | grep -E "qomolo-compose-demo.*0.0.3-499801"
    check_by_cmd_exec_return_code $FUNCNAME $?
}

prepare_clean_profiles_and_miivii_release() {
    if [[ $(cat /proc/1/cgroup | grep /docker) ]]; then
        if [ -f $veh_type_profile ]; then
            rm $veh_type_profile
        fi

        if [ -f $project_profile ]; then
            rm $project_profile
        fi

        if [ -f $global_profile ]; then
            rm $global_profile
        fi

        if [ -f $miivii_release ]; then
            rm $miivii_release
        fi

        if [ -f $chassis_profile ]; then
            rm $chassis_profile
        fi
    else
        echo not in docker, skip rm
    fi
}

prepare_setup_profiles_and_miivii_release() {
    mkdir -p /etc/qomolo/profile/{project,vehicle_type,global,chassis}
    echo "profile_name: qtaa" >$veh_type_profile
    echo "profile_name: cnwxijk" >$project_profile
    echo "host_type: dcu" >$global_profile
    echo "profile_name: csqtaa" >$chassis_profile
    echo "MIIVII APEX XAVIER II PLUS 4.5-*******" >/etc/miivii_release
}

special_modules_with_label_condition_1() {
    # 场景1：无 profile/miivii_release
    # 期望安装结果：
    #    qomolo-data(无label)
    #    qomolo-registry-tools(module/only-upgrade)
    #    qomolo-compose-demo(module/dpkg-install)
    echo "special_modules_with_label_condition_1 prepare install"
    prepare_clean_profiles_and_miivii_release
    qomolo-get s qpilot-foxy 2.5.2 --install
    echo "special_modules_with_label_condition_1 install finished"
    apt_count=$(dpkg -l | grep -E "qomolo-data|qomolo-registry-tools" | wc -l)
    dpkg_count=$(dpkg -l --root=/data/qomolo_service/.0 | grep qomolo-compose-demo | wc -l)

    if [ $apt_count -eq 2 ] && [ $dpkg_count -eq 1 ]; then
        code=0
    else
        code=1
    fi
    check_by_cmd_exec_return_code $FUNCNAME $code
    apt purge -y qomolo-data qomolo-registry-tools
    dpkg --instdir=/data/qomolo_service/.0 --admindir=/data/qomolo_service/.0/var/lib/dpkg --root=/data/qomolo_service/.0 --force-script-chrootless --purge qomolo-compose-demo
}

special_modules_with_label_condition_2() {
    # 场景2：有 profile/miivii_release
    # 期望安装结果：
    #    qomolo-data(无label)
    #    qomolo-registry-tools(module/only-upgrade)
    #    qomolo-compose-demo(module/dpkg-install)
    #    qomolo-service-adaops-backend-test(module/dcu=xav-iiplus)
    #    qpilot-profile-project-cnwxijk(__project=cnwxijk)
    #    qomolo-profile-veh-type-qtaa(__vehicle_type=qtaa)
    #    qomolo-chassis-profile-scheme(__chassis=csqtaa)
    prepare_setup_profiles_and_miivii_release

    qomolo-get s qpilot-foxy 2.5.11 --install
    apt_count=$(dpkg -l | grep -E "qomolo-data|qomolo-registry-tools|qomolo-service-adaops-backend-test|qpilot-profile-project-cnwxijk|qomolo-profile-veh-type-qtaa | qomolo-profile-chassis-csqtaa" | wc -l)
    dpkg_count=$(dpkg -l --root=/data/qomolo_service/.0 | grep qomolo-compose-demo | wc -l)

    if [ $apt_count -eq 6 ] && [ $dpkg_count -eq 1 ]; then
        code=0
    else
        code=1
    fi
    check_by_cmd_exec_return_code $FUNCNAME $code
    apt purge -y qomolo-data qomolo-registry-tools qomolo-service-adaops-backend-test qpilot-profile-project-cnwxijk qomolo-profile-veh-type-qtaa
    dpkg --instdir=/data/qomolo_service/.0 --admindir=/data/qomolo_service/.0/var/lib/dpkg --root=/data/qomolo_service/.0 --force-script-chrootless --purge qomolo-compose-demo
}

special_modules_with_label_condition_3() {
    # 场景3：无 profile/miivii_release, qomolo-registry-tools安装了高版本
    # 期望安装结果：
    #    报错 install failed, not allow to downgrade from
    prepare_clean_profiles_and_miivii_release

    apt update -o Dir::Etc::sourcelist="/etc/apt/sources.list.d/qomolo-foxy.list" -o Dir::Etc::sourceparts="-" -o APT::Get::List-Cleanup="0"
    apt install -y qomolo-registry-tools -o Dir::Etc::sourcelist="/etc/apt/sources.list.d/qomolo-foxy.list" -o Dir::Etc::sourceparts="-" -o APT::Get::List-Cleanup="0"

    output=$(APT_DRY_RUN=true qomolo-get s qpilot-foxy 2.5.1 --install 2>&1)
    echo "$output" | grep -E "install.*failed.*not allow to downgrade.*"

    check_by_cmd_exec_return_code $FUNCNAME $?

    apt purge -y qomolo-registry-tools
}

not_allow_install_qpilot_group_when_project_label_mismatch() {
    prepare_setup_profiles_and_miivii_release
    output=$(APT_DRY_RUN=true qomolo-get g qpilot-group 2.17.678 --install 2>&1)
    echo "$output" | grep -E "failed.*not match current project.*"
    check_by_cmd_exec_return_code $FUNCNAME $?
    prepare_clean_profiles_and_miivii_release
}

main() {
    check_in_docker
    upgrade_and_prepare

    basic_apt-u
    basic_apt-i
    basic_apt-r

    basic_s
    basic_s_scheme
    basic_s_scheme_version
    basic_s_scheme_install
    basic_s_scheme_cur
    basic_s_scheme_cur_format_json
    basic_s_scheme_extract_to_zip
    basic_s_scheme_install_from_zip

    basic_g
    basic_g_group
    basic_g_group_version
    basic_g_group_install
    basic_g_group_cur
    basic_g_group_cur_format_json
    basic_g_group_extract_to_zip
    basic_g_group_install_from_zip

    special_dpkg_install
    special_dpkg_remove
    special_dpkg_install_with_dpkg_dir_seq
    special_dpkg_remove_with_dpkg_dir_seq

    special_modules_with_label_condition_1
    special_modules_with_label_condition_2
    special_modules_with_label_condition_3

    #not_allow_install_qpilot_group_when_project_label_mismatch
    # group_remove_pkg_with_project_label

    final_report
}

main

if [[ ${#test_fail} > 0 ]]; then
    echo -e "${COLOR_RED}some test(s) fail${COLOR_CLEAR}"
    exit 1
else
    echo -e "${COLOR_GREEN}all test pass${COLOR_CLEAR}"
fi
