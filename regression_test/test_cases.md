# tests

## basic

### apt

```bash
qomolo-get apt -u
qomolo-get apt -i qomolo-data
qomolo-get apt -r qomolo-data
```

### s

```bash
qomolo-get s
qomolo-get s qpilot-scheme
qomolo-get s qpilot-scheme 0.0.920
qomolo-get s qpilot-scheme 0.0.920 --install
qomolo-get s qpilot-scheme --cur
qomolo-get s qpilot-scheme --cur --format json
qomolo-get s qpilot-scheme 0.0.920 --extract-to-zip
qomolo-get s qpilot-scheme 0.0.920 --install-from-zip --zip-file qpilot-group_0.0.920_xxx.zip
```

### g

```bash
qomolo-get g
qomolo-get g qpilot-group
qomolo-get g qpilot-group 0.0.985
qomolo-get g qpilot-group 0.0.985 --install
qomolo-get g qpilot-group --cur
qomolo-get g qpilot-group --cur --format json
qomolo-get g qpilot-group 0.0.985 --extract-to-zip
qomolo-get g qpilot-group 0.0.985 --install-from-zip --zip-file qpilot-group_0.0.985_xxx.zip
```

## special

### dpkg for qomolo-compose*

```bash
apt download qomolo-compose-demo=0.0.3-499801
qomolo-get apt --dpkg-install qomolo-compose-demo=0.0.3-499801
qomolo-get apt --dpkg-remove qomolo-compose-webrtc-proxy
qomolo-get apt --dpkg-install qomolo-compose-demo=0.0.3-499801 --env-path=1
qomolo-get apt --dpkg-remove qomolo-compose-demo --env-path=1
```

### modules with label condition 1

> 无 profile/miivii_release
  期望安装结果：
    qomolo-data(无label)
    qomolo-registry-tools(module/only-upgrade)
    qomolo-compose-demo(module/dpkg-install)

```bash
    APT_DRY_RUN=true qomolo-get s qpilot-foxy 2.5.1 --install
```

### modules with label condition 2

> 场景2：有 profile/miivii_release
  期望安装结果：
    qomolo-data(无label)
    qomolo-registry-tools(module/only-upgrade)
    qomolo-compose-demo(module/dpkg-install)
    qomolo-miivii-apex-l4t-core(module/dcu=xav-iiplus)
    qpilot-profile-project-cnwxijk(__project=cnwxijk)
    qomolo-profile-veh-type-qtaa(__vehicle_type=qtaa)

```bash
project_profile="/etc/qomolo/profile/project/profile.yaml"
veh_type_profile="/etc/qomolo/profile/vehicle_type/profile.yaml"
global_profile="/etc/qomolo/profile/global/profile.yaml"
miivii_release="/etc/miivii_release"

echo "profile_name: qtaa" > $veh_type_profile
echo "profile_name: cnwxijk" > $project_profile
echo "host_type: dcu" > $global_profile
echo "MIIVII APEX XAVIER II PLUS 4.5-*******" > /etc/miivii_release

APT_DRY_RUN=true qomolo-get s qpilot-foxy 2.5.1 --install
```

### modules with label condition 3

> 无 profile/miivii_release, qomolo-registry-tools(module/only-upgrade)安装了高版本
  期望安装结果：
    报错 install failed, not allow to downgrade from

```bash
apt update; apt install qomolo-registry-tools
APT_DRY_RUN=true qomolo-get s qpilot-foxy 2.5.1 --install
```
