package qpackage

import (
	"context"
	"os"
	"testing"
	"time"
)

func Test_parseDpkgControl(t *testing.T) {
	type args struct {
		isUnderlay bool
	}
	type res struct {
		Name        string
		Version     string
		IsInstalled bool
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
		wantRes res
	}{
		{
			name: "test1",
			args: args{
				isUnderlay: false,
			},
			wantErr: false,
			wantRes: res{
				Name:        "qomolo-qprofile",
				Version:     "0.2.26-615369",
				IsInstalled: true,
			},
		}, {
			name: "test2",
			args: args{
				isUnderlay: false,
			},
			wantErr: false,
			wantRes: res{
				Name:        "qomolo-profile-veh-type-igvaa",
				Version:     "0.0.115-491476",
				IsInstalled: false,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			file, err := os.ReadFile("./test/control_status.txt")
			if err != nil {
				t.Errorf("read file error:%s", err)
				return
			}
			gotRes := parseDpkgControl(file)
			info, ok := gotRes[tt.wantRes.Name]
			if !ok {
				t.Errorf("read file error:%s", err)
				return
			}
			if info.Version() != tt.wantRes.Version {
				t.Errorf("read file error:%s", err)
				return
			}
			if info.IsInstalled() != tt.wantRes.IsInstalled {
				t.Errorf("install not excepted %v", info.IsInstalled())
				return
			}
		})
	}
}
func testCtx() {
	c := make(chan bool)
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()
	var err error
	go func() {
		time.Sleep(time.Second * 2)
		c <- true
	}()
	go func() {
		select {
		case <-ctx.Done():
			println("done", ctx.Err().Error())
			if err != nil {
				println("failed")
			} else {
				println("success")
			}
			return
		case <-c:
			cancel()
			println("cancel")
			return
		}
	}()
	cmd := execCmd(ctx, "sleep 10000", false)
	cmd.CombinedOutput()
	println("end")
}

func TestCtxCancel(t *testing.T) {
	t.Skip()
	testCtx()
}
