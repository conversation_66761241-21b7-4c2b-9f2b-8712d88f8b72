package qpackage

var (
	// packageManagerPath 废弃,将 mp 移动到 MP 目录下
	packageManagerPath     = "/data/mp/agent/package_manager"
	packageMPManagerPath   = "/data/MP/agent/package_manager"
	downloadHistoryDB      = packageMPManagerPath + "/db/download.db"
	installHistoryDB       = packageMPManagerPath + "/db/install.db"
	moduleHistoryDB        = packageMPManagerPath + "/db/module.db"
	packageManagerFileName = "package_manager.yaml"
	Group                  = "group"
	Scheme                 = "scheme"
)
