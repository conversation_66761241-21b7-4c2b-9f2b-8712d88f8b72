package qpackage

import (
	"context"
	"fmt"
	"os"
	"os/exec"

	qlog "gitlab.qomolo.com/infrastructure/sw_architecture/middleware_abstraction/utility/qlog_golang"
	"gitlab.qomolo.com/qomolo_utils/qomolo_get/pkg/utils"
	"gopkg.in/yaml.v3"
)

const (
	aptArchivesDir = "/data/tmp/apt/archives/"
	dpkgTmpDir     = "/data/tmp/dpkg"
)

type AptYamlConfig struct {
	Apt AptYaml `yaml:"apt"`
}

type AptYaml struct {
	Source AptYamlSource `yaml:"source"`
}

type AptYamlSource struct {
	Amd64            string `yaml:"amd64"`
	Arm64Focal       string `yaml:"arm64.focal"`
	Arm64Bionic      string `yaml:"arm64.bionic"`
	Amd64Local       string `yaml:"amd64.local"`
	Arm64FocalLocal  string `yaml:"arm64.focal.local"`
	Arm64BionicLocal string `yaml:"arm64.bionic.local"`
}

const aptSource = `
apt:
  source:
    amd64: |
      deb [arch=amd64] https://repo.qomolo.com/repository/alpha/ focal main
      deb [arch=amd64] https://repo.qomolo.com/repository/unofficial/ focal main
      deb [arch=amd64] https://repo.qomolo.com/repository/official/ focal main

      deb https://repo.qomolo.com/repository/ubuntu-mirror/ focal main restricted
      deb https://repo.qomolo.com/repository/ubuntu-mirror/ focal-updates main restricted
      deb https://repo.qomolo.com/repository/ubuntu-mirror/ focal universe
      deb https://repo.qomolo.com/repository/ubuntu-mirror/ focal-updates universe
      deb https://repo.qomolo.com/repository/ubuntu-mirror/ focal multiverse
      deb https://repo.qomolo.com/repository/ubuntu-mirror/ focal-updates multiverse
      deb https://repo.qomolo.com/repository/ubuntu-mirror/ focal-backports main restricted universe multiverse
      deb https://repo.qomolo.com/repository/ubuntu-mirror/ focal-security main restricted
      deb https://repo.qomolo.com/repository/ubuntu-mirror/ focal-security universe
      deb https://repo.qomolo.com/repository/ubuntu-mirror/ focal-security multiverse
    arm64.focal: |
      deb [arch=arm64] https://repo.qomolo.com/repository/alpha/ focal main
      deb [arch=arm64] https://repo.qomolo.com/repository/unofficial/ focal main
      deb [arch=arm64] https://repo.qomolo.com/repository/official/ focal main

      deb https://repo.qomolo.com/repository/ubuntu-mirror-tsinghua/ focal main restricted
      deb https://repo.qomolo.com/repository/ubuntu-mirror-tsinghua/ focal-updates main restricted
      deb https://repo.qomolo.com/repository/ubuntu-mirror-tsinghua/ focal universe
      deb https://repo.qomolo.com/repository/ubuntu-mirror-tsinghua/ focal-updates universe
      deb https://repo.qomolo.com/repository/ubuntu-mirror-tsinghua/ focal multiverse
      deb https://repo.qomolo.com/repository/ubuntu-mirror-tsinghua/ focal-updates multiverse
      deb https://repo.qomolo.com/repository/ubuntu-mirror-tsinghua/ focal-backports main restricted universe multiverse
      deb https://repo.qomolo.com/repository/ubuntu-mirror-tsinghua/ focal-security main restricted
      deb https://repo.qomolo.com/repository/ubuntu-mirror-tsinghua/ focal-security universe
      deb https://repo.qomolo.com/repository/ubuntu-mirror-tsinghua/ focal-security multiverse
    arm64.bionic: |
      deb [arch=arm64] https://repo.qomolo.com/repository/alpha/ focal main
      deb [arch=arm64] https://repo.qomolo.com/repository/unofficial-bionic/ bionic main
      deb [arch=arm64] https://repo.qomolo.com/repository/official-bionic/ bionic main

      deb https://repo.qomolo.com/repository/ubuntu-mirror-tsinghua/ bionic main restricted
      deb https://repo.qomolo.com/repository/ubuntu-mirror-tsinghua/ bionic-updates main restricted
      deb https://repo.qomolo.com/repository/ubuntu-mirror-tsinghua/ bionic universe
      deb https://repo.qomolo.com/repository/ubuntu-mirror-tsinghua/ bionic-updates universe
      deb https://repo.qomolo.com/repository/ubuntu-mirror-tsinghua/ bionic multiverse
      deb https://repo.qomolo.com/repository/ubuntu-mirror-tsinghua/ bionic-updates multiverse
      deb https://repo.qomolo.com/repository/ubuntu-mirror-tsinghua/ bionic-backports main restricted universe multiverse
      deb https://repo.qomolo.com/repository/ubuntu-mirror-tsinghua/ bionic-security main restricted
      deb https://repo.qomolo.com/repository/ubuntu-mirror-tsinghua/ bionic-security universe
      deb https://repo.qomolo.com/repository/ubuntu-mirror-tsinghua/ bionic-security multiverse
    amd64.local: |
      deb [arch=amd64] http://repo.qmlsrv.zone:8081/repository/repo/alpha/ focal main
      deb [arch=amd64] http://repo.qmlsrv.zone:8081/repository/repo/beta/ focal main
      deb [arch=amd64] http://repo.qmlsrv.zone:8081/repository/repo/unofficial/ focal main
      deb [arch=amd64] http://repo.qmlsrv.zone:8081/repository/repo/official/ focal main

    arm64.focal.local: |
      deb [arch=arm64] http://repo.qmlsrv.zone:8081/repository/repo/alpha/ focal main
      deb [arch=arm64] http://repo.qmlsrv.zone:8081/repository/repo/beta/ focal main
      deb [arch=arm64] http://repo.qmlsrv.zone:8081/repository/repo/unofficial/ focal main
      deb [arch=arm64] http://repo.qmlsrv.zone:8081/repository/repo/official/ focal main

    arm64.bionic.local: |
      deb [arch=arm64] http://repo.qmlsrv.zone:8081/repository/repo/alpha/ focal main
      deb [arch=arm64] http://repo.qmlsrv.zone:8081/repository/repo/beta/ focal main
      deb [arch=arm64] http://repo.qmlsrv.zone:8081/repository/repo/unofficial-bionic/ bionic main
      deb [arch=arm64] http://repo.qmlsrv.zone:8081/repository/repo/official-bionic/ bionic main


`

const publicKey = `
-----BEGIN PGP PUBLIC KEY BLOCK-----
Version: GnuPG v2.0.22 (GNU/Linux)

mQENBF9XH7wBCACy9OQ4qgulQxI3ImD+HN9x5ThJ5Wk8aC/INeO3hXQ8n2C/LXWJ
gCfnL5Trm4Sh4nfFDAi8QKEe5rsC5yhik2feiVLWlDJNjqW4aRW9Dhykse7zlyCI
P7HsTwTwq2H26IKeUwgw/DmYSgcSJgDO5GHtyFalY/L55+6CvTEkjxHgrWqRLCb9
wvztF9l+S0a7AIUTdkzk9bHCd2bXbYxWuLSZilRmv3Ohgu3XW9hc7nwD8UrWyWyi
N3IitTJKqrA+OJLvUSO+EUdgJwW9u35yEmoOCTsQo4npe/edJ8wZ1Lmpz61SufKA
Q7yNOlolaxuohUwyl/lr76E/BSEWEoRNxAIPABEBAAG0NWFkbWluLnFvbW9sbyAo
cW9tb2xvKSA8YWRtaW4ucW9tb2xvQHdlc3R3ZWxsLWxhYi5jb20+iQE5BBMBAgAj
BQJfVx+8AhsDBwsJCAcDAgEGFQgCCQoLBBYCAwECHgECF4AACgkQ1YYqAzRWJmxr
KAgAi4o9jyT+baUjAQmFYkuUjbZn/SA5mwJgXyspu72tA1KdAPkiIcxSBPfWrDXT
8rpcQHacE3FXtRSjbMp6cbF7oFVGB3Rwuk8XnP82+FFPqLs/FvX4Ux7wUL0y/4Pr
+A3PhzUy2g3bi37lXPXlv8AC5fOpchpondvgUHDzvSl+6o6d4lpMTzKut6+lEJYQ
3bAZBQ5BApQjlwEFz+Xl+Zm9CUaQ90sTu/revJGQrqzKYYYUbexorpacN8laAd4K
UAqYlO/w4dKVgX14JLqYo2oYbw8P2g8ODQafAB4h3tvlRkW+MkadOSbJ+PKPivYa
nDoGkMLnPKkw4zt3AHezoN0YeLkBDQRfVx+8AQgAzH24hYC+yIoBo5ywcc7PVLs0
rMcxHSvVxRXB2KQnbolqB0VwtEB2qqYDllMZAotOR76YmCt+HlsZOW9dyJ/gBVQ4
x/xxmv1YhRgy4na8CXqKyl9xFVe1aK8CPSJQVLceIcB8Rljn+dKK3FZWolMIh0BY
DIhP00EUm7zHKJm6PjgjsSox1EaeMUOwtCp9rb6Xe4GeW3v9p3TfUMNV3e0ANdtf
G+OnshDDaIRhl3ptE7IvT38y4QfgPelWQIYCH2GHtGFujT+P/LS5yrbIPNDGtH7h
Xwo0Hbn4pJZcI3U3DKtV02kT4c2W0aHWWhlKvrAMPqyT17GH2O57Y9L8FbZkGQAR
AQABiQEfBBgBAgAJBQJfVx+8AhsMAAoJENWGKgM0ViZscGUIAJKejH9n/hD9LRWR
GHCTBkgCyTx8JTaX8nJdn+IYykAOLnPVpNnERWJETfq1Ag+X7QZYkFjtKbgJ6xMO
zG4TuhYN0bp15nTTLsLKQv34nopZa+pepZdtVrd8UN+pMD5EWCGOstxNYeflnbEh
hZBnWNHFhGAqkE3plsCQT29/eTt+mI/xg0aX0TPObwweta/VbrL0nFZXdgnPRj4o
sybgaYEz1Pq0OWWkJuo5gWZsUAloRhQXtOFUIF/XTRPKtfqaTnNymeaeoEWoaOpo
S+/RHs3gFhRsW9Pjw3OXOoJBtKAS2F0uD4ypK1dNLGbujAC88vKck1Bc/++z7BsA
AKpnQjA=
=jVgp
-----END PGP PUBLIC KEY BLOCK-----

`

type Arch string

const (
	ArchAmd64 Arch = "amd64"
	ArchArm64 Arch = "arm64"
)

type LinuxCodeName string

const (
	LinuxCodeNameFocal  LinuxCodeName = "focal"
	LinuxCodeNameBionic LinuxCodeName = "bionic"
)

type SourceFileType string

const (
	SourceFileTypeProject SourceFileType = "project"
	SourceFileTypeWWL     SourceFileType = "wwl"
	SourceFileTypeDefault SourceFileType = "default"
)

type AptConfig struct {
	c               *AptYamlConfig
	opt             AptConfigOption
	localSourcePath string // 项目现场 repo source 路径
	wwlSourcePath   string // 公司 repo source 路径
}

type AptConfigOption struct {
	Arch          Arch
	Code          LinuxCodeName
	Config        *AptYamlConfig
	ConfigPath    string // yaml 文件路径， config 为空使用默认的yaml配置
	SourceBaseDir string
}

func NewAptConfig(opt AptConfigOption) (*AptConfig, error) {
	if opt.SourceBaseDir == "" {
		opt.SourceBaseDir = "/tmp/MP/get_file"
	}
	var (
		sourceFile      = opt.SourceBaseDir + "/source.list"
		localSourceFile = opt.SourceBaseDir + "/local-source.list"
	)
	ctx := context.Background()
	_, err := checkAptKeyIsExistAndAdd(ctx)
	if err != nil {
		return nil, err
	}

	res := &AptConfig{
		localSourcePath: localSourceFile,
		wwlSourcePath:   sourceFile,
	}
	if opt.Config != nil {
		res.c = opt.Config
	} else {
		c, err := ParseAptConfig(ctx, opt.ConfigPath)
		if err != nil {
			return nil, err
		}
		res.c = c
	}

	res.opt = opt
	err = res.GenSource(ctx)
	if err != nil {
		return nil, err
	}
	return res, nil
}

// GenSource 同时生成 local 和 project source
func (a *AptConfig) GenSource(ctx context.Context) error {
	_ = os.MkdirAll(a.opt.SourceBaseDir, 0755)
	source, err := getSourceContent(ctx, a.c, a.opt.Code, a.opt.Arch, false)
	if err != nil {
		return err
	}
	if err := os.WriteFile(a.wwlSourcePath, []byte(source), 0644); err != nil {
		return err
	}
	source, err = getSourceContent(ctx, a.c, a.opt.Code, a.opt.Arch, true)
	if err != nil {
		return err
	}
	return os.WriteFile(a.localSourcePath, []byte(source), 0644)
}

func (a *AptConfig) CleanSourceFile() error {
	err := os.Remove(a.localSourcePath)
	if err != nil {
		return err
	}
	err = os.Remove(a.wwlSourcePath)
	if err != nil {
		return err
	}
	return nil
}

func (a *AptConfig) GetSourceFile(t SourceFileType) string {
	switch t {
	case SourceFileTypeProject:
		return a.localSourcePath
	case SourceFileTypeWWL:
		return a.wwlSourcePath
	default:
		return ""
	}
}

func ParseAptConfig(_ context.Context, path string) (*AptYamlConfig, error) {
	var (
		cfgBytes []byte
		err      error
	)
	if path != "" {
		cfgBytes, err = os.ReadFile(path)
		if err != nil {
			return nil, err
		}
	} else {
		cfgBytes = []byte(aptSource)
	}
	cfg := new(AptYamlConfig)
	if err = yaml.Unmarshal(cfgBytes, &cfg); err != nil {
		return nil, err
	}
	return cfg, nil
}

// 判断源key是否添加
func checkAptKeyIsExistAndAdd(ctx context.Context) (bool, error) {
	keyOutput, err := exec.Command("/bin/bash", "-c", "apt-key list | grep 'E72E D305 8037 B50F CA4A  FE50 D586 2A03 3456 266C' | wc -l | tr -d '\n'").Output()
	if err != nil {
		return false, err
	}
	if string(keyOutput) == "0" {
		var overlay bool
		overlay, err = utils.HasOverlay()
		if err != nil {
			return false, fmt.Errorf("check overlay error:%v", err)
		}
		cmdStr := fmt.Sprintf(`echo "%s" | sudo apt-key add`, publicKey)
		if overlay {
			err = execCmd(ctx, cmdStr, true).Run()
			if err != nil {
				return false, fmt.Errorf("add apt key underlay err:%s", err)
			}
		} else {
			err = execCmd(ctx, cmdStr, false).Run()
			if err != nil {
				return false, fmt.Errorf("add apt key err:%s", err)
			}
		}
	}
	return true, nil
}

func getSourceContent(_ context.Context, config *AptYamlConfig, linuxCodeName LinuxCodeName, arch Arch, isLocalRepo bool) (source string, err error) {
	defer func() {
		if r := recover(); r != nil {
			qlog.Errorf("parse apt config failed: %v", r)
			err = fmt.Errorf("get apt config failed:%s", r.(error))
			return
		}
	}()
	devRepoSource := fmt.Sprintf("deb [arch=%s] https://repo.qomolo.com/repository/dev/ focal main\n", arch)
	var content string
	if arch == ArchAmd64 {
		if isLocalRepo {
			content = config.Apt.Source.Amd64Local
		} else {
			content = config.Apt.Source.Amd64
			content += devRepoSource
		}
	} else if arch == ArchArm64 {
		if isLocalRepo {
			if linuxCodeName == LinuxCodeNameBionic {
				content = config.Apt.Source.Arm64BionicLocal
			} else if linuxCodeName == LinuxCodeNameFocal {
				content = config.Apt.Source.Arm64FocalLocal
			}
		} else {
			if linuxCodeName == LinuxCodeNameBionic {
				content = config.Apt.Source.Arm64Bionic
				content += devRepoSource
			} else if linuxCodeName == LinuxCodeNameFocal {
				content = config.Apt.Source.Arm64Focal
				content += devRepoSource
			}
		}
	} else {
		return "", fmt.Errorf("arch not support:%s", arch)
	}
	if content == "" {
		return "", fmt.Errorf("apt source is empty, arch: %s code: %s", arch, linuxCodeName)
	}
	return content, nil
}
