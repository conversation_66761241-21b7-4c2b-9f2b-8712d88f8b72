package qpackage

type PullDetail struct {
	Status         string         `json:"status"`
	ProgressDetail ProgressDetail `json:"progress_detail"`
	Progress       string         `json:"progress"`
	ID             string         `json:"id"`
}

type ProgressDetail struct {
	Current int64 `json:"current"`
	Total   int64 `json:"total"`
}

type PullStatus string

const (
	PullStatusStart             PullStatus = "Pull start"
	PullStatusAlreadyExists     PullStatus = "Already exists"
	PullStatusPullingFsLayer    PullStatus = "Pulling fs layer"
	PullStatusDownloading       PullStatus = "Downloading"
	PullStatusVerifyingChecksum PullStatus = "Verifying Checksum"
	PullStatusDownloadComplete  PullStatus = "Download complete"
	PullStatusExtracting        PullStatus = "Extracting"
	PullStatusPullComplete      PullStatus = "Pull complete"
	PullStatusDigest            PullStatus = "Digest"
	PullStatusFinish            PullStatus = "Pull finish"
)
