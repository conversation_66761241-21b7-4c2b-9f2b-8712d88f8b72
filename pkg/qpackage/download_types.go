package qpackage

import (
	"bufio"
	"bytes"
	"encoding/json"
	"strings"
	"time"
)

type EmptyProgress struct {
}

func (e EmptyProgress) GetPercent() float64 {
	return 0
}

func (e EmptyProgress) GetTotal() int64 {
	return 0
}

func (e EmptyProgress) GetCurrent() int64 {
	return 0
}

func (e EmptyProgress) GetImagePercent() map[string]float64 {
	return nil
}

func (e EmptyProgress) GetImageTotal() map[string]int64 {
	return nil
}

func (e EmptyProgress) GetImageCurrent() map[string]int64 {
	return nil
}

func (e EmptyProgress) GetDebPercent() float64 {
	return 0
}

func (e EmptyProgress) GetDebTotal() int64 {
	return 0
}

func (e EmptyProgress) GetDebCurrent() int64 {
	return 0
}

type GroupDownloadProgress struct {
	Name    string                    `json:"name"`
	Version string                    `json:"version"`
	Schemes []*SchemeDownloadProgress `json:"schemes,omitempty"`
	Groups  []*GroupDownloadProgress  `json:"groups,omitempty"`
}

func (g GroupDownloadProgress) GetCurrent() int64 {
	var current int64
	for _, scheme := range g.Schemes {
		current += scheme.GetCurrent()
	}
	for _, group := range g.Groups {
		current += group.GetCurrent()
	}
	return current
}

func (g GroupDownloadProgress) GetTotal() int64 {
	var total int64
	for _, scheme := range g.Schemes {
		total += scheme.GetTotal()
	}
	for _, group := range g.Groups {
		total += group.GetTotal()
	}
	return total
}

func (g GroupDownloadProgress) GetPercent() float64 {
	total := g.GetTotal()
	if total == 0 {
		return 0
	}
	return float64(g.GetCurrent()) * 100 / float64(total)
}

func (g GroupDownloadProgress) GetImagePercent() map[string]float64 {
	ip := make(map[string]float64, 0)
	ic := g.GetImageCurrent()
	it := g.GetImageTotal()
	for i, t := range it {
		if _, ok := ic[i]; ok {
			ip[i] = float64(ic[i]) / float64(t) * 100
		} else {
			ip[i] = 0
		}
	}
	return ip
}
func (g GroupDownloadProgress) GetImageCurrent() map[string]int64 {
	imageCurrent := make(map[string]int64, 0)
	for _, scheme := range g.Schemes {
		for i, c := range scheme.GetImageCurrent() {
			imageCurrent[i] = c
		}
	}
	for _, group := range g.Groups {
		for i, c := range group.GetImageCurrent() {
			imageCurrent[i] = c
		}
	}
	return imageCurrent
}
func (g GroupDownloadProgress) GetImageTotal() map[string]int64 {
	imageTotal := make(map[string]int64, 0)
	for _, scheme := range g.Schemes {
		for i, t := range scheme.GetImageTotal() {
			imageTotal[i] = t
		}
	}
	for _, group := range g.Groups {
		for i, t := range group.GetImageTotal() {
			imageTotal[i] = t
		}
	}
	return imageTotal
}

func (g GroupDownloadProgress) GetDebCurrent() int64 {
	var current int64
	for _, scheme := range g.Schemes {
		current += scheme.GetDebCurrent()
	}
	for _, group := range g.Groups {
		current += group.GetDebCurrent()
	}
	return current
}

func (g GroupDownloadProgress) GetDebTotal() int64 {
	var total int64
	for _, scheme := range g.Schemes {
		total += scheme.GetDebTotal()
	}
	for _, group := range g.Groups {
		total += group.GetDebTotal()
	}
	return total
}

func (g GroupDownloadProgress) GetDebPercent() float64 {
	total := g.GetDebTotal()
	if total == 0 {
		return 0
	}
	return float64(g.GetDebCurrent()) * 100 / float64(total)
}

type SchemeDownloadProgress struct {
	Name     string                `json:"name"`
	Version  string                `json:"version"`
	Packages []DebDownloadProgress `json:"packages,omitempty"`
}

func (s SchemeDownloadProgress) GetCurrent() int64 {
	var current int64
	current += s.GetDebCurrent()
	for _, imageCurrent := range s.GetImageCurrent() {
		current += imageCurrent
	}
	return current
}
func (s SchemeDownloadProgress) GetTotal() int64 {
	var total int64
	total += s.GetDebTotal()
	for _, imageTotal := range s.GetImageTotal() {
		total += imageTotal
	}
	return total
}
func (s SchemeDownloadProgress) GetPercent() float64 {
	total := s.GetTotal()
	if total == 0 {
		return 0
	}
	return float64(s.GetCurrent()) * 100 / float64(total)
}

func (s SchemeDownloadProgress) GetImageCurrent() map[string]int64 {
	imageCurrent := make(map[string]int64, 0)
	for _, pkg := range s.Packages {
		for _, image := range pkg.Images {
			imageCurrent[image.Image] = image.GetCurrent()
		}
	}
	return imageCurrent
}
func (s SchemeDownloadProgress) GetImageTotal() map[string]int64 {
	imageTotal := make(map[string]int64, 0)
	for _, pkg := range s.Packages {
		for _, image := range pkg.Images {
			total := image.GetTotal()
			if total > 0 {
				imageTotal[image.Image] = total
			} else {
				imageTotal[image.Image] = -1
			}
		}
	}
	return imageTotal
}
func (s SchemeDownloadProgress) GetImagePercent() map[string]float64 {
	imagePercent := make(map[string]float64, 0)
	for _, pkg := range s.Packages {
		for _, image := range pkg.Images {
			total := image.GetTotal()
			if total > 0 {
				imagePercent[image.Image] = float64(image.GetCurrent()) / float64(total) * 100
			} else {
				imagePercent[image.Image] = -1
			}
		}
	}
	return imagePercent
}

func (s SchemeDownloadProgress) GetDebCurrent() int64 {
	var current int64
	for _, pkg := range s.Packages {
		current += pkg.Current
	}
	return current
}
func (s SchemeDownloadProgress) GetDebTotal() int64 {
	var total int64
	for _, pkg := range s.Packages {
		total += pkg.Total
	}
	return total
}
func (s SchemeDownloadProgress) GetDebPercent() float64 {
	total := s.GetDebTotal()
	if total == 0 {
		return 0
	}
	return float64(s.GetDebCurrent()) * 100 / float64(total)
}

type DebDownloadProgress struct {
	Current       int64               `json:"current"`
	Total         int64               `json:"total"`
	Name          string              `json:"name"`
	Version       string              `json:"version"`
	Repo          string              `json:"repo"`
	Arch          string              `json:"arch"`
	SavePath      string              `json:"save_path"`
	LastUpdate    int64               `json:"ts"`
	FullLocalPath string              `json:"full_local_path"`
	Images        []ImagePullProgress `json:"images"`
}

func (ddp *DebDownloadProgress) saveFile() string {
	pkgFullName := ddp.Name + "_" + ddp.Version + "_" + ddp.Arch + ".deb"
	return strings.TrimRight(ddp.SavePath, "/") + "/" + pkgFullName
}

func (ddp *DebDownloadProgress) Write(p []byte) (n int, err error) {
	n = len(p)
	ddp.Current += int64(n)
	ddp.LastUpdate = time.Now().UnixMilli()
	return
}

type DockerPullMessage struct {
	Image          string        `json:"image"`
	Status         string        `json:"status"`
	ProgressDetail LayerProgress `json:"progressDetail"`
	ID             string        `json:"id"`
}

type LayerProgress struct {
	Current int64 `json:"current"`
	Total   int64 `json:"total"`
}

type ImagePullProgress struct {
	Image       string                   `json:"image"`
	ProgressMap map[string]LayerProgress `json:"progress_map"`
}

func (ipp *ImagePullProgress) Write(p []byte) (n int, err error) {
	var message DockerPullMessage
	if len(p) > 0 {
		scanner := bufio.NewScanner(bytes.NewBuffer(p))
		for scanner.Scan() {
			if err := json.Unmarshal([]byte(scanner.Text()), &message); err != nil {
				return n, err
			}

			if strings.Contains(message.Status, "Downloading") && message.ProgressDetail.Current > 0 {
				if progress, exists := ipp.ProgressMap[message.ID]; exists {
					progress.Current = message.ProgressDetail.Current
					ipp.ProgressMap[message.ID] = progress
				}
			}

			// 捕获到Pull complete即当前层已完成，即时更新current，防止current和total不一致
			if strings.Contains(message.Status, "Pull complete") {
				if progress, exists := ipp.ProgressMap[message.ID]; exists {
					progress.Current = progress.Total
					ipp.ProgressMap[message.ID] = progress
				}
			}

			// 已存在层current和Total设为0，用于进度统计
			if strings.Contains(message.Status, "Already exists") {
				if progress, exists := ipp.ProgressMap[message.ID]; exists {
					progress.Current = 0
					progress.Total = 0
					ipp.ProgressMap[message.ID] = progress
				}
			}

			if strings.Contains(message.Status, "Image is up to date") {
				for k, v := range ipp.ProgressMap {
					ipp.ProgressMap[k] = LayerProgress{Current: v.Total, Total: v.Total}
				}
			}

		}
		err = scanner.Err()
	}
	return n, err
}

func (ipp *ImagePullProgress) GetCurrent() int64 {
	var current int64
	for _, p := range ipp.ProgressMap {
		current += p.Current
	}
	return current
}
func (ipp *ImagePullProgress) GetTotal() int64 {
	var total int64
	for _, p := range ipp.ProgressMap {
		total += p.Total
	}
	return total
}
