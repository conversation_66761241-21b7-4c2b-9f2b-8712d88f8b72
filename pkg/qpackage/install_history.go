package qpackage

type ActionResourceType string
type ActionStatus string
type ActionType string

const (
	InstallAction ActionType = "install"
	RemoveAction  ActionType = "remove"
)

const (
	ActionPending ActionStatus = "pending"
	ActionSuccess ActionStatus = "success"
	ActionFailed  ActionStatus = "failed"
)

type InstallStatus struct {
	Percent float64      `json:"percent"`
	ErrCode string       `json:"err_code"`
	ErrMsg  string       `json:"err_msg"`
	Status  ActionStatus `json:"status"`
	Action  ActionType   `json:"action"`
}
