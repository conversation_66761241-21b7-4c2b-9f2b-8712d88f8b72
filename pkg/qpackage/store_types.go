package qpackage

import "time"

type PackageInfoConf struct {
	Scheme map[string]SchemeInfo `json:"scheme" yaml:"scheme"`
	Group  map[string]GroupInfo  `json:"group" yaml:"group"`
	Status PackageStatus         `json:"status" yaml:"status"` // 当前状态
}
type PackageStatus struct {
	Status  Status    `json:"status,omitempty"`
	Name    string    `json:"name,omitempty"`
	Version string    `json:"version,omitempty"`
	Type    string    `json:"type,omitempty"`
	Time    time.Time `json:"time,omitempty"`
}

type Status string

const (
	Installing Status = "installing"
	Success    Status = "success"
	Failure    Status = "failure"
)

type SchemeInfo struct {
	Name    string             `json:"name"`
	Version string             `json:"version"`
	Time    time.Time          `json:"time"`
	From    *SchemeInstallFrom `json:"from"`
}
type GroupInfo struct {
	Name    string             `json:"name"`
	Version string             `json:"version"`
	Time    time.Time          `json:"time"`
	From    *SchemeInstallFrom `json:"from"`
}

type SchemeInstallFrom struct {
	Source  string `json:"source"`
	Version string `json:"version"`
}

type Current struct {
	Name    string `json:"name"`
	Version string `json:"version"`
}
