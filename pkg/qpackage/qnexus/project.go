package qnexus

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/spf13/viper"
	"go.uber.org/zap"
)

type NexusProject struct {
	log          *zap.SugaredLogger
	client       *resty.Client
	baseDir      string
	nexusBaseURL string
	rawPath      string
}

func NewNexusProjectClient(url, rawPath string, log *zap.SugaredLogger) (QNexus, error) {
	baseDir := "integration"
	agent := resty.New().
		SetRetryCount(3).
		SetRetryWaitTime(5 * time.Second).
		SetBaseURL(url + rawPath + "/" + baseDir)
	if viper.GetBool("DEBUG") {
		agent.SetDebug(true)
	}
	return &NexusProject{
		log:          log,
		client:       agent,
		baseDir:      baseDir,
		nexusBaseURL: url,
		rawPath:      rawPath,
	}, nil
}

func (c *NexusProject) GetDebInfo(_ context.Context, name, version, _, arch, downloadURL string) (*Asset, error) {
	// downloadURL = "http://**************:8081/repository/repo/alpha/pool/main/q/qomolo-profile-project-cnshatest/qomolo-profile-project-cnshatest_0.0.294-612576_amd64.deb?md5=true"
	debName := fmt.Sprintf("/%s_%s_%s.deb", name, version, arch)
	path := strings.TrimSuffix(downloadURL, debName)
	queryURL := fmt.Sprintf("%s/?md5=true", path)

	var fileInfos []Asset
	resp, err := c.client.R().SetResult(&fileInfos).Get(queryURL)
	if err != nil {
		return nil, err
	}

	if !resp.IsSuccess() {
		return nil, err
	}

	for _, fileInfo := range fileInfos {
		if fileInfo.Name == strings.TrimPrefix(debName, "/") {
			return &fileInfo, nil
		}
	}
	return nil, err
}
