package qnexus

import (
	"fmt"
	"regexp"
	"sort"
	"strings"

	"gitlab.qomolo.com/infrastructure/sw_architecture/middleware_abstraction/utility/qlibrary/qutils"
	"gitlab.qomolo.com/qomolo_utils/qomolo_get/pkg/utils"

	"github.com/samber/lo"
)

type NexusWrap[T any] struct {
	Data T `json:"data"`
}

type NexusIntegration struct {
	Name        string                     `json:"name"`
	Version     string                     `json:"version"`
	Type        string                     `json:"type"`
	Arch        ArchType                   `json:"arch"`
	ReleaseNote string                     `json:"release_note"`
	Modules     NexusIntegrationModuleList `json:"modules"`
	Targets     SchemeTargetList           `json:"targets"` // 安装目标
	CreateTime  int64                      `json:"create_time"`
	UpdateTime  int64                      `json:"update_time"`
	Labels      CiLabels                   `json:"labels"`
}

type NexusIntegrationGroup struct {
	ID          int                        `json:"id"`         // scheme/group id
	Name        string                     `json:"name"`       // scheme name
	Type        GroupType                  `json:"type"`       // scheme/group
	Version     string                     `json:"version"`    // scheme version
	VersionID   int                        `json:"version_id"` // scheme version
	Seq         int                        `json:"seq"`        // 排序
	ReleaseNote string                     `json:"release_note"`
	Targets     SchemeTargetList           `json:"targets"` // 安装目标
	Schemes     []NexusIntegrationGroup    `json:"schemes,omitempty"`
	Modules     NexusIntegrationModuleList `json:"modules,omitempty"`
	CreateTime  int64                      `json:"create_time"`
	UpdateTime  int64                      `json:"update_time"`
	Labels      CiLabels                   `json:"labels"`
	IsDelete    bool                       `json:"is_delete"`
}

// Sort 同一个 group 层级，优先安装 profile 类型的 scheme，group
func (g *NexusIntegrationGroup) Sort() {
	// 1. profile 的 scheme 优先安装
	// 2. profile 的 group 优先安装
	// 3. 都没有 profile的scheme，group时，scheme优先
	// less 函数返回true，表示小于，返回true，则i在j之前，否则i在j之后
	sort.SliceStable(g.Schemes, func(i, j int) bool {
		si := g.Schemes[i]
		sj := g.Schemes[j]
		isSiSchemeProfile := si.Labels.ContainsKeyAndValueEqual(LabelSchemeType, LabelSchemeTypeProfile)
		isSjSchemeProfile := sj.Labels.ContainsKeyAndValueEqual(LabelSchemeType, LabelSchemeTypeProfile)
		isSiGroupProfile := si.Labels.ContainsKeyAndValueEqual(LabelGroupType, LabelGroupTypeProfile)
		isSjGroupProfile := sj.Labels.ContainsKeyAndValueEqual(LabelGroupType, LabelGroupTypeProfile)
		priorityI := getPriority(si.Type, isSiSchemeProfile || isSiGroupProfile)
		priorityJ := getPriority(sj.Type, isSjSchemeProfile || isSjGroupProfile)
		return priorityI < priorityJ
	})
}

// getPriority 根据类型和是否包含标签返回优先级
func getPriority(itemType GroupType, hasTag bool) int {
	if hasTag {
		if itemType == GroupTypeScheme {
			return 1
		}
		return 2
	}
	if itemType == GroupTypeScheme {
		return 3
	}
	return 4

}

type GroupType string

const (
	GroupTypeScheme GroupType = "scheme"
	GroupTypeGroup  GroupType = "group"
	GroupTypeModule GroupType = "module"
	GroupTypeMap    GroupType = "map"
)

type SchemeTarget struct {
	Name  string `json:"name"`
	Type  string `json:"type"`
	Value string `json:"value"`
}

type SchemeTargetList []SchemeTarget

func (s SchemeTargetList) IsAllowInstall(hostType utils.HostType, hostID int) bool {
	for _, v := range s {
		// 使用value 正则匹配
		regex, err := regexp.Compile(v.Value)
		if err != nil {
			continue
		}
		if regex.MatchString(fmt.Sprintf("%s-%d", hostType, hostID)) {
			return true
		}
	}
	return false
}

type ModuleType string

const (
	ModuleDeb ModuleType = "deb"
	ModuleRaw ModuleType = "raw"
)

func (m ModuleType) IsDeb() bool {
	return m == ModuleDeb || m == ""
}

type NexusIntegrationModuleList []NexusIntegrationModule

func (m NexusIntegrationModuleList) GetDebs() NexusIntegrationModuleList {
	return lo.Filter(m, func(v NexusIntegrationModule, _ int) bool {
		return v.ModuleType.IsDeb()
	})
}

type NexusIntegrationModule struct {
	Name       string         `json:"name"`
	PkgName    string         `json:"pkg_name"`
	Version    string         `json:"version"`
	CommitID   string         `json:"commit_id"`
	CreateTime int64          `json:"create_time"`
	Labels     CiModuleLabels `json:"labels"`
	ModuleType ModuleType     `json:"module_type"` // raw,deb 为空默认为 deb
	RepoName   string         `json:"repo_name"`
	Arch       ArchType       `json:"arch"`
	Metadata   map[string]any `json:"metadata"` // fms_interface_version
}

type Module struct {
	Name string `json:"name"`
}

type ModuleDetail struct {
	Name     string         `json:"name"`
	Version  string         `json:"version"`
	Metadata ModuleMetadata `json:"metadata"`
}

type ModuleMetadata struct {
	Time        qutils.Time `json:"time"`
	ReleaseNote string      `json:"release_note"`
	Labels      CiLabels    `json:"labels"`
}

type ModuleDetailList []ModuleDetail

func (m ModuleDetailList) Len() int {
	return len(m)
}

func (m ModuleDetailList) Less(i, j int) bool {
	// 版本号 2.10.1 比较 2.10.2
	vi, err := NewModuleVersion(m[i].Version)
	if err != nil {
		return false
	}
	vj, err := NewModuleVersion(m[j].Version)
	if err != nil {
		return false
	}
	return vi.GetCode() >= vj.GetCode()
}
func (m ModuleDetailList) Swap(i, j int) {
	m[i], m[j] = m[j], m[i]
}

type ArchType string

const (
	ArchAll   ArchType = "all"
	ArchAmd64 ArchType = "amd64"
	ArchArm64 ArchType = "arm64"
)

type SchemeDataWrap struct {
	Data []SchemeData `json:"data"`
}

type SchemeData struct {
	Name string `json:"name"`
}

type SchemeGroupRelease struct {
	Name        string                     `json:"name"`
	Project     []CiSchemeGroupProject     `json:"project"`
	Profile     []CiSchemeGroupProfile     `json:"profile"`
	VehicleType []CiSchemeGroupVehicleType `json:"vehicle_type"`
	Schemes     []CiSchemeGroupDependency  `json:"schemes"`
	CreateTime  int64                      `json:"create_time"`
	UpdateTime  int64                      `json:"update_time"`
	Labels      CiLabels                   `json:"labels"`
}

type CiSchemeGroupDependency struct {
	ID   int       `json:"id"`   // scheme/group id
	Type GroupType `json:"type"` // scheme/group
	Name string    `json:"name"`
}

type CiSchemeGroupProject struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}
type CiSchemeGroupProfile struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}
type CiSchemeGroupVehicleType struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}

type CiLabels []Label

func (l CiLabels) Contains(key, val string) bool {
	for _, v := range l {
		if v.Key == key && v.Value == val {
			return true
		}
	}
	return false
}

/*
*如果不包含key，返回 false
*如果包含key，并且值相等，返回true
 */
func (l CiLabels) ContainsKeyAndValueEqual(key, val string) bool {
	for _, v := range l {
		if v.Key == key {
			return v.Value == val
		}
	}
	return false
}

func (l CiLabels) ContainsKey(key string) bool {
	for _, v := range l {
		if v.Key == key {
			return true
		}
	}
	return false
}
func (l CiLabels) String() string {
	res := ""
	for _, v := range l {
		res += fmt.Sprintf("%s=%s,", v.Key, v.Value)
	}
	return res
}

type Label struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

func (l *Label) ValueToArray() []string {
	if l.Value == "" {
		return nil
	}
	return strings.Split(l.Value, ",")
}

type linuxCodeName = string

const (
	// nolint
	linuxCodeNameFocal linuxCodeName = "focal"
	// nolint
	linuxCodeNameBionic linuxCodeName = "bionic"
)

// https://confluence.westwell-lab.com/pages/viewpage.action?pageId=29168555#DevOps2.0集成系统方案设计-模块标签

const (
	moduleOnlyInstall                  = "module/only-install-on"
	moduleOnlyUpgrade                  = "module/only-upgrade"
	moduleDpkgInstall                  = "module/dpkg-install"
	moduleDpkgInstallWithSystem        = "module/dpkg-install-with-system"
	moduleEnvPath                      = "module/env-path"
	moduleRequiredGwReload             = "required-gw-reload"
	moduleRequiredReboot               = "required-reboot"
	moduleRequiredRestartProfileLabels = "required-restart-profile-labels"
	moduleRequiredRestartProfilePkgs   = "required-restart-profile-pkgs"
)

const (
	LabelProject                 = "__project"
	LabelChassis                 = "__chassis"
	LabelTarget                  = "__target"
	LabelVehicleType             = "__vehicle_type"
	LabelModelDCU                = "module/dcu"
	LabelTargetValueServer       = "server"
	LabelTargetValueVehicle      = "vehicle"
	LabelInSysDisableSelfUpgrade = "server/in-sys-disable-self-upgrade"
	LabelModelType               = "__module_type"
	LabelSchemeType              = "__scheme_type" // profile
	LabelGroupType               = "__group_type"  // profile
)

const (
	LabelSchemeTypeProfile = "profile"
)
const (
	LabelGroupTypeProfile = "profile"
)

type CiModuleLabels []Label

type AllowInstallArgs struct {
	CodeName    linuxCodeName
	ModelDCU    utils.ModelDCU
	ProjectName string
	VehicleType utils.VehicleType
	IsUnderlay  bool
	Chassis     string // 底盘型号
}

// IsAllowInstall 用于判断 label 的值是否符合要求
// 目前 LabelTarget 没有判断的需求和条件，所以在 有值 的情况下返回 true
func (l CiModuleLabels) IsAllowInstall(args AllowInstallArgs) bool {
	for _, v := range l {
		if len(v.Value) == 0 {
			return false
		}
		switch v.Key {
		case moduleOnlyInstall:
			if !lo.Contains(v.ValueToArray(), args.CodeName) {
				return false
			}
		case LabelProject:
			if !lo.Contains(v.ValueToArray(), args.ProjectName) {
				return false
			}
		case LabelChassis:
			if !lo.Contains(v.ValueToArray(), string(args.Chassis)) {
				return false
			}
		case LabelModelDCU:
			if args.ModelDCU == "" || !lo.Contains(v.ValueToArray(), string(args.ModelDCU)) {
				return false
			}
		case LabelVehicleType:
			if !lo.Contains(v.ValueToArray(), string(args.VehicleType)) {
				return false
			}
		default:
		}
	}

	if l.HasDpkgInstall() {
		// qomolo-service 只在 overlay 上安装
		return !args.IsUnderlay
	}

	return true
}

func (l CiModuleLabels) HasOnlyUpgrade() bool {
	for _, v := range l {
		switch v.Key {
		case moduleOnlyUpgrade:
			return true
		default:
		}
	}
	return false
}

func (l CiModuleLabels) HasDpkgInstall() bool {
	for _, v := range l {
		switch v.Key {
		case moduleDpkgInstall:
			return true
		default:
		}
	}
	return false
}

func (l CiModuleLabels) HasDpkgInstallWithSystem() bool {
	for _, v := range l {
		switch v.Key {
		case moduleDpkgInstallWithSystem:
			return true
		default:
		}
	}
	return false
}

func (l CiModuleLabels) GetEnvPath() string {
	for _, v := range l {
		switch v.Key {
		case moduleEnvPath:
			return v.Value
		default:
		}
	}
	return ""
}

func (l CiModuleLabels) AllowServiceInstall(isUnderlay bool) bool {
	if l.HasDpkgInstall() || l.HasDpkgInstallWithSystem() {
		// qomolo-service 只在 overlay 上安装
		return !isUnderlay
	}
	return true
}

func (l CiModuleLabels) IsRequiredGwReload() bool {
	for _, v := range l {
		if v.Key == moduleRequiredGwReload {
			return strings.ToLower(v.Value) == "true"
		}
	}
	return false
}

func (l CiModuleLabels) IsRequiredReboot() bool {
	for _, v := range l {
		if v.Key == moduleRequiredReboot {
			return strings.ToLower(v.Value) == "true"
		}
	}
	return false
}

func (l CiModuleLabels) GetRequiredRestartProfileLabels() []string {
	labels := make([]string, 0)
	for _, v := range l {
		if v.Key == moduleRequiredRestartProfileLabels {
			labels = append(labels, strings.Split(v.Value, ",")...)
		}
	}
	return labels
}

func (l CiModuleLabels) GetRequiredRestartProfilePkgs() []string {
	pkgs := make([]string, 0)
	for _, v := range l {
		if v.Key == moduleRequiredRestartProfilePkgs {
			pkgs = append(pkgs, strings.Split(v.Value, ",")...)
		}
	}
	return pkgs
}

type ModuleTypeLabel string

const (
	ModuleTypeLabelSw      ModuleTypeLabel = "sw"
	ModuleTypeLabelHw      ModuleTypeLabel = "hw"
	ModuleTypeLabelProfile ModuleTypeLabel = "profile"
)

func (l CiModuleLabels) GetModuleTypeLabel() ModuleTypeLabel {
	for _, v := range l {
		if v.Key == LabelModelType {
			return ModuleTypeLabel(v.Value)
		}
	}
	// 默认sw
	return ModuleTypeLabelSw
}

const (
	moduleXBase     = 100000000
	moduleYBase     = 100000
	moduleZBase     = 1
	moduleBuildBase = 1
)

type ModuleVersion struct {
	x, y, z, buildID int64
}

func (v *ModuleVersion) GetCode() int64 {
	// build id 总是递增的
	// z增加时,build也会增加,所以z和build的位数可以合并,不会冲突,但是不能通过版本code转成版本号
	return int64(v.x*moduleXBase + v.y*moduleYBase + v.z*moduleZBase + v.buildID*moduleBuildBase)
}

// 版本解析 0.2.122-98157
func NewModuleVersion(version string) (*ModuleVersion, error) {
	var x, y, z, buildID int64
	_, err := fmt.Sscanf(version, "%d.%d.%d-%d", &x, &y, &z, &buildID)
	if err != nil {
		return nil, err
	}
	return &ModuleVersion{x, y, z, buildID}, nil
}

// QDCF qomolo debian control file
type QDCF struct {
	Package        string         `json:"package,omitempty"`
	Version        string         `json:"version,omitempty"`
	Architecture   ArchType       `json:"architecture,omitempty"`
	RequiredImages []string       `json:"required_images,omitempty"`
	Labels         CiModuleLabels `json:"labels,omitempty"`
}

func (qdcf *QDCF) ConvertFrom(m map[string]string) {
	for key, value := range m {
		switch key {
		case "Package":
			qdcf.Package = value
		case "Version":
			qdcf.Version = value
		case "Architecture":
			qdcf.Architecture = ArchType(value)
		case "Required-Images":
			qdcf.RequiredImages = utils.RemoveEmpty(strings.Split(value, ","))
		case "Labels":
			for _, label := range strings.Split(value, ";") {
				if strings.Contains(label, "=") {
					labelKey := strings.SplitN(label, "=", 2)[0]
					labelValue := strings.SplitN(label, "=", 2)[1]
					qdcf.Labels = append(qdcf.Labels, Label{Key: labelKey, Value: labelValue})
				}
			}
		}
	}
}

type ModuleDownloadProgress struct {
	Name    string                       `json:"name"`
	Version string                       `json:"version"`
	Files   []utils.FileDownloadProgress `json:"files"`
}

func (m ModuleDownloadProgress) GetCurrent() int64 {
	var current int64
	for _, file := range m.Files {
		current += file.Current
	}
	return current
}
func (m ModuleDownloadProgress) GetTotal() int64 {
	var total int64
	for _, file := range m.Files {
		total += file.Total
	}
	return total
}
func (m ModuleDownloadProgress) GetPercent() float64 {
	total := m.GetTotal()
	if total == 0 {
		return 0
	}
	return float64(m.GetCurrent()) * 100 / float64(total)
}

func (m ModuleDownloadProgress) GetDebCurrent() int64 {
	return 0
}

func (m ModuleDownloadProgress) GetDebTotal() int64 {
	return 0
}

func (m ModuleDownloadProgress) GetDebPercent() float64 {
	return 0.0
}

func (m ModuleDownloadProgress) GetImageCurrent() map[string]int64 {
	return nil
}

func (m ModuleDownloadProgress) GetImageTotal() map[string]int64 {
	return nil
}

func (m ModuleDownloadProgress) GetImagePercent() map[string]float64 {
	return nil
}
