package qnexus

type CoreuiBase struct {
	Action string `json:"action"`
	Method Method `json:"method"`
	Type   string `json:"type"`
	Tid    int    `json:"tid"`
}

type BrowseDataItem struct {
	ID          string `json:"id"`
	Text        string `json:"text"`
	Type        string `json:"type"`
	Leaf        bool   `json:"leaf"`
	ComponentID string `json:"componentId"`
	AssetID     string `json:"assetId"`
	PackageURL  string `json:"packageUrl"`
}

type RepositoryItem struct {
	ID             string `json:"id"`
	RepositoryName string `json:"repositoryName"`
	Group          string `json:"group"`
	Name           string `json:"name"`
	Version        string `json:"version"`
	Format         string `json:"format"`
}

type BrowseRequestData struct {
	Node           string `json:"node"`
	RepositoryName string `json:"repositoryName"`
}

type BrowseRequest struct {
	CoreuiBase
	Data []BrowseRequestData `json:"data"`
}

type BrowseResult struct {
	Success bool             `json:"success"`
	Data    []BrowseDataItem `json:"data"`
}

type BrowseResponse struct {
	CoreuiBase
	Result BrowseResult `json:"result"`
}

type ComponentRequest struct {
	CoreuiBase
	Data []string `json:"data"`
}

type ReadComponentResult struct {
	Success bool           `json:"success"`
	Data    RepositoryItem `json:"data"`
}

type ReadComponentResponse struct {
	CoreuiBase
	Result ReadComponentResult `json:"result"`
}

type AssetAttributesChecksum struct {
	Sha1   string `json:"sha1"`
	Sha256 string `json:"sha256"`
	Sha512 string `json:"sha512"`
	Md5    string `json:"md5"`
}

type AssetAttributesContent struct {
	LastModified string `json:"last_modified"`
}

type AssetAttributesProvenance struct {
	HashesNotVerified bool `json:"hashes_not_verified"`
}

type AssetAttributesApt struct {
	PackageName    string `json:"package_name"`
	PackageVersion string `json:"package_version"`
	IndexSection   string `json:"index_section"`
	AssetKind      string `json:"asset_kind"`
	Architecture   string `json:"architecture"`
}

type AssetAttributes struct {
	Checksum   AssetAttributesChecksum   `json:"checksum"`
	Content    AssetAttributesContent    `json:"content"`
	Provenance AssetAttributesProvenance `json:"provenance"`
	Apt        AssetAttributesApt        `json:"apt"`
}

type AssetItem struct {
	ID                       string          `json:"id"`
	Name                     string          `json:"name"`
	Format                   string          `json:"format"`
	ContentType              string          `json:"contentType"`
	Size                     int64           `json:"size"`
	RepositoryName           string          `json:"repositoryName"`
	ContainingRepositoryName string          `json:"containingRepositoryName"`
	BlobCreated              string          `json:"blobCreated"`
	BlobUpdated              string          `json:"blobUpdated"`
	LastDownloaded           string          `json:"lastDownloaded"`
	BlobRef                  string          `json:"blobRef"`
	ComponentID              string          `json:"componentId"`
	CreateBy                 string          `json:"createdBy"`
	CreateByIP               string          `json:"createdByIp"`
	Attributes               AssetAttributes `json:"attributes"`
}

type ReadAssetResult struct {
	Success bool      `json:"success"`
	Data    AssetItem `json:"data"`
}

type ReadAssetResponse struct {
	CoreuiBase
	Result ReadAssetResult `json:"result"`
}

type NexusRawFile struct {
	Name   string `json:"name"`
	Link   string `json:"link"`
	Path   string `json:"path"`
	Size   int64  `json:"size"`
	Sha256 string `json:"sha256"`
	Md5    string `json:"md5"`
}

type RawFileList struct {
	Folders []string       `json:"folders"`
	Assets  []NexusRawFile `json:"Files"`
}
