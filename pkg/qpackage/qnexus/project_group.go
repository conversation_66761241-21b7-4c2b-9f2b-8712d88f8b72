package qnexus

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"sort"
	"strings"
	"time"

	"golang.org/x/xerrors"
)

func (c *NexusProject) GetGroupListByFile(_ context.Context) (groupList []SchemeGroupRelease, err error) {
	url := "/group/scheme-group-release.json"
	resp, err := c.client.R().Get(url)
	if err != nil {
		c.log.Errorf("GetSchemeGroupList %v", err)
		return nil, xerrors.New("GetSchemeGroupList error")
	}
	wrap := NexusWrap[[]SchemeGroupRelease]{}
	err = json.Unmarshal(resp.Body(), &wrap)
	if err != nil {
		c.log.Errorf("GetSchemeGroupList %v body:%s", err, string(resp.Body()))
		return nil, xerrors.Errorf("parse err %s", err)
	}
	return wrap.Data, nil
}

func (c *NexusProject) GetGroupVersionInfo(_ context.Context, name, version string) (info *NexusIntegrationGroup, err error) {
	c.log.Infof("name: %v\n", name)
	c.log.Infof("version: %v\n", version)
	url := fmt.Sprintf("/group/%s/%s/%s-%s.json", name, version, name, version)
	c.log.Infof("url: %v\n", url)
	resp, err := c.client.R().Get(url)
	if err != nil {
		return nil, errors.New("GetSchemeGroupVersionInfo err")
	}
	if resp.IsError() {
		if resp.StatusCode() == http.StatusNotFound {
			return nil, fmt.Errorf("%s %s not found", name, version)
		}
		return nil, fmt.Errorf("request error code: %d", resp.StatusCode())
	}
	wrap := NexusWrap[NexusIntegrationGroup]{}
	err = json.Unmarshal(resp.Body(), &wrap)
	if err != nil {
		c.log.Errorf("GetSchemeGroupVersionInfo %v body: %s", err, string(resp.Body()))
		return nil, xerrors.Errorf("parse err %s", err)
	}
	return &wrap.Data, nil
}

func (c *NexusProject) GetGroupVersionListByFile(_ context.Context, name string) (list []NexusIntegrationGroup, err error) {
	url := fmt.Sprintf("/group/%s/%s-release.json", name, name)
	resp, err := c.client.R().Get(url)
	if err != nil {
		c.log.Errorf("GetSchemeGroupVersionInfo %v", err)
		return nil, xerrors.New("GetSchemeGroupVersionList err")
	}
	if resp.IsError() {
		if resp.StatusCode() == http.StatusNotFound {
			return nil, errors.New("scheme group release not found")
		}
		return nil, xerrors.Errorf("request err code:%d", resp.StatusCode())
	}
	wrap := NexusWrap[[]NexusIntegrationGroup]{}
	err = json.Unmarshal(resp.Body(), &wrap)
	if err != nil {
		return nil, xerrors.Errorf("parse err %s", err)
	}

	return wrap.Data, nil
}

func (c *NexusProject) GetGroupList(ctx context.Context) ([]SchemeGroupRelease, error) {
	groupList, err := c.GetGroupListByFile(ctx)
	if err != nil {
		return nil, err
	}
	list := make([]SchemeGroupRelease, 0)

	dataMap, err := c.nginxCheckFileIsExist("/group/", "directory")
	if err != nil {
		return nil, err
	}
	for _, v := range groupList {
		if _, ok := dataMap[v.Name]; ok {
			list = append(list, v)
		}
	}

	return list, nil
}

type NginxFileType string

const (
	NginxFile = "file"
	NginxDir  = "directory"
)

type DirectoryInfo struct {
	Name string        `omitempty,json:"name"`
	Type NginxFileType `omitempty,json:"type"`
	// 修改时间 Tue, 20 Aug 2024 11:15:36 GMT
	Mtime string `omitempty,json:"mtime"`
	Size  int    `omitempty,json:"size"`
	Md5   string `omitempty,json:"md5"`
}

func (d DirectoryInfo) GetMtime() time.Time {
	// Tue, 20 Aug 2024 11:15:36 GMT
	t, err := time.Parse("Mon, 02 Jan 2006 15:04:05 GMT", d.Mtime)
	if err != nil {
		return time.Time{}
	}
	return t
}

func (c *NexusProject) nginxCheckFileIsExist(url string, typ NginxFileType) (map[string]DirectoryInfo, error) {
	if typ == NginxDir {
		if !strings.HasSuffix(url, "/") {
			url += "/"
		}
	}
	info, err := c.getPathInfo(url)
	if err != nil {
		return nil, err
	}
	dataMap := make(map[string]DirectoryInfo)
	for _, v := range info {
		if v.Type == typ {
			dataMap[v.Name] = v
		}
	}
	return dataMap, nil
}

func (c *NexusProject) getPathInfo(url string) (res []DirectoryInfo, err error) {
	resp, err := c.client.R().Get(url)
	if err != nil {
		c.log.Errorf("checkFileIsExist %v url:%s path:%s", err, resp.Request.URL, url)
		return nil, xerrors.Errorf("checkFileIsExist error:%v, url:%s", err, url)
	}
	data := make([]DirectoryInfo, 0)
	err = json.Unmarshal(resp.Body(), &data)
	if err != nil {
		c.log.Errorf("checkFileIsExist %v body:%s", err, string(resp.Body()))
		return nil, xerrors.Errorf("parse body err url:%s", url)
	}
	return data, nil
}

func (c *NexusProject) GetGroupVersionList(ctx context.Context, name string) ([]NexusIntegrationGroup, error) {
	list, err := c.GetGroupVersionListByFile(ctx, name)
	if err != nil {
		return nil, err
	}
	versionMap := make(map[string]struct{})
	versionList := make([]NexusIntegrationGroup, 0)

	url := fmt.Sprintf("group/%s/", name)
	dataMap, err := c.nginxCheckFileIsExist(url, NginxDir)
	if err != nil {
		return nil, err
	}
	for _, v := range list {
		if _, ok := dataMap[v.Version]; ok {
			versionMap[v.Version] = struct{}{}
			versionList = append(versionList, v)
		}
	}
	// 防止在 release 中不存在，但版本本身存在
	for v, k := range dataMap {
		if _, ok := versionMap[v]; ok {
			continue
		}
		info, err1 := c.GetGroupVersionInfo(ctx, name, k.Name)
		if err1 != nil {
			continue
		}
		versionList = append(versionList, *info)
	}
	sort.Slice(versionList, func(i, j int) bool {
		return versionList[i].CreateTime > versionList[j].CreateTime
	})
	return versionList, nil
}
