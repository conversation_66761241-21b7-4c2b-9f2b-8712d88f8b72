package qnexus

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"

	"golang.org/x/xerrors"
)

func (c *NexusWWL) GetSchemeListByFile(_ context.Context) (schemeList []SchemeData, err error) {
	resp, err := c.client.R().Get("/scheme/scheme-release.json")
	if err != nil {
		c.log.Errorf("GetSchemeList %v", err)
		return nil, xerrors.New("GetSchemeList err")
	}
	if resp.IsError() {
		if resp.StatusCode() == http.StatusNotFound {
			return nil, xerrors.New("scheme file not found")
		}
		return nil, xerrors.Errorf("request err %d", resp.StatusCode())
	}
	wrap := NexusWrap[[]SchemeData]{}
	err = json.Unmarshal(resp.Body(), &wrap)
	if err != nil {
		c.log.Errorf("GetSchemeList %v body:%s", err, string(resp.Body()))
		return nil, xerrors.Errorf("parse error %s", err)
	}

	return wrap.Data, nil
}

func (c *NexusWWL) GetSchemeVersionInfo(_ context.Context, name, version string) (info *NexusIntegration, err error) {
	url := fmt.Sprintf("/scheme/%s/%s/%s-%s.json", name, version, name, version)
	resp, err := c.client.R().Get(url)
	if err != nil {
		c.log.Errorf("GetSchemeVersionInfo %v", err)
		return nil, xerrors.New("GetSchemeVersionInfo err")
	}
	if resp.IsError() {
		if resp.StatusCode() == http.StatusNotFound {
			return nil, xerrors.Errorf("%s %s not found", name, version)
		}
		return nil, xerrors.Errorf("request err %d", resp.StatusCode())
	}
	wrap := NexusWrap[NexusIntegration]{}

	err = json.Unmarshal(resp.Body(), &wrap)
	if err != nil {
		c.log.Errorf("GetSchemeVersionInfo %v body:%s", err, string(resp.Body()))
		return nil, xerrors.Errorf("parse error %s", err)
	}
	return &wrap.Data, nil
}

func (c *NexusWWL) GetSchemeVersionListByFile(_ context.Context, name string) (list []NexusIntegration, err error) {
	url := fmt.Sprintf("/scheme/%s/%s-release.json", name, name)
	resp, err := c.client.R().Get(url)
	if err != nil {
		c.log.Errorf("GetSchemeVersionList %v", err)
		return nil, xerrors.New("GetSchemeVersionList err")
	}
	if resp.IsError() {
		if resp.StatusCode() == http.StatusNotFound {
			return nil, errors.New("scheme release not found")
		}
		return nil, xerrors.Errorf("request err %d", resp.StatusCode())
	}
	wrap := NexusWrap[[]NexusIntegration]{}
	err = json.Unmarshal(resp.Body(), &wrap)
	if err != nil {
		c.log.Errorf("GetSchemeVersionList %v body:%s", err, string(resp.Body()))
		return nil, xerrors.Errorf("parse err %s", err)
	}
	return wrap.Data, nil
}

func (c *NexusWWL) GetSchemeList(ctx context.Context) ([]SchemeData, error) {
	list, err := c.GetSchemeListByFile(ctx)
	if err != nil {
		return nil, err
	}
	res := make([]SchemeData, 0)

	m, err := c.parseHTML(c.repoURL, "integration/scheme")
	if err != nil {
		return nil, err
	}
	for _, v := range list {
		if _, ok := m[v.Name]; ok {
			res = append(res, v)
		}
	}

	return res, nil
}

func (c *NexusWWL) GetSchemeVersionList(ctx context.Context, name string) ([]NexusIntegration, error) {
	list, err := c.GetSchemeVersionListByFile(ctx, name)
	if err != nil {
		return nil, err
	}
	versionList := make([]NexusIntegration, 0)

	m, err := c.parseHTML(c.repoURL, "integration/scheme/"+name)
	if err != nil {
		return nil, err
	}
	for _, v := range list {
		if _, ok := m[v.Version]; ok {
			versionList = append(versionList, v)
		}
	}

	return versionList, nil
}
