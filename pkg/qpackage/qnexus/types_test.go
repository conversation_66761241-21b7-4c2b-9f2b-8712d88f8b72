package qnexus

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"gitlab.qomolo.com/qomolo_utils/qomolo_get/pkg/utils"
)

func TestIsAllowInstallByLabel(t *testing.T) {
	for _, tt := range []struct {
		name          string
		labels        CiModuleLabels
		linuxCodeName linuxCodeName
		modelDCU      utils.ModelDCU
		projectName   string
		vehType       string
		isUnderlay    bool
		expected      bool
	}{
		{
			name: "test-0",
			labels: CiModuleLabels{
				{Key: moduleOnlyInstall, Value: "focal,abc"},
				{Key: LabelProject, Value: "p1,p2"},
			},
			linuxCodeName: "abc",
			projectName:   "p2",
			expected:      true,
		},
		{
			name: "test-1",
			labels: CiModuleLabels{
				{Key: moduleOnlyInstall, Value: "focal"},
				{Key: LabelProject, Value: "p1,p2"},
			},
			linuxCodeName: "focal",
			projectName:   "p1",
			expected:      true,
		},
		{
			name: "test-2",
			labels: CiModuleLabels{
				{Key: moduleOnlyInstall, Value: "focal"},
				{Key: LabelProject, Value: "p2"},
			},
			linuxCodeName: "focal",
			projectName:   "p1",
			expected:      false,
		},
		{
			name: "test-3",
			labels: CiModuleLabels{
				{Key: moduleOnlyInstall, Value: "focal"},
				{Key: LabelProject, Value: ""},
			},
			linuxCodeName: "focal",
			projectName:   "p1",
			expected:      false,
		},
		{
			name: "test-4",
			labels: CiModuleLabels{
				{Key: moduleOnlyInstall, Value: "abcde"},
				{Key: LabelProject, Value: "p1"},
			},
			linuxCodeName: "focal",
			projectName:   "p1",
			expected:      false,
		},
		{
			name: "test-5",
			labels: CiModuleLabels{
				{Key: moduleOnlyInstall, Value: ""},
				{Key: LabelProject, Value: "p1"},
			},
			linuxCodeName: "focal",
			projectName:   "p1",
			expected:      false,
		},
		{
			name: "test-6",
			labels: CiModuleLabels{
				{Key: moduleOnlyInstall, Value: ""},
				{Key: LabelProject, Value: ""},
			},
			linuxCodeName: "focal",
			projectName:   "p1",
			expected:      false,
		},
		{
			name: "test-7",
			labels: CiModuleLabels{
				{Key: LabelProject, Value: ""},
				{Key: moduleOnlyInstall, Value: ""},
			},
			linuxCodeName: "focal",
			projectName:   "p1",
			expected:      false,
		},
		{
			name: "test-8",
			labels: CiModuleLabels{
				{Key: moduleOnlyInstall, Value: "focal,abc"},
				{Key: LabelProject, Value: "p1,p2"},
			},
			linuxCodeName: "qwe",
			projectName:   "p3",
			expected:      false,
		},
		{
			name: "test-9",
			labels: CiModuleLabels{
				{Key: moduleOnlyInstall, Value: ",  "},
				{Key: LabelProject, Value: ",  "},
			},
			linuxCodeName: "qwe",
			projectName:   "p3",
			expected:      false,
		},
		{
			name: "test-10",
			labels: CiModuleLabels{
				{Key: moduleOnlyInstall, Value: " "},
				{Key: LabelProject, Value: " "},
			},
			linuxCodeName: "qwe",
			projectName:   "p3",
			expected:      false,
		},
		{
			name:          "test-11",
			labels:        CiModuleLabels{},
			linuxCodeName: "qwe",
			projectName:   "p3",
			expected:      true,
		},
		{
			name: "test-12",
			labels: CiModuleLabels{
				{Key: LabelTarget, Value: "123"},
			},
			linuxCodeName: "qwe",
			projectName:   "p3",
			expected:      true,
		},
		{
			name: "test-13",
			labels: CiModuleLabels{
				{Key: LabelProject, Value: "p3"},
			},
			linuxCodeName: "qwe",
			projectName:   "p3",
			expected:      true,
		},
		{
			name: "test-14",
			labels: CiModuleLabels{
				{Key: LabelProject, Value: ""},
			},
			linuxCodeName: "qwe",
			projectName:   "p3",
			expected:      false,
		},
		{
			name: "test-15",
			labels: CiModuleLabels{
				{Key: LabelTarget, Value: ""},
			},
			linuxCodeName: "qwe",
			projectName:   "p3",
			expected:      false,
		},
		{
			// 目前 LabelTarget 没有判断的需求和条件，所以在 有值 的情况下返回 true
			name: "test-16",
			labels: CiModuleLabels{
				{Key: LabelTarget, Value: ", "},
			},
			linuxCodeName: "qwe",
			projectName:   "p3",
			expected:      true,
		},
		{
			name: "test-17",
			labels: CiModuleLabels{
				{Key: LabelModelDCU, Value: "xav-iiplus,xav-ii,orin-ad10"},
			},
			modelDCU: "xav-iiplus",
			expected: true,
		},
		{
			name: "test-18",
			labels: CiModuleLabels{
				{Key: LabelModelDCU, Value: "xav-iiplus,xav-ii,orin-ad10"},
			},
			modelDCU: "",
			expected: false,
		},
		{
			name: "test-19",
			labels: CiModuleLabels{
				{Key: LabelModelDCU, Value: "xav-iiplus"},
			},
			modelDCU: "orin-ad10",
			expected: false,
		},
		{
			name: "test-20",
			labels: CiModuleLabels{
				{Key: LabelModelDCU, Value: ""},
			},
			modelDCU: "orin-ad10",
			expected: false,
		},
		{
			name: "test-21",
			labels: CiModuleLabels{
				{Key: LabelModelDCU, Value: ""},
			},
			modelDCU: "",
			expected: false,
		},
		{
			name: "test-22 vehType install ",
			labels: CiModuleLabels{
				{Key: LabelVehicleType, Value: "igvaa"},
			},
			modelDCU: "",
			vehType:  "igvaa",
			expected: true,
		},
		{
			name: "test-23 vehType skip",
			labels: CiModuleLabels{
				{Key: LabelVehicleType, Value: "igvaa"},
			},
			modelDCU: "",
			vehType:  "igvab",
			expected: false,
		},
		{
			name: "test-24 vehType project install",
			labels: CiModuleLabels{
				{Key: LabelProject, Value: "p3"},
				{Key: LabelVehicleType, Value: "igvaa"},
			},
			modelDCU:    "",
			vehType:     "igvaa",
			projectName: "p3",
			expected:    true,
		},
		{
			name: "test-24 vehType project skip",
			labels: CiModuleLabels{
				{Key: LabelProject, Value: "p3"},
				{Key: LabelVehicleType, Value: "igvaa"},
			},
			modelDCU:    "",
			vehType:     "igvaa",
			projectName: "p4",
			expected:    false,
		},
		{
			name: "test-25 dpkg install overlay install",
			labels: CiModuleLabels{
				{Key: LabelProject, Value: "p3"},
				{Key: LabelVehicleType, Value: "igvaa"},
				{Key: moduleDpkgInstall, Value: "true"},
			},
			modelDCU:    "",
			vehType:     "igvaa",
			projectName: "p3",
			expected:    true,
			isUnderlay:  false,
		},
		{
			name: "test-26 dpkg install underlay install",
			labels: CiModuleLabels{
				{Key: LabelProject, Value: "p3"},
				{Key: LabelVehicleType, Value: "igvaa"},
				{Key: moduleDpkgInstall, Value: "true"},
			},
			modelDCU:    "",
			vehType:     "igvaa",
			projectName: "p3",
			expected:    false,
			isUnderlay:  true,
		},
	} {
		t.Run(tt.name, func(t *testing.T) {
			args := AllowInstallArgs{
				CodeName:     tt.linuxCodeName,
				ModelDCU:     tt.modelDCU,
				ProjectName:  tt.projectName,
				VehicleType:  utils.VehicleType(tt.vehType),
				IsUnderlay:   tt.isUnderlay,
			}
			if got := tt.labels.IsAllowInstall(args); got != tt.expected {
				t.Errorf("IsAllowInstallByLabel() = %v, want %v", got, tt.expected)
			}
		})
	}
}

// 构造一个测试用例
func newTestNexusIntegrationGroup(schemes []NexusIntegrationGroup) *NexusIntegrationGroup {
	group := &NexusIntegrationGroup{}
	group.Schemes = schemes
	return group
}

// 构造一个带有特定标签的测试模式
func newTestScheme(name string, typ GroupType, labels CiLabels) NexusIntegrationGroup {
	return NexusIntegrationGroup{
		Name:   name,
		Type:   typ,
		Labels: labels,
	}
}

// 测试 Sort 方法
func TestNexusIntegrationGroup_Sort(t *testing.T) {
	// 定义测试案例
	tests := []struct {
		name     string
		schemes  []NexusIntegrationGroup
		expected []string
	}{
		{
			name: "1 group fist",
			schemes: []NexusIntegrationGroup{
				newTestScheme("group-1", GroupTypeGroup, nil),
				newTestScheme("scheme-1", GroupTypeScheme, nil),
				newTestScheme("group-2", GroupTypeGroup, CiLabels{
					{Key: LabelGroupType, Value: LabelGroupTypeProfile},
				}),
				newTestScheme("scheme-2", GroupTypeScheme, CiLabels{
					{Key: LabelSchemeType, Value: LabelSchemeTypeProfile},
				}),
				newTestScheme("group-3", GroupTypeGroup, CiLabels{
					{Key: LabelGroupType, Value: LabelGroupTypeProfile},
				}),
			},
			expected: []string{"scheme-2", "group-2", "group-3", "scheme-1", "group-1"},
		},
		{
			name: "2 group last",
			schemes: []NexusIntegrationGroup{
				newTestScheme("scheme-1", GroupTypeScheme, nil),
				newTestScheme("scheme-2", GroupTypeScheme, CiLabels{
					{Key: LabelSchemeType, Value: LabelSchemeTypeProfile},
				}),
				newTestScheme("group-2", GroupTypeGroup, nil),
				newTestScheme("scheme-4", GroupTypeScheme, CiLabels{
					{Key: LabelSchemeType, Value: LabelSchemeTypeProfile},
				}),
				newTestScheme("group-1", GroupTypeGroup, nil),
				newTestScheme("group-3", GroupTypeGroup, CiLabels{
					{Key: LabelGroupType, Value: LabelGroupTypeProfile},
				}),
			},
			expected: []string{"scheme-2", "scheme-4", "group-3", "scheme-1", "group-2", "group-1"},
		},
	}

	// 运行测试案例
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			group := newTestNexusIntegrationGroup(test.schemes)
			group.Sort()
			values := make([]string, 0)
			for i := range group.Schemes {
				values = append(values, group.Schemes[i].Name)
			}
			assert.Equal(t, test.expected, values)
		})
	}
}
