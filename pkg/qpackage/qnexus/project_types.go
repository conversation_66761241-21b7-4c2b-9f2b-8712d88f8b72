package qnexus

import (
	"time"

	"gitlab.qomolo.com/qomolo_utils/qomolo_get/pkg/utils"
)

type RawFolder struct {
	Path    string      `json:"path"`
	Folders []RawFolder `json:"folders"`
	Files   []RawFile   `json:"files"`
}

type RawFile struct {
	Name   string    `json:"name"`
	Path   string    `json:"path"`
	Mtime  time.Time `json:"mtime"`
	Size   int       `json:"size"`
	Md5    string    `json:"md5"`
	Sha256 string    `json:"sha256"`
}

func (r RawFile) ETag() string {
	return utils.CalNginxETag(r.Mtime, int64(r.<PERSON><PERSON>))
}

type Asset struct {
	Name  string `json:"name"`
	Type  string `json:"type"`
	Mtime string `json:"mtime"`
	Size  int    `json:"size"`
	Md5   string `json:"md5"`
}

func (a Asset) ETag() string {
	time, _ := time.Parse(time.RFC1123, a.Mtime)
	return utils.CalNginxETag(time, int64(a.<PERSON><PERSON>))
}
