package qnexus

import (
	"context"
	"fmt"
	"net/url"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/spf13/viper"
	"go.uber.org/zap"
)

type NexusWWL struct {
	log                 *zap.SugaredLogger
	client              *resty.Client
	repoURL             string
	rawRepoName         string
	clientNexus         *NexusClient
	integrationBasePath string
}

func NewNexusWWLClient(url, rawPath string, log *zap.SugaredLogger) (QNexus, error) {
	integrationBasePath := rawPath + "/integration"
	agent := resty.New().
		SetRetryCount(3).
		SetRetryWaitTime(5 * time.Second).
		SetBaseURL(url + integrationBasePath)
	if viper.GetBool("DEBUG") {
		agent.SetDebug(true)
	}
	return &NexusWWL{
		repoURL:             url,
		log:                 log,
		client:              agent,
		clientNexus:         NewNexus(url, log),
		rawRepoName:         "raw",
		integrationBasePath: integrationBasePath,
	}, nil
}

func (c *NexusWWL) GetDebInfo(_ context.Context, name, version, repo, arch, downloadURL string) (*Asset, error) {
	// downloadURL = "https://repo.qomolo.com/repository/alpha/pool/c/cicd-demo-a/cicd-demo-a_0.0.1-58816_amd64.deb"
	path := strings.TrimPrefix(downloadURL, fmt.Sprintf("%s/repository/%s/pool/", c.repoURL, repo))
	path = strings.TrimSuffix(path, fmt.Sprintf("/%s_%s_%s.deb", name, version, arch))
	// packages/c/cicd-demo-a/0.0.1-58816/amd64/cicd-demo-a
	node := fmt.Sprintf("packages/%s/%s/%s/%s", path, url.QueryEscape(version), arch, name)
	browseResult, err := c.clientNexus.CoreuiBrowse(repo, node)
	if err != nil {
		return nil, err
	}
	if len(browseResult.Data) == 0 {
		return nil, fmt.Errorf("路径不存在 %s", node)
	}
	debResult, err := c.clientNexus.CoreuiComponentReadAsset(browseResult.Data[0].AssetID, repo)
	if err != nil {
		return nil, err
	}
	return &Asset{
		Name:  debResult.Data.Attributes.Apt.PackageName,
		Mtime: debResult.Data.BlobUpdated,
		Size:  int(debResult.Data.Size),
		Md5:   debResult.Data.Attributes.Checksum.Md5,
	}, nil
}
