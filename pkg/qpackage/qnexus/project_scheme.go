package qnexus

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"sort"

	"golang.org/x/xerrors"
)

func (c *NexusProject) GetSchemeListByFile() (schemeList []SchemeData, err error) {
	resp, err := c.client.R().Get("/scheme/scheme-release.json")
	if err != nil {
		c.log.Errorf("GetSchemeList %v", err)
		return nil, xerrors.New("GetSchemeList err")
	}
	if resp.IsError() {
		return nil, xerrors.Errorf("request error code:%d", resp.StatusCode())
	}
	wrap := NexusWrap[[]SchemeData]{}
	err = json.Unmarshal(resp.Body(), &wrap)
	if err != nil {
		c.log.Errorf("GetSchemeList %v body: %s", err, string(resp.Body()))
		return nil, xerrors.<PERSON><PERSON><PERSON>("parse err:%s", err)
	}

	return wrap.Data, nil
}

func (c *NexusProject) GetSchemeVersionInfo(_ context.Context, name, version string) (info *NexusIntegration, err error) {
	url := fmt.Sprintf("/scheme/%s/%s/%s-%s.json", name, version, name, version)
	resp, err := c.client.R().Get(url)
	if err != nil {
		c.log.Errorf("GetSchemeVersionInfo %v", err)
		return nil, xerrors.New("GetSchemeVersionInfo err")
	}
	if resp.IsError() {
		if resp.StatusCode() == http.StatusNotFound {
			return nil, xerrors.Errorf("%s %s not found", name, version)
		}
		return nil, xerrors.Errorf("request error code:%d", resp.StatusCode())
	}
	wrap := NexusWrap[NexusIntegration]{}

	err = json.Unmarshal(resp.Body(), &wrap)
	if err != nil {
		c.log.Errorf("GetSchemeVersionInfo %v body: %s", err, string(resp.Body()))
		return nil, xerrors.Errorf("parse err:%s", err)
	}
	return &wrap.Data, nil
}

func (c *NexusProject) GetSchemeVersionListByFile(_ context.Context, name string) (list []NexusIntegration, err error) {
	url := fmt.Sprintf("/scheme/%s/%s-release.json", name, name)
	resp, err := c.client.R().Get(url)
	if err != nil {
		c.log.Errorf("GetSchemeVersionList %v", err)
		return nil, xerrors.New("GetSchemeVersionList err")
	}
	if resp.IsError() {
		if resp.StatusCode() == http.StatusNotFound {
			return nil, errors.New("scheme release not found")
		}
		return nil, xerrors.Errorf("request error code:%d", resp.StatusCode())
	}
	wrap := NexusWrap[[]NexusIntegration]{}
	err = json.Unmarshal(resp.Body(), &wrap)
	if err != nil {
		c.log.Errorf("GetSchemeVersionList %v body: %s", err, string(resp.Body()))
		return nil, xerrors.Errorf("parse error %s", err)
	}
	return wrap.Data, nil
}

func (c *NexusProject) GetSchemeList(ctx context.Context) ([]SchemeData, error) {
	list, err := c.GetSchemeListByFile()
	if err != nil {
		return nil, err
	}
	schemeMap := make(map[string]struct{})
	res := make([]SchemeData, 0)
	dataMap, err := c.nginxCheckFileIsExist("/scheme/", NginxDir)
	if err != nil {
		return nil, err
	}
	for _, v := range list {
		if _, ok := dataMap[v.Name]; ok {
			res = append(res, v)
			schemeMap[v.Name] = struct{}{}
		}
	}

	for v, k := range dataMap {
		if _, ok := schemeMap[v]; ok {
			continue
		}
		info, err1 := c.GetSchemeVersionListByFile(ctx, k.Name)
		if err1 != nil {
			continue
		}
		if len(info) > 0 {
			res = append(res, SchemeData{Name: k.Name})
		}
	}
	sort.Slice(res, func(i, j int) bool {
		return res[i].Name > res[j].Name
	})

	return res, nil
}

func (c *NexusProject) GetSchemeVersionList(ctx context.Context, name string) ([]NexusIntegration, error) {
	list, err := c.GetSchemeVersionListByFile(ctx, name)
	if err != nil {
		return nil, err
	}
	versionMap := make(map[string]struct{})
	versionList := make([]NexusIntegration, 0)

	url := fmt.Sprintf("scheme/%s/", name)
	dataMap, err := c.nginxCheckFileIsExist(url, NginxDir)
	if err != nil {
		return nil, err
	}
	for _, v := range list {
		if _, ok := dataMap[v.Version]; ok {
			versionMap[v.Version] = struct{}{}
			versionList = append(versionList, v)
		}
	}
	// 防止在 release 中不存在，但版本本身存在
	for v, k := range dataMap {
		if _, ok := versionMap[v]; ok {
			continue
		}
		info, err1 := c.GetSchemeVersionInfo(ctx, name, k.Name)
		if err1 != nil {
			continue
		}
		versionList = append(versionList, *info)
	}
	sort.Slice(versionList, func(i, j int) bool {
		return versionList[i].CreateTime > versionList[j].CreateTime
	})
	return versionList, nil
}
