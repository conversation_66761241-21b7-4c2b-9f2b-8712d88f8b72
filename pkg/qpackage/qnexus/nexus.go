package qnexus

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"gitlab.qomolo.com/infrastructure/sw_architecture/middleware_abstraction/utility/qlibrary/qutils"
	qlog "gitlab.qomolo.com/infrastructure/sw_architecture/middleware_abstraction/utility/qlog_golang"
	"gitlab.qomolo.com/qomolo_utils/qomolo_get/pkg/utils"
	"go.uber.org/zap"

	"github.com/go-resty/resty/v2"
)

type NexusClient struct {
	nexusBaseURL string
	log          *zap.SugaredLogger
}

func NewNexus(url string, log *zap.SugaredLogger) *NexusClient {
	return &NexusClient{
		nexusBaseURL: url,
		log:          log,
	}
}

type Method string

const (
	Read          = "read"
	ReadComponent = "readComponent"
	ReadAsset     = "readAsset"
)

func (c *NexusClient) CoreuiBrowse(repoName, node string) (*BrowseResult, error) {
	url := c.nexusBaseURL + "/service/extdirect"
	client := resty.New()
	req := BrowseRequest{
		CoreuiBase{
			Action: "coreui_Browse",
			Method: "read",
			Type:   "rpc",
			Tid:    0,
		},
		[]BrowseRequestData{
			{
				RepositoryName: repoName,
				Node:           node,
			},
		},
	}
	res := BrowseResponse{}
	resp, err := client.R().
		SetBody(req).
		SetResult(&res).
		Post(url)
	if err != nil {
		return nil, err
	}
	if !resp.IsSuccess() {
		return nil, fmt.Errorf("request failed, %v", resp.StatusCode())
	}
	return &res.Result, nil
}

func (c *NexusClient) CoreuiComponentReadComponent(componentID, repoName string) (*ReadComponentResult, error) {
	url := c.nexusBaseURL + "/service/extdirect"
	client := resty.New()
	req := ComponentRequest{
		CoreuiBase{
			Action: "coreui_Component",
			Method: ReadComponent,
			Type:   "rpc",
			Tid:    0,
		},
		[]string{componentID, repoName},
	}
	// client.SetRetryCount(3)
	res := ReadComponentResponse{}
	resp, err := client.R().
		SetBody(req).
		SetResult(&res).
		Post(url)
	if err != nil {
		return nil, err
	}
	if !resp.IsSuccess() {
		return nil, fmt.Errorf("request failed, %v", resp.StatusCode())
	}
	return &res.Result, nil
}

func (c *NexusClient) CoreuiComponentReadAsset(assetID, repoName string) (*ReadAssetResult, error) {
	url := c.nexusBaseURL + "/service/extdirect"
	client := resty.New()
	req := ComponentRequest{
		CoreuiBase{
			Action: "coreui_Component",
			Method: ReadAsset,
			Type:   "rpc",
			Tid:    0,
		},
		[]string{assetID, repoName},
	}
	// client.SetRetryCount(3)
	res := ReadAssetResponse{}
	resp, err := client.R().
		SetBody(req).
		SetResult(&res).
		Post(url)
	if err != nil {
		return nil, err
	}
	if !resp.IsSuccess() {
		return nil, fmt.Errorf("request failed, %v", resp.StatusCode())
	}
	return &res.Result, nil
}

func (c *NexusClient) GetAllRawFileInfoInFolder(repoName, path string) (*RawFileList, error) {
	browseResult, err := c.CoreuiBrowse(repoName, path)
	if err != nil {
		return nil, err
	}
	rawFileList := RawFileList{}
	for _, v := range browseResult.Data {
		// fmt.Println(v.ID, v.Type, v.ComponentID)
		if v.Type == "folder" {
			rawFileList.Folders = append(rawFileList.Folders, v.ID)
			tmpRawFileList, err := c.GetAllRawFileInfoInFolder(repoName, v.ID)
			if err != nil {
				return nil, err
			}
			rawFileList.Folders = append(rawFileList.Folders, tmpRawFileList.Folders...)
			rawFileList.Assets = append(rawFileList.Assets, tmpRawFileList.Assets...)
		}

		if v.Type == "asset" {
			readAssetResult, err := c.CoreuiComponentReadAsset(v.AssetID, repoName)
			if err != nil {
				return nil, err
			}
			rawFileList.Assets = append(rawFileList.Assets,
				NexusRawFile{
					Path:   readAssetResult.Data.Name,
					Size:   readAssetResult.Data.Size,
					Sha256: readAssetResult.Data.Attributes.Checksum.Sha256,
					Md5:    readAssetResult.Data.Attributes.Checksum.Md5,
				},
			)
		}
	}
	return &rawFileList, nil
}

func (c *NexusClient) GetRawFolderSize(repoName, remotePath string) (int64, error) {
	rawFileList, err := c.GetAllRawFileInfoInFolder(repoName, remotePath)
	if err != nil {
		return -1, err
	}
	var sizeCount int64
	for _, asset := range rawFileList.Assets {
		sizeCount += asset.Size
	}
	fmt.Printf("total file size: %v B\n", sizeCount)
	return sizeCount, nil
}

func (c *NexusClient) RecursiveDownloadRawFolder(repoName, remotePath, localPath string) error {
	rawFileList, err := c.GetAllRawFileInfoInFolder(repoName, remotePath)
	if err != nil {
		return err
	}
	for _, dir := range rawFileList.Folders {
		err = os.MkdirAll(filepath.Join(localPath, dir), 0755)
		if err != nil {
			return err
		}
	}
	client := resty.New()
	client.
		SetRetryCount(3).
		SetRetryWaitTime(5 * time.Second).
		SetRetryMaxWaitTime(20 * time.Second)
	client.SetOutputDirectory(localPath)
	for _, asset := range rawFileList.Assets {
		localFile := fmt.Sprintf("%s/%s", localPath, asset.Path)
		hash, _ := utils.GetFileSHA256Hash(localFile)
		if hash == asset.Sha256 {
			c.log.Infof("skip download: %s\n", asset.Path)
			continue
		}
		c.log.Infof("开始下载 %s", asset.Path)
		_, err := client.R().SetOutput(asset.Path).Get(c.nexusBaseURL + "/repository/raw/" + asset.Path)
		if err != nil {
			return err
		}
	}
	return nil
}

func (c *NexusClient) RecursiveDownloadRawModule(repoName, remotePath, localPath string, mdp *ModuleDownloadProgress) error {
	var err error
	// remotePath integration/module/{name}/{version}
	// localPath /data/qomolo_resource/integration
	rawFileList, err := c.GetAllRawFileInfoInFolder(repoName, remotePath)
	if err != nil {
		return err
	}
	for _, dir := range rawFileList.Folders {
		// integration/module/qomolo-resource-pcd-map-cnwxijk/1.1.1-1736497612/qprofile
		// /data/qomolo_resource/integration/module/qomolo-resource-pcd-map-cnwxijk/1.1.1-1736497612/qprofile
		err = os.MkdirAll(filepath.Join(localPath, strings.TrimPrefix(dir, "integration/module")), 0755)
		if err != nil {
			return err
		}
	}

	// /data/qomolo_resource/integration/module/{name}/{version}
	localModulePath := filepath.Join(localPath, strings.TrimPrefix(remotePath, "integration/module"))
	metaDataJson := localModulePath + "/metadata.json"
	metaData := &utils.MetaData{}
	exist := utils.CheckFileExist(metaDataJson)
	if exist {
		metaData, err = utils.ParseMetaDataJson(localModulePath)
		if err != nil {
			return err
		}
	}

	for _, asset := range rawFileList.Assets {
		// integration/module/qomolo-resource-pcd-map-cnwxijk/1.1.1-1736497612/qprofile/.backup/calibration/profile.yaml
		file := strings.TrimPrefix(asset.Path, remotePath+"/")
		// /data/qomolo_resource/module/qomolo-resource-pcd-map-cnwxijk/1.1.1-1736497612/qprofile/.backup/calibration/profile.yaml
		localFile := fmt.Sprintf("%s/%s", localModulePath, file)
		hash, _ := utils.GetFileSHA256Hash(localFile)
		fileInfo := utils.FileDownloadProgress{
			Name:    asset.Name,
			Path:    file,
			Remote:  asset.Path,
			Sha256:  asset.Sha256,
			Md5:     asset.Md5,
			Size:    asset.Size,
			Total:   asset.Size,
			Current: 0,
		}
		if metaData.FileMatchHash(file, hash, utils.HashTypeSha256) {
			qlog.Debugf("%s 存在并且sha256一致, 跳过下载", file)
			fileInfo.SkipDownload = true
			fileInfo.Current = asset.Size
		} else {
			qlog.Debugf("%s sha256不一致，需重新下载\n", file)
			fileInfo.Current = 0
		}
		mdp.Files = append(mdp.Files, fileInfo)
	}

	// 下载
	tmpModuleDownloadDir := utils.DataTmpDir + localModulePath
	_ = os.MkdirAll(localModulePath, 0755)
	_ = os.RemoveAll(tmpModuleDownloadDir)
	_ = os.MkdirAll(tmpModuleDownloadDir, 0755)
	for i, file := range mdp.Files {
		if file.SkipDownload {
			continue
		}
		err := utils.DownloadWithResumeAndChunkSingleRawFile(
			c.nexusBaseURL+"/repository/raw/",
			tmpModuleDownloadDir, true,
			&mdp.Files[i],
		)
		if err != nil {
			return err
		}
		qlog.Debugf("downloaded %s\n", file.Path)
		metaData.Checksums = append(metaData.Checksums,
			utils.Checksum{File: file.Path, Hash: utils.NewHash(utils.HashTypeSha256, file.Sha256)})
	}

	// mv到正式目录
	for _, file := range mdp.Files {
		if file.SkipDownload {
			continue
		}
		srcFile := filepath.Join(tmpModuleDownloadDir, file.Path)
		dstFile := filepath.Join(localModulePath, file.Path)
		err := os.Rename(srcFile, dstFile)
		if err != nil {
			return err
		}
	}

	if len(mdp.Files) > 0 {
		metaData.Time = qutils.Time(time.Now())
		return utils.WriteToJSON(metaDataJson, metaData)
	}
	qlog.Info("所有文件校验一致，无需下载")
	return nil
}
