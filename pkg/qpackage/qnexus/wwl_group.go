package qnexus

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strings"

	"github.com/PuerkitoBio/goquery"
	"golang.org/x/xerrors"
)

func (c *NexusWWL) GetSchemeGroupListByFile(_ context.Context) (schemeList []SchemeGroupRelease, err error) {
	url := "/group/scheme-group-release.json"
	resp, err := c.client.R().Get(url)
	if err != nil {
		c.log.Errorf("GetSchemeGroupList %v", err)
		return nil, xerrors.New("GetSchemeGroupList error")
	}
	if resp.IsError() {
		c.log.Errorf("GetSchemeGroupList %v body:%s", err, string(resp.Body()))
		return nil, xerrors.Errorf("request err %d url:%s ", resp.StatusCode(), url)
	}
	wrap := NexusWrap[[]SchemeGroupRelease]{}
	err = json.Unmarshal(resp.Body(), &wrap)
	if err != nil {
		c.log.Errorf("GetSchemeGroupList %v body:%s", err, string(resp.Body()))
		return nil, xerrors.Errorf("parse error %s", err)
	}
	return wrap.Data, nil
}

func (c *NexusWWL) GetGroupVersionInfo(_ context.Context, name, version string) (info *NexusIntegrationGroup, err error) {
	c.log.Infof("name: %v\n", name)
	c.log.Infof("version: %v\n", version)
	url := fmt.Sprintf("/group/%s/%s/%s-%s.json", name, version, name, version)
	c.log.Infof("url: %v\n", url)
	resp, err := c.client.R().Get(url)
	if err != nil {
		return nil, errors.New("GetSchemeGroupVersionInfo err")
	}
	if resp.IsError() {
		if resp.StatusCode() == http.StatusNotFound {
			return nil, fmt.Errorf("%s %s not found", name, version)
		}
		return nil, fmt.Errorf("request err %d", resp.StatusCode())
	}
	wrap := NexusWrap[NexusIntegrationGroup]{}
	err = json.Unmarshal(resp.Body(), &wrap)
	if err != nil {
		c.log.Errorf("GetSchemeGroupVersionInfo %v body:%s", err, string(resp.Body()))
		return nil, xerrors.Errorf("parse error %s", err)
	}
	return &wrap.Data, nil
}

func (c *NexusWWL) GetSchemeGroupVersionListByFile(_ context.Context, name string) (list []NexusIntegrationGroup, err error) {
	url := fmt.Sprintf("/group/%s/%s-release.json", name, name)
	resp, err := c.client.R().Get(url)
	if err != nil {
		c.log.Errorf("GetSchemeGroupVersionInfo %v", err)
		return nil, xerrors.New("GetSchemeGroupVersionList err")
	}
	if resp.IsError() {
		if resp.StatusCode() == http.StatusNotFound {
			return nil, errors.New("scheme group release not found")
		}
		return nil, xerrors.Errorf("request err %d", resp.StatusCode())
	}
	wrap := NexusWrap[[]NexusIntegrationGroup]{}
	err = json.Unmarshal(resp.Body(), &wrap)
	if err != nil {
		c.log.Errorf("GetSchemeGroupVersionInfo %v body:%s", err, string(resp.Body()))
		return nil, xerrors.Errorf("parse error %s", err)
	}
	return wrap.Data, nil
}

func (c *NexusWWL) GetGroupList(ctx context.Context) ([]SchemeGroupRelease, error) {
	schemeList, err := c.GetSchemeGroupListByFile(ctx)
	if err != nil {
		return nil, err
	}
	list := make([]SchemeGroupRelease, 0)

	m, err := c.parseHTML(c.repoURL, "integration/group")
	if err != nil {
		return nil, err
	}
	for _, v := range schemeList {
		if _, ok := m[v.Name]; ok {
			list = append(list, v)
		}
	}

	return list, nil
}

func (c *NexusWWL) parseHTML(baseURL, param string) (map[string]struct{}, error) {
	//"https://repo-test.qomolo.com/service/rest/repository/browse/raw/integration/group/"
	url := fmt.Sprintf("%s/service/rest/repository/browse/raw/%s/", baseURL, param)
	res, err := c.client.R().Get(url)
	if err != nil {
		c.log.Errorf("parseHtml %v", err)
		return nil, xerrors.Errorf("parseHtml error:%v, url:%s", err, url)
	}
	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(res.Body()))
	if err != nil {
		return nil, xerrors.Errorf("new doc err:%v, url:%s", err, url)
	}
	m := make(map[string]struct{})
	ss := doc.Find("a")
	for _, v := range ss.Nodes {
		dirName := v.Attr[0].Val
		if strings.Contains(dirName, "/") {
			m[dirName[0:strings.Index(dirName, "/")]] = struct{}{}
		}
	}
	return m, nil
}

func (c *NexusWWL) GetGroupVersionList(ctx context.Context, name string) ([]NexusIntegrationGroup, error) {
	list, err := c.GetSchemeGroupVersionListByFile(ctx, name)
	if err != nil {
		return nil, err
	}
	versionList := make([]NexusIntegrationGroup, 0)

	m, err := c.parseHTML(c.repoURL, "integration/group/"+name)
	if err != nil {
		return nil, err
	}
	for _, v := range list {
		if _, ok := m[v.Version]; ok {
			versionList = append(versionList, v)
		}
	}

	return versionList, nil
}
