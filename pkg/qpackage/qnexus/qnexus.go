package qnexus

import (
	"context"
)

type QNexus interface {
	GetGroupVersionList(ctx context.Context, name string) ([]NexusIntegrationGroup, error)
	GetGroupList(ctx context.Context) ([]SchemeGroupRelease, error)
	GetGroupVersionInfo(ctx context.Context, name, version string) (info *NexusIntegrationGroup, err error)

	GetSchemeVersionList(ctx context.Context, name string) ([]NexusIntegration, error)
	GetSchemeList(ctx context.Context) ([]SchemeData, error)
	GetSchemeVersionInfo(ctx context.Context, name, version string) (info *NexusIntegration, err error)

	ModuleList(ctx context.Context) ([]Module, error)
	ModuleVersionList(ctx context.Context, name string) (ModuleDetailList, error)
	ModuleVersionInfo(ctx context.Context, name, version string) (info *RawFolder, err error)
	ModuleDownload(ctx context.Context, name, version, localPath string, mdp *ModuleDownloadProgress) error

	GetDebInfo(ctx context.Context, name, version, repo, arch, downloadURL string) (*Asset, error)
}

var _ = QNexus(&NexusWWL{})
var _ = QNexus(&NexusProject{})
