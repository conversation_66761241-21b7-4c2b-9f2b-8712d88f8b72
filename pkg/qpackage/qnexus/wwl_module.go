package qnexus

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"strings"

	qlog "gitlab.qomolo.com/infrastructure/sw_architecture/middleware_abstraction/utility/qlog_golang"
)

func (c *NexusWWL) ModuleList(_ context.Context) ([]Module, error) {
	m, err := c.parseHTML(c.repoURL, "integration/module/")
	if err != nil {
		return nil, err
	}
	list := make([]Module, 0)
	for dirName := range m {
		if dirName == ".." {
			continue
		}
		list = append(list, Module{
			Name: dirName,
		})
	}
	return list, nil
}

func (c *NexusWWL) ModuleVersionList(_ context.Context, name string) (ModuleDetailList, error) {
	remotePath := fmt.Sprintf("integration/module/%s", name)
	m, err := c.parseHTML(c.repoURL, remotePath)
	if err != nil {
		return nil, err
	}

	versionList := make(ModuleDetailList, 0)
	for dirName := range m {
		if dirName == ".." {
			continue
		}
		version := dirName
		// 获取metadata
		medatataUrl := fmt.Sprintf("%s/%s/.module_metadata.json", remotePath, version)
		resp, err1 := c.client.R().Get(medatataUrl)
		// 忽略错误
		if err1 != nil {
			qlog.Debugf("获取metadata失败: %s", err1)
		}
		metadataJson := ModuleMetadata{}
		if resp != nil && resp.IsSuccess() {
			err1 = json.Unmarshal(resp.Body(), &metadataJson)
			if err1 != nil {
				qlog.Debugf("解析metadata失败: %s", err1)
			}
		}
		versionList = append(versionList, ModuleDetail{
			Name:     name,
			Version:  version,
			Metadata: metadataJson,
		})
	}
	sort.Sort(versionList)
	return versionList, nil
}
func (c *NexusWWL) ModuleVersionInfo(_ context.Context, name, version string) (*RawFolder, error) {
	remotePath := fmt.Sprintf("integration/module/%s/%s", name, version)
	rawFileList, err := c.clientNexus.GetAllRawFileInfoInFolder(c.rawRepoName, remotePath)
	if err != nil {
		return nil, err
	}
	res := new(RawFolder)
	// TODO
	res.Path = remotePath

	for _, folder := range rawFileList.Folders {
		res.Folders = append(res.Folders, RawFolder{Path: folder})
	}
	for _, file := range rawFileList.Assets {
		pathSplit := strings.Split(file.Path, "/")
		res.Files = append(res.Files, RawFile{
			Name:   pathSplit[len(pathSplit)-1],
			Path:   file.Path,
			Size:   int(file.Size),
			Sha256: file.Sha256,
			Md5:    file.Md5,
		})
	}

	return res, nil
}
func (c *NexusWWL) ModuleDownload(_ context.Context, name, version, localPath string, mdp *ModuleDownloadProgress) error {
	qlog.Infof("module download %s %s", name, version)
	// mdp := &ModuleDownloadProgress{Name: name, Version: version}
	mdp.Name = name
	mdp.Version = version
	remotePath := fmt.Sprintf("integration/module/%s/%s", name, version)
	browseResult, err := c.clientNexus.CoreuiBrowse(c.rawRepoName, remotePath)
	if err != nil {
		return err
	}
	if len(browseResult.Data) == 0 {
		return fmt.Errorf("文件不存在 %s", remotePath)
	}
	return c.clientNexus.RecursiveDownloadRawModule(c.rawRepoName, remotePath, localPath, mdp)
}
