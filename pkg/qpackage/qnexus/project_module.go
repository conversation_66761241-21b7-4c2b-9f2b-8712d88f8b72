package qnexus

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"time"

	"gitlab.qomolo.com/infrastructure/sw_architecture/middleware_abstraction/utility/qlibrary/qutils"
	qlog "gitlab.qomolo.com/infrastructure/sw_architecture/middleware_abstraction/utility/qlog_golang"
	"gitlab.qomolo.com/qomolo_utils/qomolo_get/pkg/utils"
)

func (c *NexusProject) ModuleList(_ context.Context) ([]Module, error) {
	url := "/module/"
	res, err := c.getPathInfo(url)
	if err != nil {
		return nil, err
	}
	modules := make([]Module, 0)
	for _, v := range res {
		modules = append(modules, Module{
			Name: v.Name,
		})
	}
	return modules, nil
}

func (c *NexusProject) ModuleVersionList(_ context.Context, name string) (ModuleDetailList, error) {
	url := fmt.Sprintf("/module/%s/", name)
	dataMap, err := c.nginxCheckFileIsExist(url, NginxDir)
	if err != nil {
		return nil, err
	}

	versionList := make(ModuleDetailList, 0)
	for version := range dataMap {
		// 获取metadata
		medatataUrl := fmt.Sprintf("%s/%s/.module_metadata.json", url, version)
		resp, err1 := c.client.R().Get(medatataUrl)
		// 忽略错误
		if err1 != nil {
			qlog.Debugf("获取metadata失败: %s", err1)
		}
		md := ModuleDetail{
			Name:     name,
			Version:  version,
			Metadata: ModuleMetadata{},
		}
		if resp != nil && resp.IsSuccess() {
			metadataJson := ModuleMetadata{}
			err = json.Unmarshal(resp.Body(), &metadataJson)
			if err != nil {
				qlog.Debugf("解析metadata失败: %s", err)
			} else {
				md.Metadata = metadataJson
			}
		}
		versionList = append(versionList, md)
	}
	sort.Sort(versionList)
	return versionList, nil
}

func (c *NexusProject) ModuleVersionInfo(ctx context.Context, name, version string) (*RawFolder, error) {
	url := fmt.Sprintf("/module/%s/%s", name, version)
	return c.recursiveGetFolder(ctx, url)
}

func recursiveFolder(rf *RawFolder) (directories []RawFolder, files []RawFile) {
	files = append(files, rf.Files...)
	for _, f := range rf.Folders {
		directories = append(directories, f)
		childFolder, childFiles := recursiveFolder(&f)
		directories = append(directories, childFolder...)
		files = append(files, childFiles...)
	}
	return
}

func (c *NexusProject) ModuleDownload(ctx context.Context, name, version, localPath string, mdp *ModuleDownloadProgress) error {
	qlog.Infof("module download %s %s", name, version)
	// mdp := &ModuleDownloadProgress{Name: name, Version: version}
	mdp.Name = name
	mdp.Version = version
	localModulePath := fmt.Sprintf("%s/%s/%s", utils.ModuleDownloadLocalPath, name, version)
	tmpModuleDownloadDir := utils.DataTmpDir + localModulePath
	path := fmt.Sprintf("/module/%s/%s", name, version)
	folder, err := c.recursiveGetFolder(ctx, path)
	if err != nil {
		return err
	}
	directories, files := recursiveFolder(folder)
	metaDataJson := localModulePath + "/metadata.json"
	metaData := &utils.MetaData{}
	exist := utils.CheckFileExist(metaDataJson)
	if exist {
		metaData, err = utils.ParseMetaDataJson(localModulePath)
		if err != nil {
			return err
		}
	}
	for _, file := range files {
		f := strings.TrimPrefix(filepath.Join(file.Path, file.Name), path+"/")
		// /data/qomolo_resource/module/qomolo-resource-pcd-map-cnwxijk/1.1.1-1736497612/qprofile/.backup/calibration/profile.yaml
		localFile := fmt.Sprintf("%s/%s", localModulePath, f)
		md5, _ := utils.GetMD5(localFile)
		etag := utils.GetFileNginxETag(localFile)
		fileInfo := utils.FileDownloadProgress{
			Name: file.Name,
			Path: f, Remote: filepath.Join(path, f),
			Md5:     file.Md5,
			ETag:    file.ETag(),
			Mtime:   file.Mtime,
			Size:    int64(file.Size),
			Total:   int64(file.Size),
			Current: 0,
		}
		if metaData.FileMatchHash(f, md5, utils.HashTypeMd5) || metaData.FileMatchHash(f, etag, utils.HashTypeETag) {
			qlog.Debugf("%s 存在并且md5或etag一致, 跳过下载", f)
			fileInfo.SkipDownload = true
			fileInfo.Current = int64(file.Size)
		} else {
			qlog.Debugf("%s md5或etag不一致，需重新下载\n", f)
			fileInfo.SkipDownload = false
		}
		mdp.Files = append(mdp.Files, fileInfo)
	}

	// 下载
	_ = os.MkdirAll(localModulePath, 0755)
	for _, dir := range directories {
		_ = os.MkdirAll(filepath.Join(utils.DataTmpDir, utils.ModuleDownloadLocalPath, strings.TrimPrefix(dir.Path, "/module/")), 0755)
		_ = os.MkdirAll(filepath.Join(utils.ModuleDownloadLocalPath, strings.TrimPrefix(dir.Path, "/module/")), 0755)
	}
	for i, file := range mdp.Files {
		if file.SkipDownload {
			continue
		}
		err := utils.DownloadWithResumeAndChunkSingleRawFile(
			c.nexusBaseURL+c.rawPath+"/"+c.baseDir+"/",
			tmpModuleDownloadDir, true,
			&mdp.Files[i],
		)
		if err != nil {
			return err
		}
		qlog.Debugf("downloaded %s\n", file.Path)
		metaData.Checksums = append(metaData.Checksums,
			utils.Checksum{File: file.Path, Hash: utils.NewHash(utils.HashTypeETag, file.ETag)})
	}

	// mv到正式目录
	for _, file := range mdp.Files {
		if file.SkipDownload {
			continue
		}
		srcFile := filepath.Join(tmpModuleDownloadDir, file.Path)
		dstFile := filepath.Join(localModulePath, file.Path)
		err := os.Rename(srcFile, dstFile)
		if err != nil {
			fmt.Println(err)
			return err
		}
		err1 := os.Chtimes(dstFile, time.Time{}, file.Mtime)
		if err1 != nil {
			qlog.Debugf("ch file:%s mtime:%s\n", dstFile, file.Mtime)
		}
	}

	if len(mdp.Files) > 0 {
		metaData.Time = qutils.Time(time.Now())
		return utils.WriteToJSON(metaDataJson, metaData)
	}
	qlog.Infof("所有文件校验一致，无需下载")
	return nil

}

func (c *NexusProject) recursiveGetFolder(ctx context.Context, path string) (*RawFolder, error) {
	searchPath := path
	if !strings.HasSuffix(searchPath, "/") {
		searchPath += "/"
	}
	res, err := c.getPathInfo(searchPath)
	if err != nil {
		return nil, err
	}
	folder := RawFolder{
		Path:    path,
		Folders: make([]RawFolder, 0),
		Files:   make([]RawFile, 0),
	}
	for _, v := range res {
		if v.Type == NginxDir {
			childFolder, err := c.recursiveGetFolder(ctx, fmt.Sprintf("%s/%s", path, v.Name))
			if err != nil {
				return nil, err
			}
			folder.Folders = append(folder.Folders, *childFolder)
		} else {
			folder.Files = append(folder.Files, RawFile{
				Name:  v.Name,
				Size:  v.Size,
				Path:  path,
				Mtime: v.GetMtime(),
				Md5:   v.Md5,
			})
		}
	}
	return &folder, nil
}
