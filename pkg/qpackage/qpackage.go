package qpackage

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"slices"
	"strings"
	"time"

	aptlyDeb "github.com/aptly-dev/aptly/deb"
	dockerTypesImage "github.com/docker/docker/api/types/image"
	"github.com/docker/docker/client"
	"github.com/go-resty/resty/v2"
	"github.com/google/go-containerregistry/pkg/authn"
	"github.com/google/go-containerregistry/pkg/name"
	"github.com/google/go-containerregistry/pkg/v1/remote"
	"github.com/google/uuid"
	"github.com/samber/lo"
	"go.uber.org/zap"

	"gitlab.qomolo.com/infrastructure/sw_architecture/middleware_abstraction/utility/qlibrary/qutils"
	qlog "gitlab.qomolo.com/infrastructure/sw_architecture/middleware_abstraction/utility/qlog_golang"
	"gitlab.qomolo.com/qomolo_utils/qomolo_get/pkg/qpackage/devops"
	"gitlab.qomolo.com/qomolo_utils/qomolo_get/pkg/qpackage/qhistory"
	"gitlab.qomolo.com/qomolo_utils/qomolo_get/pkg/qpackage/qnexus"
	"gitlab.qomolo.com/qomolo_utils/qomolo_get/pkg/utils"
)

type QPackage struct {
	*QPackageStore
	opt Option
	qnexus.QNexus
	*AptUtil
	DpkgUtil *DpkgUtil
	*AptConfig
	log              *zap.SugaredLogger
	Downloader       *Downloader
	ModuleDownloader *Downloader
	installHistory   *qhistory.Client[InstallStatus]
	ModuleHistory    *qhistory.Client[ModuleHistory]
	DevopsClient     *devops.Client
}

const tmpDownloadDir = "/data/MP/tmp"
const debDownloadDir = tmpDownloadDir + "/tmp_debs"

type RepoMode string

const (
	RepoModeProjectOnly RepoMode = "project_only"
	RepoModeWWLOnly     RepoMode = "wwl_only"
)

type Nexus struct {
	Wwl     RepoConfig `yaml:"wwl"`
	Project RepoConfig `yaml:"project"`
	Mode    RepoMode   `yaml:"mode"`
}

func (n Nexus) GetRepo() RepoConfig {
	if n.Mode == RepoModeWWLOnly {
		return n.Wwl
	}
	return n.Project
}

type RepoConfig struct {
	URL        string `yaml:"url"`
	RawPath    string `yaml:"raw_path"`
	User       string `yaml:"user"`
	Password   string `yaml:"password"`
	HealthPath string `yaml:"health_path"`
}

type Option struct {
	HostType    utils.HostType
	HostID      int
	Project     string // 项目编号
	VehicleType utils.VehicleType
	Chassis     string // 底盘型号
	// SkipCheckTarget 是否校验 scheme 的 target,如果 hostType有值，则不能跳过。主要是开发本地安装，hostType都是空的
	SkipCheckTarget           bool // 是否校验 scheme 的 target
	RepoURL                   string
	RepoRawPath               string
	RepoUsername              string
	RepoPassword              string
	RepoMode                  RepoMode // wwl_only or project_only
	SourceType                SourceFileType
	AptConfig                 *AptYaml // AptConfig 与 ConfigPath 二选一
	ConfigPath                string
	SourceBaseDir             string
	DownloadHistoryDBFilepath string // 文件下载历史记录文件路径
	InstallHistoryDBFilepath  string // 安装历史记录文件路径
	ModuleHistoryDBFilepath   string // 模块历史记录文件路径
	log                       *zap.SugaredLogger
	modelDCU                  utils.ModelDCU
	arch                      Arch
	linuxCodeName             LinuxCodeName
	pkgInstallDoneCb          func(done PkgInstallDone)
	ImageDomain               string
	DevopsBaseUrl             string
}

type OptionFn func(opt *Option)

func WithLog(log *zap.SugaredLogger) OptionFn {
	return func(opt *Option) {
		opt.log = log
	}
}

func WithPkgInstallDoneCb(cb func(done PkgInstallDone)) OptionFn {
	return func(opt *Option) {
		opt.pkgInstallDoneCb = cb
	}
}

func WithArch(arch Arch) OptionFn {
	return func(opt *Option) {
		opt.arch = arch
	}
}

func WithLinuxCodeName(codeName LinuxCodeName) OptionFn {
	return func(opt *Option) {
		opt.linuxCodeName = codeName
	}
}

func WithDCU(dcu utils.ModelDCU) OptionFn {
	return func(opt *Option) {
		opt.modelDCU = dcu
	}
}

func New(opt Option, fns ...OptionFn) (*QPackage, error) {
	for i := range fns {
		fns[i](&opt)
	}
	if opt.log == nil {
		opt.log = qlog.Sugar()
	}
	if opt.arch == "" {
		arch, err := utils.GetArch()
		if err != nil {
			return nil, err
		}
		opt.arch = Arch(arch)
	}

	if opt.linuxCodeName == "" {
		codeName, err := utils.GetLinuxReleaseCodeName()
		if err != nil {
			return nil, err
		}
		opt.linuxCodeName = LinuxCodeName(codeName)
	}

	if opt.modelDCU == "" {
		opt.modelDCU = utils.GetModelDCU()
	}

	store, err := NewQPackageStore(qlog.Sugar())
	if err != nil {
		return nil, err
	}
	var nexusClient qnexus.QNexus

	if opt.RepoMode == RepoModeProjectOnly {
		nexusClient, err = qnexus.NewNexusProjectClient(opt.RepoURL, opt.RepoRawPath, qlog.Sugar())
		if err != nil {
			return nil, err
		}
	} else if opt.RepoMode == RepoModeWWLOnly {
		nexusClient, err = qnexus.NewNexusWWLClient(opt.RepoURL, opt.RepoRawPath, qlog.Sugar())
		if err != nil {
			return nil, err
		}
	} else {
		return nil, fmt.Errorf("repo mode is not valid")
	}
	var aConfig *AptYamlConfig
	if opt.AptConfig != nil {
		aConfig = &AptYamlConfig{Apt: *opt.AptConfig}
	}
	config, err := NewAptConfig(AptConfigOption{
		Arch:          opt.arch,
		Code:          opt.linuxCodeName,
		ConfigPath:    opt.ConfigPath,
		SourceBaseDir: opt.SourceBaseDir,
		Config:        aConfig,
	})
	if err != nil {
		return nil, err
	}
	if opt.DownloadHistoryDBFilepath == "" {
		opt.DownloadHistoryDBFilepath = downloadHistoryDB
	}
	downloader, err := NewDownloader(opt.DownloadHistoryDBFilepath)
	if err != nil {
		return nil, err
	}
	if opt.InstallHistoryDBFilepath == "" {
		opt.InstallHistoryDBFilepath = installHistoryDB
	}
	client, err := qhistory.New[InstallStatus](opt.InstallHistoryDBFilepath)
	if err != nil {
		return nil, err
	}
	if opt.ModuleHistoryDBFilepath == "" {
		opt.ModuleHistoryDBFilepath = moduleHistoryDB
	}
	moduleDownloader, err := NewDownloader(opt.ModuleHistoryDBFilepath)
	if err != nil {
		return nil, err
	}

	devopsClient := devops.NewDevopsApiClient(opt.DevopsBaseUrl)

	return &QPackage{
		QPackageStore:    store,
		opt:              opt,
		log:              qlog.Sugar(),
		QNexus:           nexusClient,
		AptUtil:          NewAptUtil(),
		DpkgUtil:         NewDpkgUtil(),
		AptConfig:        config,
		Downloader:       downloader,
		installHistory:   client,
		ModuleDownloader: moduleDownloader,
		DevopsClient:     devopsClient,
	}, nil
}

type DownloadOption struct {
	needArchivesReady bool
}

type DownloadOptionFn func(opt *DownloadOption)

func WithDownloadOptNeedArchivesReady(b bool) DownloadOptionFn {
	return func(opt *DownloadOption) {
		opt.needArchivesReady = b
	}
}

func (q *QPackage) PackageInstall(ctx context.Context, name, version string) (res *InstallDryRunRes, errs error) {
	err := q.QPackageStore.CheckTrySetStatus(PackageStatus{
		Status:  Installing,
		Name:    name,
		Version: version,
		Type:    string(qnexus.GroupTypeGroup),
		Time:    time.Now(),
	})
	if err != nil {
		return nil, fmt.Errorf("check status failed: %v", err)
	}
	defer q.QPackageStore.ClearStatus()
	return q.GroupInstallAll(ctx, name, version)
}

func (q *QPackage) GroupInstallAll(ctx context.Context, name, version string) (res *InstallDryRunRes, err error) {
	hasOverlay, err := utils.HasOverlay()
	if err != nil {
		return nil, fmt.Errorf("check overlay failed: %v", err)
	}
	taskID := uuid.NewString()
	startAt := time.Now()
	defer func() {
		h := qhistory.History[InstallStatus]{
			Bucket:  qhistory.BucketTypeGroup,
			Name:    name,
			Version: version,
			TaskID:  taskID,
			StartAt: qutils.Time(startAt),
			EndAt:   qutils.Time(time.Now()),
			Detail: InstallStatus{
				Action: InstallAction,
				Status: ActionSuccess,
			},
			Desc: "",
		}
		if err != nil {
			h.Detail.Status = ActionFailed
			h.Desc = fmt.Sprintf("install group %s-%s failed: %v", name, version, err)
		}
		err1 := q.installHistory.Add(ctx, h)
		if err1 != nil {
			utils.CtxLogger(ctx).Error("save group history failed", zap.Error(err1))
		}
	}()

	var errs []error
	// 如果有 overlay,先安装到 overlay,如果安装失败，不再安装到 underlay
	if hasOverlay {
		utils.CtxLogger(ctx).Info(fmt.Sprintf("hasOverlay install to overlay, name:%s,version:%s, err:%v", name, version, err))
		_, errs = q.GroupInstall(ctx, name, version, nil, false)
		if len(errs) != 0 {
			return nil, fmt.Errorf("install group %s-%s failed: %v", name, version, errs)
		}
		utils.CtxLogger(ctx).Info(fmt.Sprintf("hasOverlay install group to underlay, name:%s,version:%s, err:%v", name, version, err))
		res, errs = q.GroupInstall(ctx, name, version, nil, true)
		if len(errs) != 0 {
			return nil, fmt.Errorf("install group %s-%s failed: %v", name, version, errs)
		}
	} else {
		utils.CtxLogger(ctx).Info(fmt.Sprintf("noOverlay install group to overlay, name:%s,version:%s, err:%v", name, version, err))
		res, errs = q.GroupInstall(ctx, name, version, nil, false)
		if len(errs) != 0 {
			return nil, fmt.Errorf("install group %s-%s failed: %v", name, version, errs)
		}
	}
	return res, nil
}

func (q *QPackage) GroupInstall(ctx context.Context, name, version string, from *SchemeInstallFrom, isUnderlay bool) (res *InstallDryRunRes, errs []error) {
	info, err := q.GetGroupVersionInfo(ctx, name, version)
	if err != nil {
		errs = append(errs, err)
		return
	}
	return q.groupInstall(ctx, info, from, isUnderlay)
}

func (q *QPackage) GroupInstallDryRunAll(ctx context.Context, name, version string) (res *InstallDryRunRes, err error) {
	hasOverlay, err := utils.HasOverlay()
	if err != nil {
		return nil, fmt.Errorf("check overlay failed: %v", err)
	}
	var errs []error
	// 如果有 overlay,先安装到 overlay,如果安装失败，不再安装到 underlay
	if hasOverlay {
		utils.CtxLogger(ctx).Info(fmt.Sprintf("hasOverlay dry run install to overlay, name:%s, version:%s, err:%v", name, version, err))
		res, errs = q.GroupInstallDryRun(ctx, name, version, false)
		if len(errs) != 0 {
			return nil, fmt.Errorf("dry run install group %s-%s failed: %v", name, version, errs)
		}
		utils.CtxLogger(ctx).Info(fmt.Sprintf("hasOverlay dry run install group to underlay, name:%s, version:%s, err:%v", name, version, err))
		_, errs = q.GroupInstallDryRun(ctx, name, version, true)
		if len(errs) != 0 {
			return nil, fmt.Errorf("dry run install group %s-%s failed: %v", name, version, errs)
		}
	} else {
		utils.CtxLogger(ctx).Info(fmt.Sprintf("noOverlay dry run install group to overlay, name:%s, version:%s, err:%v", name, version, err))
		res, errs = q.GroupInstallDryRun(ctx, name, version, false)
		if len(errs) != 0 {
			return nil, fmt.Errorf("dry run install group %s-%s failed: %v", name, version, errs)
		}
	}
	return res, nil
}

func (q *QPackage) GroupInstallDryRun(ctx context.Context, name, version string, isUnderlay bool) (res *InstallDryRunRes, errs []error) {
	info, err := q.GetGroupVersionInfo(ctx, name, version)
	if err != nil {
		errs = append(errs, err)
		return
	}
	return q.groupInstallDryRun(ctx, info, isUnderlay)
}

func (q *QPackage) groupInstallDryRun(ctx context.Context, info *qnexus.NexusIntegrationGroup, isUnderlay bool) (res *InstallDryRunRes, errs []error) {
	var err error
	res = q.GroupDiffLocal(ctx, info.Name, info.Version)
	if info.IsDelete && os.Getenv("ALLOW_INSTALL_DELETE") != "true" {
		errs = append(errs, errors.New("the version has been deleted and cannot be installed"))
		return
	}

	for _, v := range info.Schemes {
		if v.Type == qnexus.GroupTypeScheme {
			var schemeRes *InstallDryRunRes
			schemeRes, err = q.SchemeInstallDryRun(ctx, v.Name, v.Version, isUnderlay)
			if err != nil {
				errs = append(errs, err)
			}
			if schemeRes != nil {
				res.Schemes = append(res.Schemes, *schemeRes)
				if schemeRes.HasDiff {
					res.HasDiff = true
					res.IsDiff = true
					res.Msgs = append(res.Msgs, fmt.Sprintf("scheme %s version %s is diff", v.Name, v.Version))
				}
			}
		} else if v.Type == qnexus.GroupTypeGroup {
			var errList []error
			// group 不判断 targets,只根据 scheme 的 targets 来判断是否安装
			var groupRes *InstallDryRunRes
			groupRes, errList = q.GroupInstallDryRun(ctx, v.Name, v.Version, isUnderlay)
			if len(errList) > 0 {
				errs = append(errs, errList...)
			}
			if groupRes != nil {
				res.Groups = append(res.Groups, *groupRes)
				if groupRes.HasDiff {
					res.HasDiff = true
					res.IsDiff = true
					res.Msgs = append(res.Msgs, fmt.Sprintf("group %s version %s is diff", v.Name, v.Version))
				}
			}
		}
	}
	return res, errs
}

func (q *QPackage) groupInstall(ctx context.Context, info *qnexus.NexusIntegrationGroup, from *SchemeInstallFrom, isUnderlay bool) (res *InstallDryRunRes, errs []error) {
	var err error
	res = &InstallDryRunRes{
		Name:            info.Name,
		RequiredVersion: info.Version,
	}
	if info.IsDelete && os.Getenv("ALLOW_INSTALL_DELETE") != "true" {
		errs = append(errs, errors.New("the version has been deleted and cannot be installed"))
		return
	}

	// 安装排序
	info.Sort()
	for _, v := range info.Schemes {
		sif := &SchemeInstallFrom{
			Source:  info.Name,
			Version: info.Version,
		}
		if v.Type == qnexus.GroupTypeScheme {
			var schemeRes *InstallDryRunRes
			schemeRes, err = q.SchemeInstall(ctx, v.Name, v.Version, sif, isUnderlay)
			if err != nil {
				errs = append(errs, err)
			}
			if schemeRes != nil {
				res.Schemes = append(res.Schemes, *schemeRes)
			}
		} else if v.Type == qnexus.GroupTypeGroup {
			var errList []error
			// group 不判断 targets,只根据 scheme 的 targets 来判断是否安装
			res, errList = q.GroupInstall(ctx, v.Name, v.Version, sif, isUnderlay)
			if len(errList) > 0 {
				errs = append(errs, errList...)
			}
		}
	}
	if len(errs) == 0 {
		err = q.QPackageStore.SetLocalGroupInfo(ctx, info.Name, info.Version, from)
		if err != nil {
			errs = append(errs, err)
		}
	}
	return res, errs
}

func (q *QPackage) GroupDownload(ctx context.Context, name, version, arch, path string) error {
	err2 := utils.HasRootPrivilege()
	if err2 != nil {
		qlog.Errorf("%v", err2)
		return err2
	}

	info, err := q.GetGroupVersionInfo(ctx, name, version)
	if err != nil {
		qlog.Errorf("%v", err)
		return err
	}

	if info.IsDelete && os.Getenv("ALLOW_INSTALL_DELETE") != "true" {
		return errors.New("version is deleted,can't install")
	}

	versionJSON := fmt.Sprintf("%s/%s_%s.json", path, name, version)
	err = utils.WriteToJSON(versionJSON, info)
	if err != nil {
		return err
	}

	for _, v := range info.Schemes {
		if v.Type == qnexus.GroupTypeScheme {
			err = q.SchemeDownload(ctx, v.Name, v.Version, arch, path)
			if err != nil {
				return err
			}
		} else if v.Type == qnexus.GroupTypeGroup {
			err = q.GroupDownload(ctx, v.Name, v.Version, arch, path)
			if err != nil {
				return err
			}
		}
	}
	return nil
}

func (q *QPackage) GroupRemoveAll(ctx context.Context, name, version string) (err error) {
	hasOverlay, err := utils.HasOverlay()
	if err != nil {
		return fmt.Errorf("check overlay failed: %v", err)
	}
	// 如果有 overlay,先安装到 overlay,如果安装失败，不再安装到 underlay
	if hasOverlay {
		utils.CtxLogger(ctx).Sugar().Infof("hasOverlay remove overlay group name:%s version:%s err:%s", name, version, err)
		err = q.GroupRemove(ctx, name, version, false)
		if err != nil {
			return fmt.Errorf("remove group %s-%s failed: %v", name, version, err)
		}
		utils.CtxLogger(ctx).Sugar().Infof("hasOverlay remove underlay group name:%s version:%s err:%s", name, version, err)
		err = q.GroupRemove(ctx, name, version, true)
		if err != nil {
			return fmt.Errorf("remove group %s-%s failed: %v", name, version, err)
		}
	} else {
		utils.CtxLogger(ctx).Sugar().Infof("noOverlay remove overlay group name:%s version:%s err:%s", name, version, err)
		err = q.GroupRemove(ctx, name, version, false)
		if err != nil {
			return fmt.Errorf("remove group %s-%s failed: %v", name, version, err)
		}
	}
	return q.QPackageStore.RemoveLocalGroupInfo(ctx, name)
}
func (q *QPackage) GroupRemove(ctx context.Context, name, version string, isUnderlay bool) (err error) {
	info, err := q.GetGroupVersionInfo(ctx, name, version)
	if err != nil {
		return
	}

	for _, v := range info.Schemes {
		if v.Type == qnexus.GroupTypeScheme {
			err = q.SchemeRemove(ctx, v.Name, v.Version, isUnderlay)
			if err != nil {
				return
			}
		} else if v.Type == qnexus.GroupTypeGroup {
			// group 不判断 targets,只根据 scheme 的 targets 来判断是否安装
			err = q.GroupRemove(ctx, v.Name, v.Version, isUnderlay)
			if err != nil {
				return fmt.Errorf("group %s-%s remove failed: %v", v.Name, v.Version, err)
			}
		}
	}
	return nil
}
func (q *QPackage) SchemeInstallAll(ctx context.Context, name, version string) (res *InstallDryRunRes, err error) {
	hasOverlay, err := utils.HasOverlay()
	if err != nil {
		return nil, fmt.Errorf("check overlay failed: %v", err)
	}

	taskID := uuid.NewString()
	startAt := time.Now()
	defer func() {
		h := qhistory.History[InstallStatus]{
			Bucket:  qhistory.BucketTypeScheme,
			Name:    name,
			Version: version,
			TaskID:  taskID,
			StartAt: qutils.Time(startAt),
			EndAt:   qutils.Time(time.Now()),
			Detail: InstallStatus{
				Action: InstallAction,
				Status: ActionSuccess,
			},
			Desc: "",
		}
		if err != nil {
			h.Detail.Status = ActionFailed
			h.Desc = fmt.Sprintf("install scheme %s-%s failed: %v", name, version, err)
		}
		err1 := q.installHistory.Add(ctx, h)
		if err1 != nil {
			utils.CtxLogger(ctx).Error("save scheme history failed", zap.Error(err1))
		}
	}()

	if utils.GetCtxAllToEnvFlag(ctx) {
		utils.CtxLogger(ctx).Info("allToEnv install to", zap.String("env-path", utils.GetCtxEnvPath(ctx)), zap.String("name", name), zap.String("version", version), zap.Error(err))
		res, err = q.SchemeInstall(ctx, name, version, nil, false)
		if err != nil {
			return nil, fmt.Errorf("allToEnv install scheme %s=%s failed: %v", name, version, err)
		}
		return res, nil
	}

	// 如果有 overlay,先安装到 overlay,如果安装失败，不再安装到 underlay
	if hasOverlay {
		utils.CtxLogger(ctx).Info(fmt.Sprintf("hasOverlay install to overlay, name:%s, version:%s, err:%v", name, version, err))
		_, err = q.SchemeInstall(ctx, name, version, nil, false)
		if err != nil {
			return nil, fmt.Errorf("install scheme %s=%s failed: %v", name, version, err)
		}
		utils.CtxLogger(ctx).Info(fmt.Sprintf("hasOverlay install to underlay, name:%s, version:%s, err:%v", name, version, err))
		res, err = q.SchemeInstall(ctx, name, version, nil, true)
		if err != nil {
			return nil, fmt.Errorf("install scheme %s=%s failed: %v", name, version, err)
		}
	} else {
		utils.CtxLogger(ctx).Info(fmt.Sprintf("noOverlay install to overlay, name:%s, version:%s, err:%v", name, version, err))
		res, err = q.SchemeInstall(ctx, name, version, nil, false)
		if err != nil {
			return nil, fmt.Errorf("install scheme %s=%s failed: %v", name, version, err)
		}
	}
	return res, nil
}
func (q *QPackage) SchemeInstallAllDryRun(ctx context.Context, name, version string) (res *InstallDryRunRes, err error) {
	if utils.GetCtxAllToEnvFlag(ctx) {
		utils.CtxLogger(ctx).Info("allToEnv install dry run to", zap.String("env-path", utils.GetCtxEnvPath(ctx)), zap.String("name", name), zap.String("version", version), zap.Error(err))
		res, err = q.SchemeInstallDryRun(ctx, name, version, false)
		if err != nil {
			return nil, fmt.Errorf("allToEnv install dry run scheme %s=%s failed: %v", name, version, err)
		}
		return res, nil
	}

	hasOverlay, err := utils.HasOverlay()
	if err != nil {
		return nil, fmt.Errorf("check overlay failed: %v", err)
	}
	// 如果有 overlay,先安装到 overlay,如果安装失败，不再安装到 underlay
	if hasOverlay {
		utils.CtxLogger(ctx).Info(fmt.Sprintf("hasOverlay install dry run to overlay, name:%s, version:%s, err:%v", name, version, err))
		res, err = q.SchemeInstallDryRun(ctx, name, version, false)
		if err != nil {
			return nil, fmt.Errorf("install dry run scheme %s-%s failed: %v", name, version, err)
		}
		utils.CtxLogger(ctx).Info(fmt.Sprintf("hasOverlay install dry run to underlay, name:%s, version:%s, err:%v", name, version, err))
		_, err = q.SchemeInstallDryRun(ctx, name, version, true)
		if err != nil {
			return nil, fmt.Errorf("install dry run scheme %s-%s failed: %v", name, version, err)
		}
	} else {
		utils.CtxLogger(ctx).Info(fmt.Sprintf("noOverlay install dry run to overlay, name:%s, version:%s, err:%v", name, version, err))
		res, err = q.SchemeInstallDryRun(ctx, name, version, false)
		if err != nil {
			return nil, fmt.Errorf("install dry run scheme %s-%s failed: %v", name, version, err)
		}
	}
	return res, nil
}

func (q *QPackage) SchemeInstall(ctx context.Context, name, version string, group *SchemeInstallFrom, isUnderlay bool) (res *InstallDryRunRes, err error) {
	info, err := q.GetSchemeVersionInfo(ctx, name, version)
	if err != nil {
		return nil, err
	}
	return q.schemeInstall(ctx, info, group, isUnderlay)
}

func (q *QPackage) SchemeInstallDryRun(ctx context.Context, name, version string, isUnderlay bool) (res *InstallDryRunRes, err error) {
	info, err := q.GetSchemeVersionInfo(ctx, name, version)
	if err != nil {
		return nil, err
	}
	return q.schemeInstallDryRun(ctx, info, isUnderlay)
}

func (q *QPackage) schemeInstallDryRun(ctx context.Context, info *qnexus.NexusIntegration, isUnderlay bool) (res *InstallDryRunRes, err error) {
	res = &InstallDryRunRes{
		Name:            info.Name,
		RequiredVersion: info.Version,
		Type:            qnexus.GroupTypeScheme,
	}
	allToEnv := utils.GetCtxAllToEnvFlag(ctx)
	if allToEnv && isUnderlay {
		return nil, errors.New("allToEnv和isUnderlay不能同时为true")
	}
	local := q.GetLocalSchemeInfo(ctx, info.Name)
	res.LocalVersion = local.Version
	if !q.isAllowSchemeInstall(info.Targets) {
		qlog.Debugf("target:%+v not allow install,host:%s-%d, scheme:%s, version:%s",
			info.Targets, q.opt.HostType, q.opt.HostID, info.Name, info.Version)
		return res, nil
	}
	res.AllowInstall = true
	qlog.Debugf("target:%+v allow install,host:%s-%d, scheme:%s, version:%s",
		info.Targets, q.opt.HostType, q.opt.HostID, info.Name, info.Version)
	var installedMap, envInstalledMap PackageInfo
	if !allToEnv {
		installedMap, err = getLocalInstalledPkgList(ctx, isUnderlay)
		if err != nil {
			return nil, err
		}
	}
	envInstalledMap, err = getEnvLocalAllPkgListVersion(ctx)
	if err != nil {
		return nil, err
	}

	moduleDebs := info.Modules.GetDebs()
	ignoreDepends := utils.GetCtxIgnoreDepends(ctx)
	if ignoreDepends != "" {
		moduleDebs = lo.Filter(moduleDebs, func(item qnexus.NexusIntegrationModule, _ int) bool {
			return !slices.Contains(strings.Split(ignoreDepends, ","), item.PkgName)
		})
	}

	// Add dbg package logic
	if utils.GetCtxInstallDbgPackage(ctx) {
		// Query for all -dbg packages once
		req := devops.ModuleVersionListReq{
			PkgName:  "-dbg",
			RepoName: "dev",
			PageNum:  1,
			PageSize: 1000,
		}
		dbgModules, err := q.DevopsClient.ModuleVersionList(req)
		if err != nil {
			qlog.Warnf("Failed to get all -dbg module version list: %v", err)
		} else {
			dbgModuleMap := make(map[string]struct{})
			for _, mod := range dbgModules {
				dbgModuleMap[mod.PkgName] = struct{}{}
			}

			// Iterate through original modules and check for corresponding -dbg packages
			for _, m := range info.Modules.GetDebs() {
				dbgPkgName := m.PkgName + "-dbg"
				if _, ok := dbgModuleMap[dbgPkgName]; ok {
					moduleDebs = append(moduleDebs, qnexus.NexusIntegrationModule{
						Name:       m.Name,
						PkgName:    dbgPkgName,
						Version:    m.Version,
						CommitID:   m.CommitID,
						CreateTime: m.CreateTime,
						Labels:     m.Labels,
						ModuleType: m.ModuleType,
						Arch:       m.Arch,
						RepoName:   "dev",
					})
				}
			}
		}
	}

	for _, v := range moduleDebs {
		if !q.isAllowModuleInstall(v.Labels, isUnderlay) {
			qlog.Debugf("not allow install, pkg_name:%s, code_name:%s, model_dcu:%s",
				v.PkgName, q.opt.linuxCodeName, q.opt.modelDCU)
			continue
		}
		// 含有env-path的标签，排除env-path值不一致的包
		envPath := utils.GetCtxEnvPath(ctx)
		if v.Labels.GetEnvPath() != "" && envPath != v.Labels.GetEnvPath() {
			qlog.Debugf("env-path not same, skip install, from label:%s, from ctx:%s",
				v.Labels.GetEnvPath(), envPath)
			continue
		}
		var isDiff bool
		var installedVersion string
		if allToEnv {
			installedVersion = envInstalledMap[v.PkgName].Version()
		} else if v.Labels.HasDpkgInstallWithSystem() {
			installedVersion = installedMap[v.PkgName].Version()
			isDiff = installedVersion != v.Version
			if !isDiff {
				installedVersion = envInstalledMap[v.PkgName].Version()
			}
		} else if v.Labels.HasDpkgInstall() {
			installedVersion = envInstalledMap[v.PkgName].Version()
		} else {
			installedVersion = installedMap[v.PkgName].Version()
		}
		isDiff = installedVersion != v.Version
		// 只允许升级，不允许降级
		if v.Labels.HasOnlyUpgrade() {
			if installedVersion != "" {
				installedModuleVersion, err := qnexus.NewModuleVersion(installedVersion)
				if err != nil {
					return nil, err
				}
				toInstallModuleVersion, err := qnexus.NewModuleVersion(v.Version)
				if err != nil {
					return nil, err
				}
				if toInstallModuleVersion.GetCode() < installedModuleVersion.GetCode() {
					return nil, fmt.Errorf("%s has label %v, not allow to downgrade from %s to %s",
						v.PkgName, v.Labels, installedVersion, v.Version)
				}
			}
		}
		localArch, err := utils.GetArch()
		if err != nil {
			return nil, err
		}
		if v.Arch == "all" {
			localArch = "all"
		}
		// qomolo-service 只在 overlay 安装
		res.Modules = append(res.Modules, PkgInfo{
			PkgName:         v.PkgName,
			LocalVersion:    installedVersion,
			RequiredVersion: v.Version,
			RepoName:        v.RepoName,
			Arch:            qnexus.ArchType(localArch),
			Labels:          v.Labels,
			IsDiff:          isDiff,
			HasDiff:         isDiff,
		})
		if isDiff {
			res.HasDiff = true
			res.IsDiff = true
		}
	}

	return res, nil
}

func (q *QPackage) schemeInstall(ctx context.Context, info *qnexus.NexusIntegration, group *SchemeInstallFrom, isUnderlay bool) (res *InstallDryRunRes, err error) {
	res = new(InstallDryRunRes)
	if !q.isAllowSchemeInstall(info.Targets) {
		qlog.Debugf("target:%+v not allow install,host:%s-%d, scheme:%s, version:%s",
			info.Targets, q.opt.HostType, q.opt.HostID, info.Name, info.Version)
		return nil, nil
	}
	qlog.Debugf("target:%+v allow install,host:%s-%d, scheme:%s, version:%s",
		info.Targets, q.opt.HostType, q.opt.HostID, info.Name, info.Version)
	run, err := q.schemeInstallDryRun(ctx, info, isUnderlay)
	if err != nil {
		return nil, err
	}

	if run == nil {
		qlog.Debugf("dry run return nil, scheme:%s, version:%s", info.Name, info.Version)
		return nil, nil
	}

	allToEnv := utils.GetCtxAllToEnvFlag(ctx)
	installList := run.AptPkgs(allToEnv)
	dpkgInstallList := run.DpkgPkgs(isUnderlay, allToEnv)
	if len(installList) > 0 {
		err = q.AptUtil.InstallWithUpdate(ctx, installList, isUnderlay, q.GetSourceFile(), q.opt.pkgInstallDoneCb)
		if err != nil {
			return nil, fmt.Errorf("install %s-%s failed: %v", info.Name, info.Version, err)
		}
	}

	if len(dpkgInstallList) > 0 {
		err = q.schemeInstallDpkgPkgs(ctx, dpkgInstallList, isUnderlay)
		if err != nil {
			return nil, fmt.Errorf("install %s-%s failed: %v", info.Name, info.Version, err)
		}
	}

	_ = q.QPackageStore.SetLocalSchemeInfo(ctx, info.Name, info.Version, group)
	err = q.sync(ctx, isUnderlay)
	if err != nil {
		return nil, err
	}
	return res, nil
}

func (q *QPackage) schemeInstallDpkgPkgs(ctx context.Context, dpkgInstallList []PkgInfo, isUnderlay bool) (err error) {
	timestamp := time.Now().Unix()
	tmpDownloadDebDir := fmt.Sprintf("%s/%v", debDownloadDir, timestamp)
	_, err = os.Stat(tmpDownloadDebDir)
	if os.IsNotExist(err) {
		err = os.MkdirAll(tmpDownloadDebDir, 0755)
		if err != nil {
			return err
		}
	}

	ignoredPkgs := make([]string, 0)
	ignoreDepends := utils.GetCtxIgnoreDepends(ctx)
	if ignoreDepends != "" {
		ignoredPkgs = strings.Split(ignoreDepends, ",")
	}

	filteredDpkgInstallList := make([]PkgInfo, 0)
	for _, pkg := range dpkgInstallList {
		isIgnored := slices.Contains(ignoredPkgs, pkg.PkgName)
		if isIgnored {
			qlog.Debugf("skip download/install pkg: %s, because it is in ignoreDepends", pkg.PkgName)
			continue
		}
		filteredDpkgInstallList = append(filteredDpkgInstallList, pkg)
	}

	for _, pkg := range filteredDpkgInstallList {
		pkgFullName, downloadURL := GetRepoDownloadURL(pkg.PkgName, pkg.RequiredVersion, pkg.RepoName, string(pkg.Arch), q.opt.RepoURL, q.opt.RepoMode)
		archivesSaveFile := aptArchivesDir + pkgFullName
		tmpSaveFile := tmpDownloadDebDir + "/" + pkgFullName

		var debInfo *qnexus.Asset
		debInfo, err = q.GetDebInfo(ctx, pkg.PkgName, pkg.RequiredVersion, pkg.RepoName, string(pkg.Arch), downloadURL)
		if err != nil {
			return fmt.Errorf("get deb info err:%v, pkg:%+v", err, pkg)
		}

		if debInfo == nil {
			return fmt.Errorf("get deb info empty, pkg:%+v", pkg)
		}

		// 检查deb存放目录是否有同名deb，如果有则校验md5或etag，校验通过则跳过下载，直接从Deb存放目录复制到临时目录
		if utils.CheckFileExist(archivesSaveFile) {
			if DownloadFileVerify(archivesSaveFile, debInfo) == nil {
				qlog.Infof("file exist and verify success, copy from archives: %s", archivesSaveFile)
				_, err = utils.CopyFile(archivesSaveFile, tmpSaveFile)
				if err != nil {
					return fmt.Errorf("copy file from archives to tmp failed: %v", err)
				}
				continue // 跳过下载
			}
		}

		// 下载到临时目录
		err = q.DownloadDeb(
			ctx,
			pkg.PkgName,
			pkg.RequiredVersion,
			pkg.RepoName,
			string(pkg.Arch),
			tmpDownloadDebDir,
		)
		if err != nil {
			return err
		}

		// 下载完成后，复制到一份deb存放目录
		_, err = utils.CopyFile(tmpSaveFile, archivesSaveFile)
		if err != nil {
			qlog.Errorf("copy file from tmp to archives failed: %v", err)
		}

		// 修改文件的mtime,用于缓存
		var mTime time.Time
		if q.opt.RepoMode == RepoModeProjectOnly {
			mTime, _ = time.Parse(time.RFC1123, debInfo.Mtime)
		}
		if q.opt.RepoMode == RepoModeWWLOnly {
			mTime, _ = time.Parse(time.RFC3339, debInfo.Mtime)
		}
		if !mTime.IsZero() {
			err1 := os.Chtimes(archivesSaveFile, time.Time{}, mTime)
			if err1 != nil {
				qlog.Warnf("ch file:%s mtime:%s", archivesSaveFile, debInfo.Mtime)
			}
		}
	}
	err = q.DpkgUtil.InstallWithUpdate(ctx, filteredDpkgInstallList, isUnderlay, tmpDownloadDebDir, q.opt.pkgInstallDoneCb)
	if err != nil {
		return err
	}
	_ = os.RemoveAll(tmpDownloadDebDir)
	return nil
}

func (q *QPackage) sync(ctx context.Context, isUnderlay bool) error {
	// sync to disk
	syncOutput, err := execCmd(ctx, "sync", isUnderlay).Output()
	if err != nil {
		qlog.Errorf("sync error:%vs, output:%s", err, string(syncOutput))
		return err
	}
	syncFOutput, err := execCmd(ctx, "sync -f", isUnderlay).Output()
	if err != nil {
		qlog.Errorf("sync -f error:%v, output:%s", err, string(syncFOutput))
		return err
	}
	return nil
}

func (q *QPackage) SchemeDownload(ctx context.Context, name, version, arch, path string) error {
	err2 := utils.HasRootPrivilege()
	if err2 != nil {
		qlog.Errorf("%v", err2)
		return err2
	}

	info, err := q.GetSchemeVersionInfo(ctx, name, version)
	if err != nil {
		qlog.Errorf("%s %s %v", name, version, err)
		return err
	}
	versionJSON := fmt.Sprintf("%s/%s_%s.json", path, name, version)
	err = utils.WriteToJSON(versionJSON, info)
	if err != nil {
		return err
	}

	if string(info.Arch) != arch && info.Arch != qnexus.ArchAll {
		return errors.New("arch not mach, default 'arm64', if 'amd64' please add --arch=amd64")
	}
	for _, v := range info.Modules {
		if v.ModuleType == qnexus.ModuleDeb {
			localArch := arch
			if v.Arch == qnexus.ArchAll {
				localArch = string(qnexus.ArchAll)
			}
			err = q.DownloadDeb(ctx, v.PkgName, v.Version, v.RepoName, localArch, path)
			if err != nil {
				return err
			}
		}
	}
	return nil
}

func (q *QPackage) SchemeInfo(ctx context.Context, scheme, format string, full bool, level int) (map[string]interface{}, error) {
	local := q.GetLocalSchemeInfo(ctx, scheme)
	if local == (SchemeInfo{}) {
		return nil, fmt.Errorf("%s local not found", scheme)
	}
	if local.Version == "" {
		qlog.Infof("%s local version not found", scheme)
		return nil, fmt.Errorf("%s local version not found", scheme)
	}
	infoMap := map[string]interface{}{}
	if full {
		info, err := q.GetSchemeVersionInfo(context.Background(), scheme, local.Version)
		if err != nil {
			qlog.Errorf("%v", err)
			return nil, err
		}

		var modules []map[string]interface{}
		for _, v := range info.Modules.GetDebs() {
			if !q.isAllowModuleInstall(v.Labels, false) {
				continue
			}
			localVer, err := q.GetAndDiffVersion(context.Background(), v.PkgName)
			if err != nil {
				return nil, err
			}
			diff := ""
			infoModuleMap := map[string]interface{}{}

			if len(localVer) > 0 {
				if localVer != v.Version {
					diff = "not equal"
				}
			} else {
				if len(localVer) == 0 {
					diff = "not install"
				} else {
					diff = "removed,config remains"
				}
			}
			infoModuleMap["module_name"] = v.PkgName
			infoModuleMap["module_version"] = v.Version
			infoModuleMap["local_version"] = localVer
			infoModuleMap["diff"] = diff
			modules = append(modules, infoModuleMap)
		}
		infoMap["name"] = scheme
		infoMap["version"] = local.Version
		infoMap["type"] = info.Type
		infoMap["created"] = time.Unix(info.CreateTime, 0).String()
		infoMap["updated"] = time.Unix(info.UpdateTime, 0).String()
		infoMap["modules"] = modules
	} else {
		infoMap["name"] = scheme
		infoMap["version"] = local.Version

	}
	if level == 0 {
		b, err := json.Marshal(infoMap)
		if err != nil {
			return nil, err
		}
		if format == "json" {
			fmt.Println(string(b))
		} else {
			dst := &bytes.Buffer{}
			if err := json.Indent(dst, b, "", "  "); err != nil {
				log.Println(err)
				return nil, err
			}

			fmt.Println(dst.String())
		}
	}
	return infoMap, nil
}

func (q *QPackage) GroupInfo(ctx context.Context, group, format string, full bool, level int) (map[string]interface{}, error) {
	local := q.GetLocalGroupInfo(ctx, group)
	if local == (GroupInfo{}) {
		return nil, fmt.Errorf("%s local not found", group)
	}
	if local.Version == "" {
		qlog.Infof("%s local version not found", group)
		return nil, fmt.Errorf("%s local version not found", group)
	}
	infoMap := map[string]interface{}{}
	if full { // 获取所有信息
		info, err := q.GetGroupVersionInfo(context.Background(), local.Name, local.Version)
		if err != nil {
			qlog.Errorf("%v", err)
			return nil, err
		}

		var infoSchemes []map[string]interface{}
		infoMap["name"] = info.Name
		infoMap["version"] = info.Version
		infoMap["created"] = time.Unix(info.CreateTime, 0).String()
		infoMap["updated"] = time.Unix(info.UpdateTime, 0).String()

		for _, v := range info.Schemes {
			if v.Type == qnexus.GroupTypeScheme {
				m, err := q.SchemeInfo(ctx, v.Name, format, full, level+1)
				if err != nil {
					return nil, err
				}
				infoSchemes = append(infoSchemes, m)
			} else if v.Type == qnexus.GroupTypeGroup {
				m, err := q.GroupInfo(ctx, v.Name, format, full, level+1)
				if err != nil {
					return nil, err
				}
				infoSchemes = append(infoSchemes, m)
			}
		}
		infoMap["schemes"] = infoSchemes
	} else {
		infoMap["name"] = local.Name
		infoMap["version"] = local.Version
	}
	if level == 0 {
		b, err := json.Marshal(infoMap)
		if err != nil {
			log.Println(err)
			return nil, err
		}
		if format == "json" {
			fmt.Println(string(b))
		} else {
			dst := &bytes.Buffer{}
			if err := json.Indent(dst, b, "", "  "); err != nil {
				log.Println(err)
				return nil, err
			}

			fmt.Println(dst.String())
		}
	}
	return infoMap, nil
}

func (q *QPackage) SchemeRemoveAll(ctx context.Context, name, version string) (err error) {

	if utils.GetCtxAllToEnvFlag(ctx) {
		utils.CtxLogger(ctx).Info("allToEnv remove ", zap.String("env-path", utils.GetCtxEnvPath(ctx)), zap.String("name", name), zap.String("version", version), zap.Error(err))
		err = q.SchemeRemove(ctx, name, version, false)
		if err != nil {
			return fmt.Errorf("allToEnv remove scheme %s=%s failed: %v", name, version, err)
		}
		return q.QPackageStore.RemoveLocalSchemeInfo(ctx, name)
	}

	hasOverlay, err := utils.HasOverlay()
	if err != nil {
		return fmt.Errorf("check overlay failed: %v", err)
	}
	// 如果有 overlay,先安装到 overlay,如果安装失败，不再安装到 underlay
	if hasOverlay {
		utils.CtxLogger(ctx).Sugar().Infof("hasOverlay remove overlay scheme name:%s version:%s err:%s", name, version, err)
		err = q.SchemeRemove(ctx, name, version, false)
		if err != nil {
			return fmt.Errorf("remove %s-%s failed: %v", name, version, err)
		}
		utils.CtxLogger(ctx).Sugar().Infof("hasOverlay remove underlay scheme name:%s version:%s err:%s", name, version, err)
		err = q.SchemeRemove(ctx, name, version, true)
		if err != nil {
			return fmt.Errorf("remove %s-%s failed: %v", name, version, err)
		}
	} else {
		utils.CtxLogger(ctx).Sugar().Infof("noOverlay remove overlay scheme name:%s version:%s err:%s", name, version, err)
		err = q.SchemeRemove(ctx, name, version, false)
		if err != nil {
			return fmt.Errorf("remove %s-%s failed: %v", name, version, err)
		}
	}
	return q.QPackageStore.RemoveLocalSchemeInfo(ctx, name)
}

func (q *QPackage) SchemeRemove(ctx context.Context, name, version string, isUnderlay bool) (err error) {
	info, err := q.GetSchemeVersionInfo(ctx, name, version)
	if err != nil {
		return
	}
	removeListApt := make([]string, 0)
	removeListDpkg := make([]string, 0)
	// FIXME: 考虑不需要安装的包是否要移除？
	for _, v := range info.Modules.GetDebs() {
		if utils.GetCtxAllToEnvFlag(ctx) {
			removeListDpkg = append(removeListDpkg, v.PkgName)
			continue
		}
		if v.Labels.HasDpkgInstall() || v.Labels.HasDpkgInstallWithSystem() {
			if v.Labels.AllowServiceInstall(isUnderlay) {
				version, err = getDpkgLocalInstallPkgVersion(ctx, v.PkgName, isUnderlay)
				if err != nil {
					return err
				}
				if version == v.Version {
					removeListDpkg = append(removeListDpkg, v.PkgName)
				}
			}
			if v.Labels.HasDpkgInstallWithSystem() {
				removeListApt = append(removeListApt, v.PkgName+"="+v.Version)
			}
		} else {
			removeListApt = append(removeListApt, v.PkgName+"="+v.Version)
		}
	}
	if len(removeListApt) > 0 {
		err = q.AptUtil.Remove(ctx, removeListApt)
	}
	if len(removeListDpkg) > 0 {
		err = q.DpkgUtil.Remove(ctx, removeListDpkg)
	}
	if err != nil {
		return fmt.Errorf("remove scheme %s failed", name)
	}
	return nil
}

func (q *QPackage) GetGroupAllModules(ctx context.Context, name, version string) ([]qnexus.NexusIntegrationModule, error) {
	info, err := q.GetGroupVersionInfo(context.Background(), name, version)
	if err != nil {
		qlog.Errorf("%v", err)
		return nil, err
	}
	modules := make([]qnexus.NexusIntegrationModule, 0)
	for _, v := range info.Schemes {
		if v.Type == qnexus.GroupTypeScheme {
			versionInfo, err := q.GetSchemeVersionInfo(context.Background(), v.Name, v.Version)
			if err != nil {
				return nil, err
			}
			for _, m := range versionInfo.Modules.GetDebs() {
				if !q.isAllowModuleInstall(m.Labels, false) {
					continue
				}
				modules = append(modules, m)
			}
		} else if v.Type == qnexus.GroupTypeGroup {
			return q.GetGroupAllModules(ctx, v.Name, v.Version)
		}
	}
	return modules, nil
}

func (q *QPackage) GroupDiffLocal(ctx context.Context, name, version string) *InstallDryRunRes {
	res := &InstallDryRunRes{
		Name:            name,
		Type:            qnexus.GroupTypeGroup,
		RequiredVersion: version,
		AllowInstall:    true,
	}
	groupInfo := q.QPackageStore.GetLocalGroupInfo(ctx, name)
	res.LocalVersion = groupInfo.Version
	if version != groupInfo.Version {
		res.HasDiff = true
		res.IsDiff = true
	}
	return res
}
func (q *QPackage) isAllowModuleInstall(labels qnexus.CiModuleLabels, isUnderlay bool) bool {
	return labels.IsAllowInstall(qnexus.AllowInstallArgs{
		CodeName:    string(q.opt.linuxCodeName),
		ModelDCU:    q.opt.modelDCU,
		ProjectName: q.opt.Project,
		VehicleType: q.opt.VehicleType,
		IsUnderlay:  isUnderlay,
		Chassis:     q.opt.Chassis,
	})
}

func (q *QPackage) isAllowSchemeInstall(targets qnexus.SchemeTargetList) bool {
	if q.opt.SkipCheckTarget && q.opt.HostType == "" && q.opt.HostID == 0 {
		return true
	}
	return targets.IsAllowInstall(q.opt.HostType, q.opt.HostID)
}

func (q *QPackage) AptInstallAll(ctx context.Context, installList []PkgInfo) error {
	hasOverlay, err := utils.HasOverlay()
	if err != nil {
		return fmt.Errorf("check overlay failed: %v", err)
	}
	// 如果有 overlay,先安装到 overlay,如果安装失败，不再安装到 underlay
	if hasOverlay {
		ok, err := utils.IsInChroot()
		if err != nil {
			return err
		}
		if ok {
			return fmt.Errorf("forbid install pkg in underlay")
		}

		err = q.AptInstall(ctx, installList, false)
		if err != nil {
			return err
		}
		err = q.AptInstall(ctx, installList, true)
		if err != nil {
			return err
		}
	} else {
		err = q.AptInstall(ctx, installList, false)
		if err != nil {
			return err
		}
	}
	return nil
}

func (q *QPackage) AptInstall(ctx context.Context, installList []PkgInfo, isUnderlay bool) error {
	sourceFilePath := q.GetSourceFile()
	return q.AptUtil.InstallWithUpdate(ctx, installList, isUnderlay, sourceFilePath, nil)
}

func (q *QPackage) AptUpdate(ctx context.Context, isUnderlay bool) error {
	sourceFilePath := q.GetSourceFile()
	return q.AptUtil.Update(ctx, sourceFilePath, isUnderlay)
}

func (q *QPackage) AptGetPkgLatestVersion(ctx context.Context, pkgName string) (string, error) {
	sourceFilePath := q.GetSourceFile()
	return q.AptUtil.GetPkgLatestVersion(ctx, pkgName, sourceFilePath)
}

func (q *QPackage) GetSourceFile() string {
	return q.AptConfig.GetSourceFile(q.opt.SourceType)
}

func (q *QPackage) DpkgInstall(ctx context.Context, installList []PkgInfo, pkgPathDir string, isUnderlay bool) error {
	return q.DpkgUtil.Install(ctx, installList, isUnderlay, pkgPathDir, nil)
}

func (q *QPackage) DownloadDeb(ctx context.Context, pkg, version, repo, arch, path string) error {
	pkgFullName, downloadURL := GetRepoDownloadURL(pkg, version, repo, arch, q.opt.RepoURL, q.opt.RepoMode)

	saveFile := path + "/" + pkgFullName
	client := resty.New()
	client.
		SetRetryCount(2).
		SetRetryWaitTime(3 * time.Second).
		SetRetryMaxWaitTime(10 * time.Second)
	client.SetOutputDirectory(path)
	resp, err := client.R().SetOutput(saveFile).Get(downloadURL)
	if err != nil {
		return err
	}

	if resp.StatusCode() != http.StatusOK {
		_ = os.Remove(saveFile)
		return fmt.Errorf("request %s failed, code %v", downloadURL, resp.StatusCode())
	}
	qlog.Infof("downloaded:%s", saveFile)
	return nil
}

func (q *QPackage) SchemeInstallFromZip(_ context.Context, name, version, file string) error {
	err := utils.HasRootPrivilege()
	if err != nil {
		qlog.Errorf("%v", err)
		return err
	}
	targetDir := fmt.Sprintf("%s/%s/%s", tmpDownloadDir, name, version)
	err = utils.CleanTempDownloadFiles(targetDir)
	if err != nil {
		return err
	}
	err = utils.UnpackFromZip(file, targetDir)
	if err != nil {
		return err
	}
	versionJSON := fmt.Sprintf("%s/%s_%s.json", targetDir, name, version)
	infoJSON, err := utils.LoadFromJSON(versionJSON, &qnexus.NexusIntegration{})
	if err != nil {
		qlog.Errorf("%v", err)
		return err
	}
	info, ok := infoJSON.(*qnexus.NexusIntegration)
	if !ok {
		return fmt.Errorf("load %s failed", versionJSON)
	}

	_, err = q.schemeInstall(context.Background(), info, nil, false)
	if err != nil {
		return err
	}
	return utils.CleanTempDownloadFiles(targetDir)
}

func (q *QPackage) GroupInstallFromZip(ctx context.Context, name, version, file string) error {
	err := utils.HasRootPrivilege()
	if err != nil {
		qlog.Errorf("%v", err)
		return err
	}

	targetDir := fmt.Sprintf("%s/%s/%s", tmpDownloadDir, name, version)
	err = utils.CleanTempDownloadFiles(targetDir)
	if err != nil {
		return err
	}

	err = utils.UnpackFromZip(file, targetDir)
	if err != nil {
		return err
	}
	versionJSON := fmt.Sprintf("%s/%s_%s.json", targetDir, name, version)
	var info *qnexus.NexusIntegrationGroup
	infoJSON, err := utils.LoadFromJSON(versionJSON, &qnexus.NexusIntegrationGroup{})
	if err != nil {
		qlog.Errorf("%v", err)
		return err
	}
	info, ok := infoJSON.(*qnexus.NexusIntegrationGroup)
	if !ok {
		return fmt.Errorf("infoJSON is not *qnexus.NexusIntegrationGroup")
	}
	_, errs := q.groupInstall(ctx, info, nil, false)
	if len(errs) != 0 {
		return fmt.Errorf("install %s-%s failed: %v", name, version, errs)
	}
	return utils.CleanTempDownloadFiles(targetDir)
}

func GetRepoDownloadURL(pkg, version, repo, arch, repoURL string, repoMode RepoMode) (pkgFullName, downloadURL string) {
	prefix := pkg[0:1]
	pkgFullName = pkg + "_" + version + "_" + arch + ".deb"
	if repoMode == RepoModeWWLOnly {
		downloadURL = strings.Join([]string{repoURL, "repository", repo, "pool", prefix, pkg, pkgFullName}, "/")
	}

	if repoMode == RepoModeProjectOnly {
		// TODO 跟 package中的方法合并,改成单独的函数,不依赖 d *Downloader,写测试用例 (DONE)
		// 现场的 repo 前置是 /repository/repo/
		downloadURL = strings.Join([]string{repoURL, "repository", "repo", repo, "pool", "main", prefix, pkg, pkgFullName}, "/")
	}
	return pkgFullName, downloadURL
}

func DownloadFileVerify(file string, debInfo *qnexus.Asset) error {
	ctx := context.Background()
	if !utils.CheckFileExist(file) {
		return fmt.Errorf("file not exist")
	}

	if debInfo.Md5 != "" {
		md5, err := utils.GetMD5Ctx(ctx, file)
		if err != nil {
			return fmt.Errorf("md5: %w", err)
		}
		if md5 != debInfo.Md5 {
			return fmt.Errorf("md5 of file %s is %s, not match with %s", file, md5, debInfo.Md5)
		}
		return nil
	}
	debInfoEtag := debInfo.ETag()
	fileEtag := utils.GetFileNginxETag(file)
	if fileEtag != debInfoEtag {
		return fmt.Errorf("etag of file %s is %s, not match with %s", file, fileEtag, debInfoEtag)
	}
	return nil
}

func (q *QPackage) GetGroupSaveFiles(ctx context.Context, name, version string) (map[string]*qnexus.Asset, error) {
	gdp, err := q.getGroupTotal(ctx, name, version)
	if err != nil {
		return nil, fmt.Errorf("get group total err:%v", err)
	}
	ret := make(map[string]*qnexus.Asset)
	if err := q.getSaveFilesFromGdp(gdp, ret); err != nil {
		return nil, err
	}
	return ret, nil
}

func (q *QPackage) getSaveFilesFromGdp(gdp *GroupDownloadProgress, ret map[string]*qnexus.Asset) error {
	ctx := context.Background()
	for si := range gdp.Schemes {
		for pi := range gdp.Schemes[si].Packages {
			ddp := &gdp.Schemes[si].Packages[pi]
			_, downloadURL := GetRepoDownloadURL(ddp.Name, ddp.Version, ddp.Repo, ddp.Arch, q.opt.RepoURL, q.opt.RepoMode)
			debInfo, err := q.GetDebInfo(ctx, ddp.Name, ddp.Version, ddp.Repo, ddp.Arch, downloadURL)
			if err != nil {
				return err
			}
			ret[gdp.Schemes[si].Packages[pi].saveFile()] = debInfo
		}
	}
	for gi := range gdp.Groups {
		if err := q.getSaveFilesFromGdp(gdp.Groups[gi], ret); err != nil {
			return err
		}
	}
	return nil
}

func (q *QPackage) DownloadGroupWithProgress(ctx context.Context, name, version, taskID string, optFns ...DownloadOptionFn) (err error) {
	opt := &DownloadOption{}
	for _, fn := range optFns {
		fn(opt)
	}

	progress, err := q.DownloaderPreCheck(context.Background(), qnexus.GroupTypeGroup, name, version)
	if err != nil {
		return err
	}
	err = q.Downloader.start(qnexus.GroupTypeGroup, name, version, taskID)
	if err != nil {
		return err
	}
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()
	go func() {
		select {
		case <-ctx.Done():
			if err != nil {
				qlog.Errorf("download group %s-%s failed: %v", name, version, err)
				q.Downloader.finish(FailedStatus, err.Error())
			} else {
				q.Downloader.finish(SuccessStatus, "")
			}
			return
		case <-q.Downloader.cancelChan:
			qlog.Infof("download group %s-%s canceled", name, version)
			q.Downloader.finish(CanceledStatus, "")
			cancel()
			return
		}
	}()
	groupDownloadProgress, ok := progress.(*GroupDownloadProgress)
	if !ok {
		return fmt.Errorf("progress is not GroupDownloadProgress")
	}
	q.Downloader.isStart = true
	q.Downloader.Progress = groupDownloadProgress

	if opt.needArchivesReady { // 需要等待档案文件准备完毕
		qlog.Infof("download group %s-%s begin to wait archives ready", name, version)
		if err := q.waitArchivesReady(ctx); err != nil {
			qlog.Errorf("download group %s-%s wait archives ready failed:%v", name, version, err)
			return err
		}
		qlog.Infof("download group %s-%s archives ready", name, version)
	}

	for si := range groupDownloadProgress.Schemes {
		for pi := range groupDownloadProgress.Schemes[si].Packages {
			err = q.DownloadDebWithProgress(ctx, &groupDownloadProgress.Schemes[si].Packages[pi])
			if err != nil {
				return err
			}
			err = q.ParseDebAndPullImages(ctx, &groupDownloadProgress.Schemes[si].Packages[pi])
			if err != nil {
				return err
			}
		}
	}
	for gi := range groupDownloadProgress.Groups {
		_, err = q.downloadGroup(ctx, groupDownloadProgress.Groups[gi])
		if err != nil {
			return err
		}
	}

	return nil
}

func (q *QPackage) waitArchivesReady(ctx context.Context) error {
	arCtx, arCancel := context.WithTimeout(ctx, time.Hour)
	defer arCancel()
	select {
	case <-arCtx.Done():
		return fmt.Errorf("ctx done")
	case archivesReady := <-q.Downloader.archivesReadyChan:
		if !archivesReady {
			return fmt.Errorf("archive not ready, abort")
		}
		return nil
	}
}

func (q *QPackage) downloadGroup(ctx context.Context, gdp *GroupDownloadProgress) (*GroupDownloadProgress, error) {
	for si := range gdp.Schemes {
		for pi := range gdp.Schemes[si].Packages {
			err := q.DownloadDebWithProgress(ctx, &gdp.Schemes[si].Packages[pi])
			if err != nil {
				return nil, err
			}
			err = q.ParseDebAndPullImages(ctx, &gdp.Schemes[si].Packages[pi])
			if err != nil {
				return nil, err
			}
		}
	}
	for gi := range gdp.Groups {
		_, err := q.downloadGroup(ctx, gdp.Groups[gi])
		if err != nil {
			return nil, err
		}
	}
	return gdp, nil
}

func (q *QPackage) GetSchemeSaveFiles(ctx context.Context, name, version string) (map[string]*qnexus.Asset, error) {
	sdp, err := q.getSchemeTotal(ctx, name, version)
	if err != nil {
		return nil, fmt.Errorf("get group total err:%v", err)
	}
	ret := make(map[string]*qnexus.Asset)
	for pi := range sdp.Packages {
		ddp := &sdp.Packages[pi]
		_, downloadURL := GetRepoDownloadURL(ddp.Name, ddp.Version, ddp.Repo, ddp.Arch, q.opt.RepoURL, q.opt.RepoMode)
		debInfo, err := q.GetDebInfo(ctx, ddp.Name, ddp.Version, ddp.Repo, ddp.Arch, downloadURL)
		if err != nil {
			return nil, err
		}
		ret[sdp.Packages[pi].saveFile()] = debInfo
	}
	return ret, nil
}

func (q *QPackage) DownloadSchemeWithProgress(ctx context.Context, name, version, taskID string, optFns ...DownloadOptionFn) (err error) {
	opt := &DownloadOption{}
	for _, fn := range optFns {
		fn(opt)
	}

	progress, err := q.DownloaderPreCheck(context.Background(), qnexus.GroupTypeScheme, name, version)
	if err != nil {
		return err
	}
	err = q.Downloader.start(qnexus.GroupTypeScheme, name, version, taskID)
	if err != nil {
		return err
	}
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()
	go func() {
		select {
		case <-ctx.Done():
			if err != nil {
				qlog.Errorf("download scheme %s-%s failed: %v", name, version, err)
				q.Downloader.finish(FailedStatus, err.Error())
			} else {
				qlog.Infof("download scheme %s-%s success", name, version)
				q.Downloader.finish(SuccessStatus, "")
			}
			return
		case <-q.Downloader.cancelChan:
			qlog.Infof("download scheme %s-%s canceled", name, version)
			q.Downloader.finish(CanceledStatus, "")
			cancel()
			return
		}
	}()
	schemeDownloadProgress, ok := progress.(*SchemeDownloadProgress)
	if !ok {
		return fmt.Errorf("progress is not *SchemeDownloadProgress")
	}
	q.Downloader.isStart = true
	q.Downloader.Progress = schemeDownloadProgress

	if opt.needArchivesReady { // 需要等待档案文件准备完毕
		qlog.Infof("download scheme %s-%s begin to wait archives ready", name, version)
		if err := q.waitArchivesReady(ctx); err != nil {
			qlog.Errorf("download scheme %s-%s wait archives ready failed:%v", name, version, err)
			return err
		}
		qlog.Infof("download scheme %s-%s archives ready", name, version)
	}

	for pi := range schemeDownloadProgress.Packages {
		err = q.DownloadDebWithProgress(ctx, &schemeDownloadProgress.Packages[pi])
		if err != nil {
			return err
		}
		err = q.ParseDebAndPullImages(ctx, &schemeDownloadProgress.Packages[pi])
		if err != nil {
			return err
		}
	}
	return err
}

func (q *QPackage) DownloadDebWithProgress(ctx context.Context, ddp *DebDownloadProgress) (err error) {
	_, downloadURL := GetRepoDownloadURL(ddp.Name, ddp.Version, ddp.Repo, ddp.Arch, q.opt.RepoURL, q.opt.RepoMode)
	saveFile := ddp.saveFile()

	var debInfo *qnexus.Asset
	debInfo, err = q.GetDebInfo(ctx, ddp.Name, ddp.Version, ddp.Repo, ddp.Arch, downloadURL)
	if err != nil {
		return fmt.Errorf("get deb info err:%v, ddp:%+v", err, *ddp)
	}

	if debInfo == nil {
		return fmt.Errorf("get deb info empty, ddp:%+v", *ddp)
	}

	exist := utils.CheckFileExist(saveFile)
	if exist {
		ddp.FullLocalPath = saveFile
		if debInfo.Md5 != "" {
			md5, err := utils.GetMD5Ctx(ctx, saveFile)
			if err != nil {
				return fmt.Errorf("md5: %w, file:%s", err, saveFile)
			}
			if md5 == debInfo.Md5 {
				qlog.Infof("md5 skip download: %s", debInfo.Name)
				ddp.Current = ddp.Total
				return nil
			}
		} else {
			etag := debInfo.ETag()
			if etag == utils.GetFileNginxETag(saveFile) {
				qlog.Infof("etag skip download: %s etag:%s", debInfo.Name, etag)
				ddp.Current = ddp.Total
				return nil
			}
		}
	}

	// download
	out, err := os.Create(saveFile)
	if err != nil {
		return err
	}
	defer func(file *os.File) {
		_ = file.Close()
	}(out)
	client := resty.New()
	client.SetRetryCount(2)
	client.SetRetryWaitTime(3 * time.Second)
	client.SetDoNotParseResponse(true)

	resp, err := client.R().SetContext(ctx).Get(downloadURL)
	if err != nil {
		return err
	}
	defer func(resp *resty.Response) {
		_ = resp.RawBody().Close()
	}(resp)

	if resp.StatusCode() != http.StatusOK {
		_ = os.Remove(saveFile)
		return fmt.Errorf("request %s failed, code %v", downloadURL, resp.StatusCode())
	}

	if _, err = io.Copy(out, io.TeeReader(resp.RawBody(), ddp)); err != nil {
		return err
	}

	// 修改文件的mtime,用于缓存
	var mTime time.Time
	if q.opt.RepoMode == RepoModeProjectOnly {
		mTime, _ = time.Parse(time.RFC1123, debInfo.Mtime)
	}
	if q.opt.RepoMode == RepoModeWWLOnly {
		mTime, _ = time.Parse(time.RFC3339, debInfo.Mtime)
	}
	err1 := os.Chtimes(saveFile, time.Time{}, mTime)
	if err1 != nil {
		qlog.Warnf("ch file:%s mtime:%s", saveFile, debInfo.Mtime)
	}

	ddp.FullLocalPath = saveFile
	return err
}

func (q *QPackage) DownloaderPreCheck(ctx context.Context, typ qnexus.GroupType, name, version string) (progress Progress, err error) {
	err = q.Downloader.PreCheck()
	if err != nil {
		return nil, err
	}
	defer q.Downloader.StopPreCheck()
	var total int64
	if typ == qnexus.GroupTypeGroup {
		gdp, err := q.getGroupTotal(ctx, name, version)
		if err != nil {
			return nil, err
		}
		total = gdp.GetTotal()
		progress = gdp
	} else if typ == qnexus.GroupTypeScheme {
		sdp, err := q.getSchemeTotal(ctx, name, version)
		if err != nil {
			return nil, err
		}
		total = sdp.GetTotal()
		progress = sdp
	} else {
		return nil, errors.New("unknown group type")
	}
	err = q.AptUtil.Clean(ctx, true)
	if err != nil {
		return nil, err
	}
	diskFree, err := utils.CheckDiskFree(aptArchivesDir)
	if diskFree < 3*total {
		return nil, PErrorNotEnoughFreeSpace
	}
	return progress, err
}

// getDebSize send HEAD request to get size
func (q *QPackage) getDebSize(_ context.Context, pkg, version, repo, arch string) (int64, error) {
	_, downloadURL := GetRepoDownloadURL(pkg, version, repo, arch, q.opt.RepoURL, q.opt.RepoMode)
	res, err := http.Head(downloadURL)
	if err != nil {
		return 0, err
	}
	defer func(resp *http.Response) {
		_ = resp.Body.Close()
	}(res)
	return res.ContentLength, err
}

func (q *QPackage) getSchemeTotal(ctx context.Context, name, version string) (*SchemeDownloadProgress, error) {
	// 获取scheme信息
	schemeInfo, err := q.GetSchemeVersionInfo(ctx, name, version)
	if err != nil {
		qlog.Errorf("%s %s %v", name, version, err)
		return nil, err
	}
	systemArch, err := utils.GetArch()
	if err != nil {
		return nil, err
	}
	if string(schemeInfo.Arch) != systemArch && schemeInfo.Arch != qnexus.ArchAll {
		fmt.Printf("scheme %s %s arch not match current system arch, skip\n", name, version)
		return nil, nil
	}
	// 按Labels过滤
	InstallDryRunRes, err := q.schemeInstallDryRun(ctx, schemeInfo, false)
	if err != nil {
		return nil, err
	}
	schemeDownloadProgress := &SchemeDownloadProgress{Name: name, Version: version}
	for _, pkg := range InstallDryRunRes.Modules {
		size, err := q.getDebSize(ctx, pkg.PkgName, pkg.RequiredVersion, pkg.RepoName, string(pkg.Arch))
		if err != nil {
			return nil, err
		}
		schemeDownloadProgress.Packages = append(schemeDownloadProgress.Packages,
			DebDownloadProgress{
				Name:     pkg.PkgName,
				Version:  pkg.RequiredVersion,
				Total:    size,
				Arch:     string(pkg.Arch),
				Repo:     pkg.RepoName,
				SavePath: aptArchivesDir,
			},
		)
	}
	return schemeDownloadProgress, err
}

func (q *QPackage) getGroupTotal(ctx context.Context, name, version string) (*GroupDownloadProgress, error) {
	err2 := utils.HasRootPrivilege()
	if err2 != nil {
		qlog.Errorf("perm err: %s", err2.Error())
		return nil, err2
	}

	info, err := q.GetGroupVersionInfo(ctx, name, version)
	if err != nil {
		qlog.Errorf("get group version err: %s", err.Error())
		return nil, err
	}

	if info.IsDelete && os.Getenv("ALLOW_INSTALL_DELETE") != "true" {
		return nil, errors.New("version is deleted,can't install")
	}

	groupDownloadProgress := &GroupDownloadProgress{Name: name, Version: version}

	for _, v := range info.Schemes {
		if v.Type == qnexus.GroupTypeScheme {
			sdp, err := q.getSchemeTotal(ctx, v.Name, v.Version)
			if err != nil {
				return nil, err
			}
			if sdp != nil {
				groupDownloadProgress.Schemes = append(groupDownloadProgress.Schemes, sdp)
			}
		} else if v.Type == qnexus.GroupTypeGroup {
			gdp, err := q.getGroupTotal(ctx, v.Name, v.Version)
			if err != nil {
				return nil, err
			}
			if gdp != nil {
				groupDownloadProgress.Groups = append(groupDownloadProgress.Groups, gdp)
			}
		}
	}
	return groupDownloadProgress, nil
}

func (q *QPackage) ResourceDelete(name, version string) error {
	dir := fmt.Sprintf("%s/%s/%s", utils.ModuleDownloadLocalPath, name, version)
	return os.RemoveAll(dir)
}

func (q *QPackage) DownloadModuleWithProgress(ctx context.Context, name, version, taskID string) (err error) {
	err = q.ModuleDownloader.start(qnexus.GroupTypeModule, name, version, taskID)
	if err != nil {
		return err
	}
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()
	go func() {
		select {
		case <-ctx.Done():
			if err != nil {
				qlog.Errorf("download module %s-%s failed: %v", name, version, err)
				q.ModuleDownloader.finish(FailedStatus, err.Error())
			} else {
				q.ModuleDownloader.finish(SuccessStatus, "")
			}
			return
		case <-q.ModuleDownloader.cancelChan:
			qlog.Infof("download module %s-%s canceled", name, version)
			q.ModuleDownloader.finish(CanceledStatus, "")
			cancel()
			return
		}
	}()
	progress := &qnexus.ModuleDownloadProgress{}
	q.ModuleDownloader.Progress = progress
	err = q.ModuleDownload(ctx, name, version, utils.ModuleDownloadLocalPath, progress)
	q.ModuleDownloader.isStart = true
	if err != nil {
		return err
	}
	return nil
}

func (q *QPackage) ModuleClean(ctx context.Context, name, version string) error {
	dir := utils.ResourceDir(name, version)
	return os.RemoveAll(dir)
}

func (q *QPackage) GetImageDomain() string {
	return q.opt.ImageDomain
}

func (q *QPackage) ParseDebAndPullImages(ctx context.Context, ddp *DebDownloadProgress) error {
	// 检查是否忽略拉取镜像
	if utils.GetCtxIgnorePullImages(ctx) {
		qlog.Debugf("忽略拉取镜像，跳过ParseDebAndPullImages")
		return nil
	}

	control, err := parseDebControl(ctx, ddp.FullLocalPath)
	if err != nil {
		return err
	}
	imageDomain := q.opt.ImageDomain
	for _, image := range control.RequiredImages {
		exists, err := checkLocalImageExists(ctx, image)
		if err != nil {
			return err
		}
		if exists {
			qlog.Debugf("image: %s exists, skip pull", image)
			continue
		}
		progressMap, err := preloadLayerSizes(ctx, imageDomain+"/"+image)
		if err != nil {
			return err
		}
		ipp := ImagePullProgress{
			Image:       image,
			ProgressMap: progressMap,
		}
		ddp.Images = append(ddp.Images, ipp)

	}

	for ii := range ddp.Images {
		err = dockerPullImage(ctx, imageDomain, ddp.Images[ii].Image, &ddp.Images[ii])
		if err != nil {
			return err
		}
	}
	return err
}

func parseDebControl(_ context.Context, debPath string) (*qnexus.QDCF, error) {
	qdcf := &qnexus.QDCF{}
	debControlInfo, err := aptlyDeb.GetControlFileFromDeb(debPath)
	if err != nil {
		return nil, err
	}
	qdcf.ConvertFrom(debControlInfo)
	return qdcf, nil
}

func dockerPull(ctx context.Context, imageName string) error {
	cli, err := client.NewClientWithOpts(client.FromEnv, client.WithAPIVersionNegotiation())
	if err != nil {
		return err
	}
	defer func(cli *client.Client) {
		_ = cli.Close()
	}(cli)

	reader, err := cli.ImagePull(ctx, imageName, dockerTypesImage.PullOptions{})
	if err != nil {
		return err
	}

	defer func(reader io.ReadCloser) {
		_ = reader.Close()
	}(reader)
	// cli.ImagePull is asynchronous.
	// The reader needs to be read completely for the pull operation to complete.
	// If stdout is not required, consider using io.Discard instead of os.Stdout.
	_, err = io.Copy(os.Stdout, reader)
	return err
}

func dockerPullImage(ctx context.Context, domain, image string, imagePullProgress *ImagePullProgress) error {
	cli, err := client.NewClientWithOpts(client.FromEnv, client.WithAPIVersionNegotiation())
	if err != nil {
		return err
	}
	defer func(cli *client.Client) {
		_ = cli.Close()
	}(cli)

	options := dockerTypesImage.PullOptions{}
	imageToPull := image
	if domain != "" {
		imageToPull = fmt.Sprintf("%s/%s", domain, image)
	}
	reader, err := cli.ImagePull(ctx, imageToPull, options)
	if err != nil {
		return err
	}

	defer func(reader io.ReadCloser) {
		_ = reader.Close()
	}(reader)
	// cli.ImagePull is asynchronous.
	// The reader needs to be read completely for the pull operation to complete.
	// If stdout is not required, consider using io.Discard instead of os.Stdout.
	if _, err = io.Copy(io.Discard, io.TeeReader(reader, imagePullProgress)); err != nil {
		return err
	}

	if domain != "" {
		// pull时带域名，pull完成tag成不带域名的
		err = cli.ImageTag(ctx, imageToPull, image)
	}

	return err
}

func preloadLayerSizes(_ context.Context, imageName string) (map[string]LayerProgress, error) {
	progressMap := make(map[string]LayerProgress, 0)
	ref, err := name.ParseReference(imageName)
	if err != nil {
		return nil, fmt.Errorf("failed to parse image reference: %s, %v", imageName, err)
	}

	img, err := remote.Image(ref, remote.WithAuthFromKeychain(authn.DefaultKeychain))
	if err != nil {
		return nil, fmt.Errorf("failed to fetch image: %s, %v", imageName, err)
	}

	manifest, err := img.Manifest()
	if err != nil {
		return nil, fmt.Errorf("failed to get manifest: %s, %v", imageName, err)
	}
	for _, layer := range manifest.Layers {
		// layer中获取到的是完整的 如 602d8ad51b8130f3fcd71cb936dea612ebc799666136abf2e5914585b3178a4a
		// 进度 如{"status":"Downloading","progressDetail":{"current":21725184,"total":27511769},"progress":"[=======================================\u003e           ]  21.73MB/27.51MB","id":"602d8ad51b81"}
		// 进度中的id是12位的，为了能对应上，这里从完整的digest取前12位
		progressMap[layer.Digest.Hex[:12]] = LayerProgress{
			Total:   layer.Size,
			Current: 0,
		}
	}

	return progressMap, nil
}

func getLocalImageList(ctx context.Context) ([]string, error) {
	cli, err := client.NewClientWithOpts(client.FromEnv, client.WithAPIVersionNegotiation())
	if err != nil {
		return nil, err
	}
	defer func(cli *client.Client) {
		_ = cli.Close()
	}(cli)
	images, err := cli.ImageList(ctx, dockerTypesImage.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("get local image list failed: %v", err)
	}
	imageList := make([]string, 0)
	for _, image := range images {
		for _, repoTag := range image.RepoTags {
			if strings.Contains(repoTag, "<none") {
				continue
			}
			parts := strings.SplitN(repoTag, ":", 2)
			if len(parts) != 2 {
				continue
			}
			imageList = append(imageList, repoTag)
		}
	}
	return imageList, nil
}

func checkLocalImageExists(ctx context.Context, image string) (bool, error) {
	imageLocalList, err := getLocalImageList(ctx)
	if err != nil {
		return false, err
	}
	if slices.Contains(imageLocalList, image) {
		return true, nil
	}
	return false, nil
}
