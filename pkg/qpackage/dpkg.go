package qpackage

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"os"
	"os/exec"
	"strings"
	"time"

	qlog "gitlab.qomolo.com/infrastructure/sw_architecture/middleware_abstraction/utility/qlog_golang"
	"gitlab.qomolo.com/qomolo_utils/qomolo_get/pkg/utils"
)

type DpkgUtil struct{}

func NewDpkgUtil() *DpkgUtil {
	_ = os.MkdirAll(aptArchivesDir, 0755)
	_ = os.MkdirAll(dpkgTmpDir, 0755)
	return &DpkgUtil{}
}

func (d *DpkgUtil) Remove(ctx context.Context, pkgList []string) error {
	return dpkgRemovePkgList(ctx, pkgList)
}

func (d *DpkgUtil) Install(ctx context.Context, pkgList []PkgInfo, isUnderlay bool, pkgPathDir string, fn func(PkgInstallDone)) error {
	err := checkDpkgPathAndFile(ctx)
	if err != nil {
		return err
	}
	return dpkgInstallPkgList(ctx, pkgList, pkgPathDir, isUnderlay, fn)
}

func (d *DpkgUtil) GetAndDiffVersion(ctx context.Context, name string) (version string, err error) {
	var overlayVer, underlayVer string
	overlayVer, err = getDpkgLocalInstallPkgVersion(ctx, name, false)
	if err != nil {
		return "", err
	}
	if overlayVer != "" {
		overlay, err := utils.HasOverlay()
		if err != nil {
			return "", err
		}
		if overlay {
			underlayVer, err = getDpkgLocalInstallPkgVersion(ctx, name, true)
			if err != nil {
				return "", err
			}
			if overlayVer != underlayVer {
				return "", fmt.Errorf("overlay version is not equal underlay version overlay:%s underlay:%s", overlayVer, underlayVer)
			}
			if underlayVer != "" {
				return underlayVer, nil
			}
		}
	}
	return overlayVer, nil
}

func (d *DpkgUtil) InstallWithUpdate(ctx context.Context, pkgList []PkgInfo, isUnderlay bool, pkgPathDir string, fn func(PkgInstallDone)) error {
	err := checkDpkgPathAndFile(ctx)
	if err != nil {
		return err
	}
	return dpkgInstallPkgList(ctx, pkgList, pkgPathDir, isUnderlay, fn)
}

func (d *DpkgUtil) DockerPull(ctx context.Context, domain, image string) error {
	exists, err := checkLocalImageExists(ctx, image)
	if err != nil {
		return err
	}
	if exists {
		qlog.Debugf("image: %s exists, skip pull", image)
		return nil
	}
	if strings.HasSuffix("/", domain) {
		domain = strings.TrimSuffix(domain, "/")
	}
	imageName := fmt.Sprintf("%s/%s", domain, image)
	progressMap, err := preloadLayerSizes(ctx, imageName)
	if err != nil {
		return err
	}
	ipp := &ImagePullProgress{
		Image:       image,
		ProgressMap: progressMap,
	}
	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()
	go func() {
		for range ticker.C {
			percent := float64(ipp.GetCurrent()) / float64(ipp.GetTotal()) * 100
			fmt.Printf("dpkg install pull image %s %.2f\n", image, percent)
			if percent == 100 {
				return
			}
			ticker.Reset(1 * time.Second)
		}
	}()
	err = dockerPullImage(ctx, "", image, ipp)
	time.Sleep(2 * time.Second)
	return err
}
func (d *DpkgUtil) GetInOverlay(ctx context.Context, name string) (version string, err error) {
	return getDpkgLocalInstallPkgVersion(ctx, name, false)
}
func (d *DpkgUtil) GetInUnderlay(ctx context.Context, name string) (version string, err error) {
	return getDpkgLocalInstallPkgVersion(ctx, name, true)
}
func (d *DpkgUtil) RemoveInOverlay(ctx context.Context, name, version string, isPurge bool) error {
	_, err := dpkgRemovePkg(ctx, name, version, isPurge, false)
	return err
}
func (d *DpkgUtil) RemoveInUnderlay(ctx context.Context, name, version string, isPurge bool) error {
	_, err := dpkgRemovePkg(ctx, name, version, isPurge, true)
	return err
}

func (d *DpkgUtil) Get(ctx context.Context, name string, isUnderlay bool) (version string, err error) {
	return getDpkgLocalInstallPkgVersion(ctx, name, isUnderlay)
}

func dpkgRemovePkgList(ctx context.Context, pkgList []string) error {
	pkgStr := strings.Join(pkgList, " ")
	seq := utils.GetCtxEnvPath(ctx)
	forceAll := utils.GetCtxForceAllFlag(ctx)
	dpkgRoot := GetDpkgRoot(seq)
	cmdStr := fmt.Sprintf("dpkg --instdir=%s --admindir=%s/var/lib/dpkg --root=%s --force-script-chrootless --remove ", dpkgRoot, dpkgRoot, dpkgRoot)
	if forceAll {
		cmdStr += " --force-all "
	}
	cmdStr += pkgStr
	if IsCmdDryRun() {
		fmt.Println(cmdStr)
		return nil
	}

	cmd := exec.Command("/bin/bash", "-c", cmdStr)

	var stdOut bytes.Buffer
	var stdErr bytes.Buffer

	stdWriter := io.MultiWriter(os.Stdout, &stdOut)
	stdErrWriter := io.MultiWriter(os.Stderr, &stdErr)

	if IsCmdOutputConsole() {
		cmd.Stdout = stdWriter
		cmd.Stderr = stdErrWriter
	} else {
		cmd.Stdout = &stdOut
		cmd.Stderr = &stdErr
	}
	if err := cmd.Start(); err != nil {
		qlog.Errorf("apt dpkg remove error command Start, err:%v, cmd:%s", err, cmdStr)
		return err
	}
	if err := cmd.Wait(); err != nil {
		qlog.Errorf("apt dpkg error command Wait, err:%v, cmd:%s, stderr:%s",
			err, cmdStr, stdErr.String())
		return fmt.Errorf("%w stderr:%s", err, stdErr.String())
	}

	return nil
}
