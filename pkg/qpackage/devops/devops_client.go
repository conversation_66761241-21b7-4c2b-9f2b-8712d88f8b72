package devops

import (
	"fmt"
	"time"

	"github.com/go-resty/resty/v2"
)

type Client struct {
	client *resty.Client
}

func NewDevopsApiClient(baseUrl string) *Client {
	client := resty.New()
	client.SetBaseURL(baseUrl)
	client.SetTimeout(time.Second * 10)
	client.SetHeader("server", "qomolo-get")
	client.SetHeader("x-token", "wxjuqev93vqkp82yyhvxq60je7e60s6s")
	return &Client{
		client: client,
	}
}

func (c *Client) ModuleVersionList(req ModuleVersionListReq) ([]ModuleVersion, error) {
	res := Response[ResponseDataList[ModuleVersion]]{}
	resp, err := c.client.R().SetBody(req).SetResult(&res).Post("/ci/module_versions")
	if err != nil {
		return nil, err
	}
	if resp.StatusCode() != 200 {
		return nil, fmt.Errorf("get module version list, url:%s error: %s", resp.Request.URL, resp.String())
	}
	if res.Data.Total == 0 {
		return nil, fmt.Errorf("get empty module version list, req: %v", req)
	}
	return res.Data.List, nil
}

func (c *Client) ModuleVersionInfo(id int64) (ModuleVersion, error) {
	res := Response[ModuleVersion]{}
	resp, err := c.client.R().SetBody(id).SetResult(&res).Get(fmt.Sprintf("/ci/module_version/%v", id))
	if err != nil {
		return ModuleVersion{}, err
	}
	if resp.StatusCode() != 200 {
		return ModuleVersion{}, fmt.Errorf("get module version info, url:%s error: %s", resp.Request.URL, resp.String())
	}
	return res.Data, nil
}

func (c *Client) ModuleList(req ModuleListReq) ([]Module, error) {
	res := Response[ResponseDataList[Module]]{}
	resp, err := c.client.R().SetBody(req).SetResult(&res).Post("/ci/modules")
	if err != nil {
		return nil, err
	}
	if resp.StatusCode() != 200 {
		return nil, fmt.Errorf("get module list, url:%s error: %s", resp.Request.URL, resp.String())
	}
	if res.Data.Total == 0 {
		return nil, fmt.Errorf("get empty module version list, req: %v", req)
	}
	return res.Data.List, nil
}
