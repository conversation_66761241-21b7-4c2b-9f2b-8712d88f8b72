package devops

type Response[T any] struct {
	Code     int         `json:"code"`
	Msg      string      `json:"msg"`
	Ts       string      `json:"ts"`
	Reason   string      `json:"reason"`
	Data     T           `json:"data"`
	Metadata interface{} `json:"metadata"`
}

type ResponseDataList[T any] struct {
	List  []T `json:"list"`
	Total int `json:"total"`
}

type T struct{}

type Module struct {
	Id      int    `json:"id"`
	Name    string `json:"name"`
	Status  int    `json:"status"`
	PkgName string `json:"pkg_name"`
}

type ModuleVersion struct {
	Id           int             `json:"id"`
	ModuleId     int             `json:"module_id"`
	GitlabId     int             `json:"gitlab_id"`
	Name         string          `json:"name"`
	Path         string          `json:"path"`
	PkgName      string          `json:"pkg_name"`
	Version      string          `json:"version"`
	PipelineId   int             `json:"pipeline_id"`
	CommitId     string          `json:"commit_id"`
	CommitAt     int64           `json:"commit_at"`
	Branch       string          `json:"branch"`
	TargetBranch string          `json:"target_branch"`
	IssueKey     string          `json:"issue_key"`
	Arch         ArchType        `json:"arch"`
	Dependence   string          `json:"dependence"`
	ReleaseNote  string          `json:"release_note"`
	Creator      string          `json:"creator"`
	Updater      string          `json:"updater"`
	CreateTime   int64           `json:"create_time"`
	UpdateTime   int64           `json:"update_time"`
	Labels       Labels          `json:"labels"`
	RepoName     string          `json:"repo_name"`
	ModuleType   ModuleType      `json:"module_type"`
	Metadata     string          `json:"metadata"`
	Modules      []ModuleVersion `json:"modules"` // 依赖模块
}

type ArchType string

const (
	ArchAll   ArchType = "all"
	ArchAmd64 ArchType = "amd64"
	ArchArm64 ArchType = "arm64"
)

type ModuleType string

const (
	ModuleDeb ModuleType = "deb"
	ModuleRaw ModuleType = "raw"
)

type Label struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

type Labels []Label

const (
	RepoAlpha = "alpha"
	RepoRaw   = "raw"
	RepoDev   = "dev"
)

type ModuleListReq struct {
	PageNum  int    `json:"page_num"`
	PageSize int    `json:"page_size"`
	Name     string `json:"name"`
	PkgName  string `json:"pkg_name"`
}

type ModuleVersionListReq struct {
	PageNum  int     `json:"page_num"`
	PageSize int     `json:"page_size"`
	ModuleId int     `json:"module_id"`
	Version  string  `json:"version"`
	Name     string  `json:"name"`
	PkgName  string  `json:"pkg_name"`
	Arch     string  `json:"arch"`
	CommitId string  `json:"commit_id"`
	IsDelete int     `json:"is_delete"`
	Status   int     `json:"status"`
	Branch   string  `json:"branch"`
	Labels   []Label `json:"labels"`
	RepoName string  `json:"repo_name"`
}
