//go:build !unit

package qpackage

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"testing"

	"gotest.tools/assert"
)

func Init() *QPackage {
	_ = os.Setenv("QLOG_CMD_STDOUT", "true")
	opt := Option{
		RepoURL:         "https://repo.qomolo.com",
		RepoRawPath:     "/repository/raw",
		RepoMode:        RepoModeWWLOnly,
		SourceType:      SourceFileTypeWWL,
		SkipCheckTarget: true,
	}
	q, err := New(opt)
	if err != nil {
		fmt.Printf("init qpackage err: %s", err.Error())
		return nil
	}
	return q
}

func TestGroupDownloadWithProgress(t *testing.T) {
	qp := Init()
	err := qp.DownloadGroupWithProgress(context.Background(), "remote-control", "0.1.8", "")
	t.Log("err", err)
	t.Log("percent", qp.Downloader.GetPercent())
}

func TestSchemeDownloadWithProgress(t *testing.T) {
	qp := Init()
	err := qp.DownloadSchemeWithProgress(context.Background(), "remote-control-vehicle", "0.1.8", "")
	t.Log("err", err)
	t.Log("percent", qp.Downloader.GetPercent())
}

func TestDebDownloadWithProgress(t *testing.T) {
	ddp := DebDownloadProgress{
		Total:    1936,
		Name:     "qomolo-compose-webrtc-proxy",
		Version:  "0.1.85-788901",
		Repo:     "alpha",
		Arch:     "amd64",
		SavePath: "/tmp",
	}
	qp := Init()
	err := qp.DownloadDebWithProgress(context.Background(), &ddp)
	t.Log("err", err)
	// t.Log("percent", ddp.GetPercent())
}

func TestDebDownloadWithProgressProject(t *testing.T) {
	ddp := DebDownloadProgress{
		Total:    3200,
		Name:     "qomolo-profile-project-cnshatest",
		Version:  "0.0.294-612576",
		Repo:     "alpha",
		Arch:     "amd64",
		SavePath: "/tmp",
	}
	qp := Init()
	err := qp.DownloadDebWithProgress(context.Background(), &ddp)
	t.Log("err", err)
	// t.Log("percent", ddp.GetPercent())
}

func TestGroupDownloadWithProgressWithoutGetTotal(t *testing.T) {
	jsonStr := `{"name":"remote-control","version":"0.1.8","schemes":[{"name":"remote-control-server","version":"0.1.2","packages":[{"current":0,"total":12717892,"name":"qomolo-webrtc-sfu","version":"0.1.82-609393","repo":"alpha","arch":"amd64","save_path":"/data/tmp/apt/archives/","ts":0}]},{"name":"remote-control-vehicle","version":"0.1.8","packages":[{"current":0,"total":1936,"name":"qomolo-compose-webrtc-proxy","version":"0.1.85-788901","repo":"alpha","arch":"amd64","save_path":"/data/tmp/apt/archives/","ts":0}]}],"groups":[{"name":"remote-control","version":"0.1.8-1","schemes":[{"name":"remote-control-server","version":"0.1.2-1","packages":[{"current":0,"total":12717892,"name":"qomolo-webrtc-sfu","version":"0.1.82-609393","repo":"alpha","arch":"amd64","save_path":"/data/tmp/apt/archives/","ts":0}]},{"name":"remote-control-vehicle","version":"0.1.8-1","packages":[{"current":0,"total":1936,"name":"qomolo-compose-webrtc-proxy","version":"0.1.85-788901","repo":"alpha","arch":"amd64","save_path":"/data/tmp/apt/archives/","ts":0}]}]}]}`
	var gdp *GroupDownloadProgress
	_ = json.Unmarshal([]byte(jsonStr), &gdp)
	qp := Init()
	_, err := qp.downloadGroup(context.Background(), gdp)
	t.Log("err", err)
	t.Log("percent", qp.Downloader.GetPercent())
}

func TestGetRepoDownloadURL(t *testing.T) {
	pkg := "qomolo-compose-webrtc-proxy"
	version := "0.1.85-788901"
	repo := "alpha"
	arch := "amd64"
	repoURL1 := "https://repo.qomolo.com"
	dURL1Expect := "https://repo.qomolo.com/repository/alpha/pool/q/qomolo-compose-webrtc-proxy/qomolo-compose-webrtc-proxy_0.1.85-788901_amd64.deb"
	repoURL2 := "http://192.168.33.210:8081"
	dURL2Expect := "http://192.168.33.210:8081/repository/repo/alpha/pool/main/q/qomolo-compose-webrtc-proxy/qomolo-compose-webrtc-proxy_0.1.85-788901_amd64.deb"
	_, url1 := GetRepoDownloadURL(pkg, version, repo, arch, repoURL1, RepoModeWWLOnly)
	assert.Equal(t, url1, dURL1Expect)
	_, url2 := GetRepoDownloadURL(pkg, version, repo, arch, repoURL2, RepoModeProjectOnly)
	assert.Equal(t, url2, dURL2Expect)
}
