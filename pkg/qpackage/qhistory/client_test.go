package qhistory

import (
	"context"
	"encoding/json"
	"testing"
)

func TestHistoryRecord(t *testing.T) {
	db, err := New[History[string]]("/tmp/history.db")
	t.Log(err)
	err = db.Add(context.Background(), History[string]{Bucket: "test", TaskID: "test-1"})
	t.Log(err)
	res, err := db.Get(context.Background(), "test", Query{
		BucketType: "test",
		TaskID:     "test-1",
		Order:      OrderDesc,
		PageSize:   1,
		PageNum:    1,
	})
	if err != nil {
		t.Log(err)
	}
	b, _ := json.Marshal(res)
	t.Log(string(b))
}
