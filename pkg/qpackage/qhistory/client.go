package qhistory

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strconv"
	"time"

	"go.etcd.io/bbolt"

	"gitlab.qomolo.com/infrastructure/sw_architecture/middleware_abstraction/utility/qlibrary/qutils"
	qlog "gitlab.qomolo.com/infrastructure/sw_architecture/middleware_abstraction/utility/qlog_golang"

	"gitlab.qomolo.com/qomolo_utils/qomolo_get/pkg/qpackage/qnexus"
	"gitlab.qomolo.com/qomolo_utils/qomolo_get/pkg/utils"
)

type Client[T any] struct {
	historyFile string
	MaxCount    int
}

func New[T any](historyFile string) (*Client[T], error) {
	if historyFile == "" {
		return nil, errors.New("history file path is empty")
	}

	// 创建目录
	dir := filepath.Dir(historyFile)
	_, err := os.Stat(dir)
	if err != nil {
		if os.IsNotExist(err) {
			err = os.MkdirAll(dir, 0755)
			if err != nil {
				qlog.Errorf("failed to create history directory %s: %v", dir, err)
				return nil, fmt.Errorf("failed to create history directory: %w", err)
			}
		}
	}

	qlog.Debugf("history client initialized with file: %s", historyFile)
	return &Client[T]{
		historyFile: historyFile,
		MaxCount:    1000, // nolint
	}, nil
}

// openDB 按需打开数据库连接，使用完后需要立即关闭
func (r *Client[T]) openDB() (*bbolt.DB, error) {
	// 配置 bbolt 选项，添加超时机制避免无限等待
	options := &bbolt.Options{
		Timeout:         5 * time.Second, // 5秒超时
		NoGrowSync:      false,
		ReadOnly:        false,
		MmapFlags:       0,
		InitialMmapSize: 0,
	}

	// 尝试打开数据库
	db, err := bbolt.Open(r.historyFile, 0600, options)
	if err != nil {
		qlog.Errorf("failed to open history database %s: %v", r.historyFile, err)
		return nil, fmt.Errorf("failed to open history database: %w", err)
	}

	return db, nil
}

type BucketType string

const (
	BucketTypeAll     BucketType = "all"
	BucketTypePackage BucketType = "package"
	BucketTypeScheme  BucketType = "scheme"
	BucketTypeGroup   BucketType = "group"
	BucketTypeMap     BucketType = "map"
)

type History[T any] struct {
	Bucket  BucketType       `json:"bucket"`
	Name    string           `json:"name"`
	Type    qnexus.GroupType `json:"type"`
	Version string           `json:"version"`
	TaskID  string           `json:"task_id"`
	StartAt qutils.Time      `json:"start_at"`
	EndAt   qutils.Time      `json:"end_at"`
	Detail  T                `json:"detail"`
	// Desc 兼容旧数据,类型为 any
	Desc any `json:"desc"`
}

func (h History[T]) GetBucket() BucketType {
	return h.Bucket
}

type Order string

const (
	OrderAsc  Order = "asc"
	OrderDesc Order = "desc"
)

type Query struct {
	PageSize   int
	PageNum    int
	TaskID     string
	Order      Order
	Name       string
	Version    string
	BucketType string
}

func (q *Query) Validate() {
	if q.PageSize <= 0 {
		q.PageSize = 10
	}
	if q.PageNum <= 0 {
		q.PageNum = 1
	}
	if q.Order != OrderAsc && q.Order != OrderDesc {
		q.Order = OrderDesc
	}
}

type HistoryInter interface {
	GetBucket() BucketType
}

func (r *Client[T]) Add(ctx context.Context, record HistoryInter) error {
	// 按需打开数据库连接
	db, err := r.openDB()
	if err != nil {
		return fmt.Errorf("failed to open database for add operation: %w", err)
	}
	defer func() {
		if closeErr := db.Close(); closeErr != nil {
			qlog.Errorf("failed to close database after add operation: %v", closeErr)
		}
	}()

	return db.Update(func(tx *bbolt.Tx) error {
		b, err := tx.CreateBucketIfNotExists([]byte(record.GetBucket()))
		if err != nil {
			return err
		}
		id, _ := b.NextSequence()
		res, err := r.getHistoryWithDB(ctx, db, "", "", "", "")
		if err != nil {
			return err
		}
		if len(res) > r.MaxCount {
			err := b.Delete([]byte(strconv.Itoa(int(id) - r.MaxCount)))
			if err != nil {
				qlog.Errorf("clean record %v fail", int(id)-r.MaxCount)
			}
		}
		data, err := json.Marshal(record)
		if err != nil {
			return err
		}
		return b.Put([]byte(strconv.Itoa(int(id))), data)
	})
}

type GetHistoryRes[T any] struct {
	List  HistoryList[T] `json:"list"`
	Total int            `json:"total"`
}

type HistoryList[T any] []History[T]

func (b HistoryList[T]) Len() int {
	return len(b)
}

func (b HistoryList[T]) Swap(i, j int) {
	b[i], b[j] = b[j], b[i]
}

func (b HistoryList[T]) Less(i, j int) bool {
	return b[i].StartAt.Time().After(b[j].StartAt.Time())
}

func (r *Client[T]) Get(ctx context.Context, bucketType BucketType, q Query) (*GetHistoryRes[T], error) {
	// 按需打开数据库连接
	db, err := r.openDB()
	if err != nil {
		return nil, fmt.Errorf("failed to open database for get operation: %w", err)
	}
	defer func() {
		if closeErr := db.Close(); closeErr != nil {
			qlog.Errorf("failed to close database after get operation: %v", closeErr)
		}
	}()

	var res GetHistoryRes[T]
	q.Validate()
	if bucketType == BucketTypeAll {
		res.List, err = r.getHistoryWithDB(ctx, db, "", q.Name, q.Version, q.TaskID)
		if err != nil {
			qlog.Errorf("get history failed err:%s", err)
			return nil, err
		}
	} else {
		res.List, err = r.getHistoryWithDB(ctx, db, bucketType, q.Name, q.Version, q.TaskID)
		if err != nil {
			qlog.Errorf("get history failed err:%s", err)
			return nil, err
		}
	}
	if q.Order == OrderAsc {
		sort.Sort(sort.Reverse(res.List))
	} else {
		sort.Sort(res.List)
	}
	return &GetHistoryRes[T]{
		List:  utils.ListPagigation(res.List, q.PageNum, q.PageSize),
		Total: len(res.List),
	}, nil
}

func (r *Client[T]) getHistoryWithDB(_ context.Context, db *bbolt.DB, bucketType BucketType, name, version, taskID string) (HistoryList[T], error) {
	var records HistoryList[T]
	var handler = func(_, v []byte) error {
		var history History[T]
		err := json.Unmarshal(v, &history)
		if err != nil {
			return err
		}
		if taskID != "" {
			if history.TaskID == taskID {
				records = append(records, history)
			}
		} else if name != "" && version != "" {
			if history.Name == name && history.Version == version {
				records = append(records, history)
			}
		} else if name != "" {
			if history.Name == name {
				records = append(records, history)
			}
		} else {
			records = append(records, history)
		}
		return nil
	}
	err := db.View(func(tx *bbolt.Tx) error {
		if bucketType != "" {
			b := tx.Bucket([]byte(bucketType))
			if b == nil {
				return nil
			}
			return b.ForEach(handler)
		}
		return tx.ForEach(func(_ []byte, b *bbolt.Bucket) error {
			if b == nil {
				return nil
			}
			return b.ForEach(handler)
		})
	})
	return records, err
}
