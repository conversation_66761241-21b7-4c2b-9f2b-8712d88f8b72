package qpackage

import (
	"bufio"
	"bytes"
	"context"
	"errors"
	"fmt"
	"io"
	"os"
	"os/exec"
	"slices"
	"strings"
	"time"

	"golang.org/x/sync/errgroup"

	qlog "gitlab.qomolo.com/infrastructure/sw_architecture/middleware_abstraction/utility/qlog_golang"
	"gitlab.qomolo.com/qomolo_utils/qomolo_get/pkg/qpackage/qnexus"
	"gitlab.qomolo.com/qomolo_utils/qomolo_get/pkg/utils"

	aptlyDeb "github.com/aptly-dev/aptly/deb"
	"github.com/go-resty/resty/v2"
	"github.com/nxadm/tail"
	"go.uber.org/zap"
)

type Return struct {
	Success bool   `json:"success"`
	Info    string `json:"info"`
}

var (
	errAptUpdate = errors.New("exit status 100")
)

type Installer interface {
	Update(ctx context.Context, sourceFilePath string, isUnderlay bool) error
	InstallWithUpdate(ctx context.Context, pkgList []PkgInfo, isUnderlay bool, sourceFilePath, pkgPathDir string, fn func(PkgInstallDone)) error
	Clean(ctx context.Context, force bool) error
	Install(ctx context.Context, pkgList []PkgInfo, isUnderlay bool, sourceFilePath, pkgPathDir string, fn func(PkgInstallDone)) error
	LocalInstall(ctx context.Context, pkgList []PkgInfo, isUnderlay bool, pkgPathDir string, fn func(PkgInstallDone)) error
	InstallSingle(ctx context.Context, info PkgInfo, sourceFilePath, pkgPathDir string) error
	Remove(ctx context.Context, pkgList []string) error
	RemoveExactAll(ctx context.Context, name, version string, isPurge bool) error
	RemoveInOverlay(ctx context.Context, name, version string, isPurge bool) error
	RemoveInUnderlay(ctx context.Context, name, version string, isPurge bool) error
	GetAndDiffVersion(ctx context.Context, name string) (version string, err error)
	Get(ctx context.Context, name string, isUnderlay bool) (version string, err error)
	GetInOverlay(ctx context.Context, name string) (version string, err error)
	GetInUnderlay(ctx context.Context, name string) (version string, err error)
	Query(ctx context.Context, name string, isUnderlay bool) ([]QueryPkgInfo, error)
}

type AptInstaller interface{}
type DpkgInstaller interface{}

type AptUtil struct{}

func NewAptUtil() *AptUtil {
	_ = os.MkdirAll(aptArchivesDir, 0755)
	_ = os.MkdirAll(dpkgTmpDir, 0755)
	return &AptUtil{}
}

func (a *AptUtil) Update(ctx context.Context, sourceFilePath string, isUnderlay bool) (err error) {
	return aptUpdate(ctx, sourceFilePath, isUnderlay)
}

func (a *AptUtil) GetPkgLatestVersion(ctx context.Context, pkgName, sourceFilePath string) (string, error) {
	output, err := aptCacheMadison(ctx, pkgName, sourceFilePath)
	if err != nil {
		return "", err
	}

	lines := strings.Split(string(output), "\n")
	if len(lines) < 1 { // 至少有一行输出
		return "", fmt.Errorf("no version found for package %s", pkgName)
	}

	// apt-cache madison 的输出格式通常是 "  pkgName | version | repository"
	// 直接从第一行开始解析
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}
		parts := strings.Split(line, "|")
		if len(parts) >= 2 {
			version := strings.TrimSpace(parts[1])
			return version, nil
		}
	}

	return "", fmt.Errorf("no version found for package %s", pkgName)
}

func (a *AptUtil) InstallWithUpdate(ctx context.Context, pkgList []PkgInfo, isUnderlay bool, sourceFilePath string, fn func(PkgInstallDone)) error {
	err := a.Update(ctx, sourceFilePath, isUnderlay)
	if err != nil {
		return err
	}
	return aptInstallPkgList(ctx, pkgList, isUnderlay, sourceFilePath, fn)
}

const (
	SIZE20G = 1024 * 1024 * 1024 * 20
	DAYS15  = 24 * time.Hour * 15
	DAYS10  = 24 * time.Hour * 10
	DAYS7   = 24 * time.Hour * 7
	DAYS5   = 24 * time.Hour * 5
)

func (a *AptUtil) Clean(_ context.Context, force bool) error {
	// 统计文件夹大小
	size, err := utils.GetFolderSize(aptArchivesDir)
	if err != nil {
		return fmt.Errorf("get folder size error: %s", err)
	}
	// 大于 20G 才清理
	// nolint
	if !force && size < 1024*1024*1024*20 {
		return nil
	}
	// 更新清理逻辑 按访问时间清理 15 天
	// TODO: 清理之后还是大于 20G,继续清理到 10G
	size, err = utils.GetFolderSize(aptArchivesDir)
	if err != nil {
		return fmt.Errorf("get folder size error: %s", err)
	}
	if size > SIZE20G {
		for _, dur := range []time.Duration{DAYS15, DAYS10, DAYS7} {
			qlog.Infof("apt cache dir size > 20G, clean files before %v", dur.Hours())
			err := utils.CleanFilesInDirBySuffix(aptArchivesDir, ".deb", dur)
			if err != nil {
				return err
			}
			size, err := utils.GetFolderSize(aptArchivesDir)
			if err != nil {
				return fmt.Errorf("get folder size error: %s", err)
			}
			if size < SIZE20G {
				break
			}
		}
	}
	return err
}

func (a *AptUtil) Install(ctx context.Context, pkgList []PkgInfo, isUnderlay bool, sourceFilePath string, fn func(PkgInstallDone)) error {
	return aptInstallPkgList(ctx, pkgList, isUnderlay, sourceFilePath, fn)
}

func (a *AptUtil) LocalInstall(ctx context.Context, pkgList []PkgInfo, isUnderlay bool, pkgPathDir string, fn func(PkgInstallDone)) error {
	return aptLocalInstallPkgList(ctx, pkgList, pkgPathDir, isUnderlay, fn)
}

// InstallSingle
// 先安装 overlay，再安装underlay
// version 为空时，安装最新版本
func (a *AptUtil) InstallSingle(ctx context.Context, info PkgInfo, sourceFilePath string) error {
	err := a.InstallWithUpdate(ctx, []PkgInfo{info}, false, sourceFilePath, nil)
	if err != nil {
		return err
	}
	ok, _ := utils.HasOverlay()
	if ok {
		err = a.InstallWithUpdate(ctx, []PkgInfo{info}, true, sourceFilePath, nil)
		if err != nil {
			return err
		}
	}
	return nil
}

func (a *AptUtil) Remove(ctx context.Context, pkgList []string) error {
	return removePkgList(ctx, pkgList)
}

func (a *AptUtil) RemoveExactAll(ctx context.Context, name, version string, isPurge bool) error {
	output, err := a.RemoveInOverlay(ctx, name, version, isPurge, false)
	if err != nil {
		qlog.Sugar().Errorf("remove in overlay error, name: %s, version: %s, isPurge: %t, output: %s", name, version, isPurge, string(output))
		return err
	}
	overlay, err := utils.HasOverlay()
	if err != nil {
		return err
	}
	if overlay {
		output, err = a.RemoveInUnderlay(ctx, name, version, isPurge, false)
		if err != nil {
			qlog.Sugar().Errorf("remove in underlay error, name: %s, version: %s, isPurge: %t, output: %s", name, version, isPurge, string(output))
			return err
		}
	}
	return nil
}

func (a *AptUtil) RemoveInOverlay(ctx context.Context, name, version string, isPurge, ignoreVersion bool) (output []byte, err error) {
	return aptRemovePkg(ctx, name, version, isPurge, ignoreVersion, false)
}

func (a *AptUtil) RemoveInUnderlay(ctx context.Context, name, version string, isPurge, ignoreVersion bool) (output []byte, err error) {
	return aptRemovePkg(ctx, name, version, isPurge, ignoreVersion, true)
}

func (a *AptUtil) GetAndDiffVersion(ctx context.Context, name string) (version string, err error) {
	var overlayVer, underlayVer string
	overlayVer, err = getLocalInstalledPkgVersion(ctx, name, false)
	if err != nil {
		return "", err
	}
	if overlayVer != "" {
		overlay, err := utils.HasOverlay()
		if err != nil {
			return "", err
		}
		if overlay {
			underlayVer, err = getLocalInstalledPkgVersion(ctx, name, true)
			if err != nil {
				return "", err
			}
			if overlayVer != underlayVer {
				return "", fmt.Errorf("overlay version is not equal underlay version overlay:%s underlay:%s", overlayVer, underlayVer)
			}
			if underlayVer != "" {
				return underlayVer, nil
			}
		}
	}
	return overlayVer, nil
}

func (a *AptUtil) Get(ctx context.Context, name string, isUnderlay bool) (version string, err error) {
	return getLocalInstalledPkgVersion(ctx, name, isUnderlay)
}

func (a *AptUtil) GetInOverlay(ctx context.Context, name string) (version string, err error) {
	return getLocalInstalledPkgVersion(ctx, name, false)
}

func (a *AptUtil) GetInUnderlay(ctx context.Context, name string) (version string, err error) {
	return getLocalInstalledPkgVersion(ctx, name, true)
}

func (a *AptUtil) Query(ctx context.Context, name string, isUnderlay bool) ([]QueryPkgInfo, error) {
	return aptQuery(ctx, name, isUnderlay)
}

func aptUpdate(ctx context.Context, sourceFilePath string, isUnderlay bool) (err error) {
	var cmdStr string
	if sourceFilePath == "" {
		cmdStr = "apt-get -y update"
	} else {
		cmdStr = fmt.Sprintf("DEBIAN_FRONTEND=noninteractive apt-get -y update -o Dir::Etc::sourcelist='%s' -o Dir::Etc::sourceparts='-' -o APT::Get::List-Cleanup='0'", sourceFilePath)
	}
	// nolint
	qlog.Debugf("apt update cmd:%s, isUnderlay:%t", cmdStr, isUnderlay)
	cmd := execCmd(ctx, cmdStr, isUnderlay)
	var buf bytes.Buffer
	writer := io.MultiWriter(os.Stdout, &buf)
	if IsCmdOutputConsole() {
		cmd.Stdout = writer
		cmd.Stderr = writer
	} else {
		cmd.Stdout = &buf
		cmd.Stderr = &buf
	}
	err = cmd.Run()
	output := buf.String()
	if err != nil {
		qlog.Errorf("apt update error:%v, cmd:%s, isUnderlay:%t, output:%s",
			err, cmdStr, isUnderlay, output)
		if strings.Contains(err.Error(), errAptUpdate.Error()) {
			return nil
		}
		return fmt.Errorf("update err:%w output:%s", err, output)
	}
	return nil
}

func removePkgList(_ context.Context, pkgList []string) error {
	pkgStr := strings.Join(pkgList, " ")
	cmdStr := "DEBIAN_FRONTEND=noninteractive apt-get -y remove " + pkgStr
	if IsCmdDryRun() {
		fmt.Println(cmdStr)
		return nil
	}
	cmd := exec.Command("/bin/bash", "-c", cmdStr)
	qlog.Debugf("apt remove cmd:%s", cmdStr)

	var stdOut bytes.Buffer
	var stdErr bytes.Buffer

	stdWriter := io.MultiWriter(os.Stdout, &stdOut)
	stdErrWriter := io.MultiWriter(os.Stderr, &stdErr)

	if IsCmdOutputConsole() {
		cmd.Stdout = stdWriter
		cmd.Stderr = stdErrWriter
	} else {
		cmd.Stdout = &stdOut
		cmd.Stderr = &stdErr
	}
	if err := cmd.Start(); err != nil {
		qlog.Errorf("apt remove error command Start, err:%v, cmd:%s", err, cmdStr)
		return err
	}
	if err := cmd.Wait(); err != nil {
		qlog.Errorf("apt error command Wait, err:%v, cmd:%s, stderr:%s", err, cmdStr, stdErr.String())
		return fmt.Errorf("%w stderr:%s", err, stdErr.String())
	}
	return nil
}

type PkgInfo struct {
	PkgName  string          `json:"pkg_name"`
	Latest   bool            `json:"latest"`    // version 为空时，是否安装最新版本
	RepoName string          `json:"repo_name"` //
	Arch     qnexus.ArchType `json:"arch"`
	IsDiff   bool            `json:"is_diff"`
	// HasDiff 只是为了兼容旧的管理平面gui,完全等于IsDiff
	HasDiff         bool                  `json:"has_diff"`
	LocalVersion    string                `json:"local_version"`
	RequiredVersion string                `json:"required_version"`
	Labels          qnexus.CiModuleLabels `json:"labels"`
}

func (p *PkgInfo) IsUpdated() bool {
	return p.LocalVersion != p.RequiredVersion
}

func (p *PkgInfo) AptInstall() bool {
	return !p.Labels.HasDpkgInstall()
}

func NewPkgInfoList(args []string) []PkgInfo {
	var pkgList []PkgInfo
	for _, v := range args {
		pkg := strings.Split(v, "=")
		info := PkgInfo{}
		if len(pkg) == 1 {
			info.PkgName = pkg[0]
			info.Latest = true
		} else if len(pkg) == 2 {
			info.PkgName = pkg[0]
			info.RequiredVersion = pkg[1]
			info.Latest = false
		} else {
			continue
		}
		pkgList = append(pkgList, info)
	}
	return pkgList
}

func aptInstallPkgList(ctx context.Context, pkgList []PkgInfo, isUnderlay bool, sourceFilePath string, fn func(PkgInstallDone)) error {
	pkgList = removeDuplicationPkg(pkgList)
	// 如果要安装到 underlay，必须先校验是否已经在 overlay 中已经安装每个模块对应的版本
	if isUnderlay {
		// 查找 overlay 下已经安装的版本
		installedMap, err := getLocalInstalledPkgList(ctx, false)
		if err != nil {
			return err
		}

		for _, v := range pkgList {
			installedVer := installedMap[v.PkgName]
			if installedVer.Version() != "" && installedVer.Version() != v.RequiredVersion {
				return fmt.Errorf("module %s overlay installed version %s is not equal %s", v.PkgName, installedVer, v.RequiredVersion)
			}
		}
	}
	ignoredPkgs := make([]string, 0)
	ignoreDepends := utils.GetCtxIgnoreDepends(ctx)
	if ignoreDepends != "" {
		ignoredPkgs = strings.Split(ignoreDepends, ",")
	}
	pkgStr := ""
	for _, pkg := range pkgList {
		err := utils.CleanParamDir(pkg.PkgName)
		if err != nil {
			return err
		}
		if pkg.RequiredVersion == "" {
			if pkg.Latest {
				pkgStr += pkg.PkgName + " "
			} else {
				return fmt.Errorf("module %s version is empty", pkg.PkgName)
			}
		} else if slices.Contains(ignoredPkgs, pkg.PkgName) {
			qlog.Debugf("ignore depends %s", pkg.PkgName)
		} else {
			pkgStr += pkg.PkgName + "=" + pkg.RequiredVersion + " "
		}
	}
	err := runDpkgConfigure(ctx, isUnderlay)
	if err != nil {
		return err
	}
	cmdStr := fmt.Sprintf("DEBIAN_FRONTEND=noninteractive apt-get -y --allow-downgrades -o Dpkg::Options::='--force-overwrite' -o Dir::Cache::Archives='%s' install ", aptArchivesDir)
	if sourceFilePath == "" {
		cmdStr += pkgStr
	} else {
		cmdStr += fmt.Sprintf("%s -o Dir::Etc::sourcelist='%s' -o Dir::Etc::sourceparts='-' -o APT::Get::List-Cleanup='0'", pkgStr, sourceFilePath)
	}

	if fn != nil {
		cmdStr += " -o APT::Status-Fd=1"
	}

	if IsCmdDryRun() {
		fmt.Println(cmdStr)
		return nil
	}

	// 指定语言为英文，避免 apt install 进度解析错误
	cmd := execCmd(ctx, cmdStr, isUnderlay, "LANGUAGE=en_US:en", "TMPDIR="+dpkgTmpDir)
	qlog.Debugf("apt install cmd:%s, isUnderlay:%t", cmdStr, isUnderlay)
	var stdOut bytes.Buffer
	var stdErr bytes.Buffer

	var stdWriter io.Writer
	var stdErrWriter io.Writer
	stdoutPipe, err := cmd.StdoutPipe()
	if err != nil {
		return fmt.Errorf("install err stdoutPipe:%s", err)
	}
	stderrPipe, err := cmd.StderrPipe()
	if err != nil {
		utils.CtxLogger(ctx).Error("apt install error", zap.Error(err))
	}
	if IsCmdOutputConsole() {
		stdWriter = io.MultiWriter(os.Stdout, &stdOut)
		stdErrWriter = io.MultiWriter(os.Stderr, &stdErr)
	} else {
		stdWriter = io.MultiWriter(&stdOut)
		stdErrWriter = io.MultiWriter(&stdErr)
	}
	if err := cmd.Start(); err != nil {
		qlog.Errorf("apt install error command Start, err:%v, cmd:%s", err, cmdStr)
		return err
	}
	go func() {
		pkgMap := make(map[string]PkgInfo)
		for _, v := range pkgList {
			pkgMap[v.PkgName] = v
		}
		scanner := bufio.NewScanner(stdoutPipe)
		for scanner.Scan() {
			t := scanner.Text()
			_, _ = stdWriter.Write([]byte(t + "\n"))
			if fn == nil {
				continue
			}
			pkgName := parsePkgInstallDone(ctx, t)
			if len(pkgName) > 0 {
				pkg, ok := pkgMap[pkgName]
				if ok {
					fn(PkgInstallDone{
						IsUnderlay: isUnderlay,
						Version:    pkg.RequiredVersion,
						PkgName:    pkgName,
					})
				}
			}
		}
	}()

	scannerErr := bufio.NewScanner(stderrPipe)
	scannerErr.Bytes()
	for scannerErr.Scan() {
		_, _ = stdErrWriter.Write(scannerErr.Bytes())
	}

	if err := cmd.Wait(); err != nil {
		qlog.Errorf("apt install error command Wait, err:%s, cmd:%s, stderr:%s", err, cmdStr, stdErr.String())
		return fmt.Errorf("%w stdout:%s stderr:%s", err, stdOut.String(), stdErr.String())
	}
	return nil
}

func runDpkgConfigure(ctx context.Context, isUnderlay bool) error {
	// 安装之前先执行 dpkg --configure -a
	configureCmd := "dpkg --configure -a 2>&1"
	configureCmdOutput, err := execCmd(ctx, configureCmd, isUnderlay).Output()
	qlog.Debugf("pkg configure cmd:%s, isUnderlay:%t", configureCmd, isUnderlay)
	if err != nil {
		qlog.Errorf("dpkg configure error:%v, cmd:%s, output:%s", err, configureCmd, string(configureCmdOutput))
		return fmt.Errorf("dpkg configure err:%w output:%s", err, string(configureCmdOutput))
	}
	return nil
}

func aptLocalInstallPkgList(ctx context.Context, pkgList []PkgInfo, path string, isUnderlay bool, fn func(PkgInstallDone)) error {
	pkgList = removeDuplicationPkg(pkgList)
	// 如果要安装到 underlay，必须先校验是否已经在 overlay 中已经安装每个模块对应的版本
	if isUnderlay {
		// 查找 overlay 下已经安装的版本
		installedMap, err := getLocalInstalledPkgList(ctx, false)
		if err != nil {
			return err
		}

		for _, v := range pkgList {
			installedVer := installedMap[v.PkgName]
			if installedVer.Version() != "" && installedVer.Version() != v.RequiredVersion {
				return fmt.Errorf("module %s overlay installed version %s is not equal %s", v.PkgName, installedVer, v.RequiredVersion)
			}
		}
	}
	pkgStr := ""
	for _, pkg := range pkgList {
		pkgFullName, err := utils.FindFileInDir(path, pkg.PkgName+"_"+pkg.RequiredVersion)
		if err != nil {
			return err
		}
		pkgStr += fmt.Sprintf(" %s/%s ", path, pkgFullName)
	}

	cmdStr := "apt -y install --allow-downgrades " + pkgStr + " -o dir::cache::archives='" + path + "'"

	if fn != nil {
		cmdStr += " -o APT::Status-Fd=1"
	}
	if IsCmdDryRun() {
		fmt.Println(cmdStr)
		return nil
	}
	// 指定语言为英文，避免 apt install 进度解析错误
	cmd := execCmd(ctx, cmdStr, isUnderlay, "LANGUAGE=en_US:en")
	qlog.Debugf("apt local install cmd:%s, isUnderlay:%t", cmdStr, isUnderlay)
	var stdOut bytes.Buffer
	var stdErr bytes.Buffer

	stdWriter := io.MultiWriter(os.Stdout, &stdOut)
	stdErrWriter := io.MultiWriter(os.Stderr, &stdErr)

	if IsCmdOutputConsole() {
		cmd.Stdout = stdWriter
		cmd.Stderr = stdErrWriter
	} else {
		cmd.Stdout = &stdOut
		cmd.Stderr = &stdErr
	}
	if err := cmd.Start(); err != nil {
		qlog.Errorf("apt install error command Start, err:%v, cmd:%s", err, cmdStr)
		return err
	}
	if fn != nil {
		go func() {
			pkgMap := make(map[string]PkgInfo)
			for _, v := range pkgList {
				pkgMap[v.PkgName] = v
			}
			scanner := bufio.NewScanner(&stdOut)
			for scanner.Scan() {
				pkgName := parsePkgInstallDone(ctx, scanner.Text())
				if len(pkgName) > 0 {
					pkg, ok := pkgMap[pkgName]
					if ok {
						fn(PkgInstallDone{
							IsUnderlay: isUnderlay,
							Version:    pkg.RequiredVersion,
							PkgName:    pkgName,
						})
					}
				}
			}
		}()
	}
	if err := cmd.Wait(); err != nil {
		qlog.Errorf("apt local install error command Wait, err:%v, cmd:%s, stderr:%s", err, cmdStr, stdErr.String())
		return fmt.Errorf("%w stderr:%s", err, stdErr.String())
	}
	return nil
}

func dpkgInstallPkgList(ctx context.Context, pkgList []PkgInfo, pkgPathDir string, isUnderlay bool, fn func(PkgInstallDone)) error {
	pkgList = removeDuplicationPkg(pkgList)
	// 如果要安装到 underlay，必须先校验是否已经在 overlay 中已经安装每个模块对应的版本
	if isUnderlay {
		// 查找 overlay 下已经安装的版本
		installedMap, err := getEnvLocalAllPkgListVersion(ctx)
		if err != nil {
			return err
		}

		for _, v := range pkgList {
			installedVer := installedMap[v.PkgName]
			if installedVer.Version() != "" && installedVer.Version() != v.RequiredVersion {
				return fmt.Errorf("module %s overlay installed version %s is not equal %s", v.PkgName, installedVer, v.RequiredVersion)
			}
		}
	}

	for _, pkg := range pkgList {
		if pkg.RequiredVersion == "" {
			return fmt.Errorf("module %s version is empty", pkg.PkgName)
		}
		deb, err := utils.FindFileInDir(pkgPathDir, pkg.PkgName)
		if err != nil {
			return err
		}
		err = parseDebControlPullImages(ctx, pkgPathDir+"/"+deb)
		if err != nil {
			return err
		}
	}

	// 创建指定的fd
	dpkgStatusFile, err := os.CreateTemp("/data/tmp", "dpkg-")
	if err != nil {
		return err
	}
	defer func() {
		_ = dpkgStatusFile.Close()
		_ = os.Remove(dpkgStatusFile.Name())
	}()
	dpkgRoot := GetDpkgRootCtx(ctx)
	cmdStr := fmt.Sprintf("dpkg --instdir=%s --admindir=%s/var/lib/dpkg --root=%s --force-script-chrootless", dpkgRoot, dpkgRoot, dpkgRoot)
	if fn != nil {
		// 进度解析
		cmdStr += fmt.Sprintf(" --log=%s ", dpkgStatusFile.Name())
	}
	ignoreDepends := utils.GetCtxIgnoreDepends(ctx)
	if ignoreDepends != "" {
		cmdStr += fmt.Sprintf(" --ignore-depends=%s ", ignoreDepends)
	}
	cmdStr += fmt.Sprintf(" -i %s/*.deb", pkgPathDir)
	if IsCmdDryRun() {
		fmt.Println(cmdStr)
		return nil
	}

	// 指定语言为英文，避免 apt install 进度解析错误
	cmd := execCmd(ctx, cmdStr, isUnderlay, "LANGUAGE=en_US:en")
	// nolint
	qlog.Debugf("apt dpkg install cmd:%s, isUnderlay:%t", cmdStr, isUnderlay)
	var stdOut bytes.Buffer
	var stdErr bytes.Buffer

	stdWriter := io.MultiWriter(os.Stdout, &stdOut)
	stdErrWriter := io.MultiWriter(os.Stderr, &stdErr)

	if IsCmdOutputConsole() {
		cmd.Stdout = stdWriter
		cmd.Stderr = stdErrWriter
	} else {
		cmd.Stdout = &stdOut
		cmd.Stderr = &stdErr
	}
	if err := cmd.Start(); err != nil {
		qlog.Errorf("apt dpkg install error command Start, err:%v, cmd:%s", err, cmdStr)
		return err
	}
	if fn != nil {
		go func() {
			pkgMap := make(map[string]PkgInfo)
			for _, v := range pkgList {
				pkgMap[v.PkgName] = v
			}
			scanner := bufio.NewScanner(&stdOut)
			for scanner.Scan() {
				pkgName := parsePkgInstallDone(ctx, scanner.Text())
				if len(pkgName) > 0 {
					pkg, ok := pkgMap[pkgName]
					if ok {
						fn(PkgInstallDone{
							IsUnderlay: isUnderlay,
							Version:    pkg.RequiredVersion,
							PkgName:    pkgName,
						})
					}
				}
			}
		}()

		if dpkgStatusFile.Fd() > 0 {
			go func() {
				t, err1 := tail.TailFile(dpkgStatusFile.Name(), tail.Config{Follow: true})
				if err1 != nil {
					qlog.Error("dpkg install tail file error", zap.Error(err1))
					return
				}
				pkgMap := make(map[string]PkgInfo)
				for _, v := range pkgList {
					pkgMap[v.PkgName] = v
				}
				for line := range t.Lines {
					pkgName := parsePkgInstallDone(ctx, line.Text)
					if len(pkgName) > 0 {
						pkg, ok := pkgMap[pkgName]
						if ok {
							fn(PkgInstallDone{
								IsUnderlay: isUnderlay,
								Version:    pkg.RequiredVersion,
								PkgName:    pkgName,
							})
						}
					}
				}
			}()

		}
	}
	if err := cmd.Wait(); err != nil {
		qlog.Errorf("apt dpkg install error command Wait, err:%v, cmd:%s, stderr:%s", err, cmdStr, stdErr.String())
		return fmt.Errorf("%w stderr:%s", err, stdErr.String())
	}
	return nil
}

func parseDebControlPullImages(ctx context.Context, debPath string) error {
	// 检查是否忽略拉取镜像
	if utils.GetCtxIgnorePullImages(ctx) {
		qlog.Debugf("忽略拉取镜像，跳过parseDebControlPullImages")
		return nil
	}

	qdcf := &qnexus.QDCF{}
	debControlInfo, err := aptlyDeb.GetControlFileFromDeb(debPath)
	if err != nil {
		return err
	}
	qdcf.ConvertFrom(debControlInfo)
	qlog.Debugf("image:%+v", qdcf.RequiredImages)
	qlog.Debugf("restart labels profile:%+v", qdcf.Labels.GetRequiredRestartProfileLabels())
	qlog.Debugf("restart labels pkg:%+v", qdcf.Labels.GetRequiredRestartProfilePkgs())
	eg, ctx := errgroup.WithContext(ctx)
	clusterIps, _ := utils.GetClusterIps()
	for _, image := range qdcf.RequiredImages {
		image := image
		if len(clusterIps) == 0 {
			eg.Go(func() error {
				exists, err := checkLocalImageExists(ctx, image)
				if err != nil {
					return err
				}
				if exists {
					qlog.Debugf("image: %s exists, skip pull", image)
					return nil
				}
				err = dockerPull(ctx, image)
				if err != nil {
					return fmt.Errorf("pull docker image %s on localhost err %s", image, err.Error())
				}
				return nil
			})
		} else {
			for _, ip := range clusterIps {
				ip := ip
				eg.Go(func() error {
					type RespBody struct {
						Code int    `json:"code"`
						Msg  string `json:"msg"`
					}
					var body RespBody
					resp, err := resty.New().R().SetBody(map[string]string{"image": image}).SetResult(&body).Post("http://" + ip + ":8765/docker/pull")
					if err != nil {
						return fmt.Errorf("post url:%s error: %s pkg: %s image: %s", resp.Request.URL, err.Error(), qdcf.Package, image)
					}
					if resp.StatusCode() != 200 {
						return fmt.Errorf("post url:%s error: %s pkg:%s image: %s", resp.Request.URL, resp.String(), qdcf.Package, image)
					}
					qlog.Infof("host %s %s", ip, body.Msg)
					return nil
				})
			}
		}
	}
	return eg.Wait()
}

func dpkgRemovePkg(ctx context.Context, name, version string, isPurge, isUnderlay bool) ([]byte, error) {
	var cmdStr string
	if name == "" {
		return nil, fmt.Errorf("name is empty")
	}
	if version == "" {
		return nil, fmt.Errorf("version is empty")
	}

	// 要删除 underlay的包，需要先将 overlay 下的包删除
	if isUnderlay {
		localVersion, err := getDpkgLocalInstallPkgVersion(ctx, name, false)
		if err != nil {
			return nil, err
		}
		if localVersion != "" {
			return nil, errors.New("overlay package is not removed")
		}
	}
	dpkgRoot := GetDpkgRootCtx(ctx)
	if isPurge {
		cmdStr = fmt.Sprintf("dpkg --instdir=%s --admindir=%s/var/lib/dpkg --root=%s --force-script-chrootless --purge %s", dpkgRoot, dpkgRoot, dpkgRoot, name)
	} else {
		cmdStr = fmt.Sprintf("dpkg --instdir=%s --admindir=%s/var/lib/dpkg --root=%s --force-script-chrootless --remove %s", dpkgRoot, dpkgRoot, dpkgRoot, name)
	}
	return execCmd(ctx, cmdStr, isUnderlay).Output()
}

func aptRemovePkg(ctx context.Context, name, version string, isPurge, ignoreVersion, isUnderlay bool) ([]byte, error) {
	var cmdStr string
	if name == "" {
		return nil, fmt.Errorf("name is empty")
	}
	if version == "" && !ignoreVersion {
		return nil, fmt.Errorf("version is empty")
	}
	nameVersion := fmt.Sprintf("%s=%s", name, version)
	if ignoreVersion {
		nameVersion = name
	}

	// 要删除 underlay的包，需要先将 overlay 下的包删除
	if isUnderlay {
		localVersion, err := getLocalInstalledPkgVersion(ctx, name, false)
		if err != nil {
			return nil, err
		}
		if localVersion != "" {
			return nil, errors.New("overlay package is not removed")
		}
	}

	if isPurge {
		cmdStr = fmt.Sprintf("DEBIAN_FRONTEND=noninteractive apt-get -y purge %s", nameVersion)
	} else {
		cmdStr = fmt.Sprintf("DEBIAN_FRONTEND=noninteractive apt-get -y remove %s", nameVersion)
	}
	return execCmd(ctx, cmdStr, isUnderlay).Output()
}

type PkgInstallDone struct {
	PkgName    string `json:"pkg_name"`
	IsUnderlay bool   `json:"is_underlay"`
	Version    string `json:"version"`
}

func removeDuplicationPkg(s []PkgInfo) (s1 []PkgInfo) {
	m := make(map[string]struct{})
	for _, v := range s {
		key := fmt.Sprintf("%s=%s", v.PkgName, v.RequiredVersion)
		if _, exist := m[key]; !exist {
			m[key] = struct{}{}
			s1 = append(s1, v)
		}
	}
	return s1
}

func getLocalInstalledPkgVersion(ctx context.Context, name string, isUnderlay bool) (string, error) {
	// dpkg -l ii 表示是已经安装的包
	out, err := execCmd(ctx, fmt.Sprintf("dpkg -l %s | grep %s | grep -E '^ii' | awk '{print $3}' | tr -d '\n'", name, name), isUnderlay).Output()
	if err != nil {
		return "", err
	}
	return string(out), nil
}

func getLocalInstalledPkgList(_ context.Context, isUnderlay bool) (res PackageInfo, err error) {
	control, err := GetDpkgControl("", isUnderlay)
	if err != nil {
		return res, err
	}
	return control.FilterInstalled(), nil
}

func getEnvLocalAllPkgListVersion(ctx context.Context) (res PackageInfo, err error) {
	dpkgRoot := GetDpkgRootCtx(ctx)
	_, err = os.Stat(dpkgRoot)
	if err != nil {
		// 不存在直接跳过
		if os.IsNotExist(err) {
			return res, nil
		}
		return nil, err
	}

	control, err := GetDpkgControl(dpkgRoot, false)
	if err != nil {
		return res, err
	}
	return control.FilterInstalled(), nil
}

func getDpkgLocalInstallPkgVersion(ctx context.Context, name string, isUnderlay bool) (string, error) {
	// pkg install with dpkg --instdir=/data/qomolo_service/.0 --admindir=/data/qomolo_service/.0/var/lib/dpkg --root=/data/qomolo_service/.0 --force-script-chrootless
	dpkgRoot := GetDpkgRootCtx(ctx)
	out, err := execCmd(ctx, fmt.Sprintf("dpkg --root=%s --list %s | grep %s | grep -E '^ii' | awk '{print $3}' | tr -d '\n'", dpkgRoot, name, name), isUnderlay).Output()
	if err != nil {
		return "", err
	}
	return string(out), nil
}

// 解析apt和 dpkg install 进度
// qomolo-qprofile is already the newest version (0.2.24-164025)
// pmstatus:python-apt:80.0000:Installed
// dpkg 的
// status: 2024-10-11 07:37:44 status installed qomolo-compose-webrtc-proxy:amd64 0.1.52-571243
func parsePkgInstallDone(_ context.Context, text string) string {
	text = strings.Trim(text, "\n")
	if strings.HasPrefix(text, "pmstatus:") {
		statusArr := strings.Split(text, ":")
		if len(statusArr) == 4 {
			pkgName := statusArr[1]
			desc := statusArr[3]
			if strings.Contains(desc, "Installed") {
				return pkgName
			}
		}
	} else if strings.Contains(text, "is already the newest version") {
		// 正则解析 'qomolo-qprofile is already the newest version (0.2.24-164025)'，返回 qomolo-qprofie
		arr := strings.Split(text, " ")
		if len(arr) > 0 {
			return arr[0]
		}
		return ""
	} else if strings.Contains(text, "status installed") {
		statusArr := strings.Split(text, " ")
		// nolint
		if len(statusArr) == 6 {
			pkgPart := strings.Split(statusArr[4], ":")
			if len(pkgPart) == 2 {
				return pkgPart[0]
			}
		}
	}
	return ""
}

func aptQuery(_ context.Context, pkg string, isUnderlay bool) ([]QueryPkgInfo, error) {
	control, err := GetDpkgControl("", isUnderlay)
	if err != nil {
		return nil, err
	}
	res := make([]QueryPkgInfo, 0)
	for s := range control {
		if strings.Contains(s, pkg) {
			res = append(res, QueryPkgInfo{
				PkgName:     s,
				Version:     control[s].Version(),
				IsInstalled: control[s].IsInstalled(),
			})
		}
	}
	return res, nil
}

func aptCacheMadison(ctx context.Context, pkg, sourceFilePath string) ([]byte, error) {
	cmdStr := fmt.Sprintf("DEBIAN_FRONTEND=noninteractive apt-cache madison %s", pkg)
	if sourceFilePath != "" {
		cmdStr += fmt.Sprintf("%s -o Dir::Etc::sourcelist='%s' -o Dir::Etc::sourceparts='-' -o APT::Get::List-Cleanup='0'", cmdStr, sourceFilePath)
	}
	return execCmd(ctx, cmdStr, false).Output()
}
