package qpackage

import (
	"context"
	"os"
	"path/filepath"
	"testing"

	"gitlab.qomolo.com/qomolo_utils/qomolo_get/pkg/utils"
	"go.uber.org/zap"
)

func TestQPackageStore_MultiEnv(t *testing.T) {
	// 创建临时目录用于测试
	tmpDir := "/tmp/qpackage_test"
	defer os.RemoveAll(tmpDir)

	// 确保目录存在
	err := os.MkdirAll(tmpDir, 0755)
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}

	// 创建logger
	logger, _ := zap.NewDevelopment()
	sugar := logger.Sugar()

	// 创建store
	store := &QPackageStore{
		log: sugar,
		cfg: &PackageInfoConf{
			Scheme: map[string]SchemeInfo{},
			Group:  map[string]GroupInfo{},
			Status: PackageStatus{},
		},
		filePath: filepath.Join(tmpDir, "package_manager.yaml"),
	}

	// 测试默认环境（非allToEnv）
	ctx := context.Background()
	err = store.SetLocalGroupInfo(ctx, "test-group", "1.0.0", &SchemeInstallFrom{
		Source:  "test-source",
		Version: "1.0.0",
	})
	if err != nil {
		t.Fatalf("Failed to set group info: %v", err)
	}

	groupInfo := store.GetLocalGroupInfo(ctx, "test-group")
	if groupInfo.Name != "test-group" || groupInfo.Version != "1.0.0" {
		t.Errorf("Expected group info (test-group, 1.0.0), got (%s, %s)", groupInfo.Name, groupInfo.Version)
	}

	// 测试基本的配置文件路径获取逻辑
	ctx0 := utils.SetCtxAllToEnvFlag(context.Background(), true)
	ctx0 = utils.SetCtxEnvPath(ctx0, "0")

	expectedPath := store.getConfigFilePathForTest(ctx0, tmpDir)
	expectedDir := filepath.Join(tmpDir, "env-0")
	expectedFile := filepath.Join(expectedDir, "package_manager.yaml")

	if expectedPath != expectedFile {
		t.Errorf("Expected path %s, got %s", expectedFile, expectedPath)
	}

	// 测试loadConfigFromPath和saveConfigToPath
	testCfg := &PackageInfoConf{
		Scheme: map[string]SchemeInfo{
			"test-scheme": {
				Name:    "test-scheme",
				Version: "1.0.0",
			},
		},
		Group: map[string]GroupInfo{
			"test-group": {
				Name:    "test-group",
				Version: "1.0.0",
			},
		},
		Status: PackageStatus{},
	}

	testPath := filepath.Join(tmpDir, "test-config.yaml")
	err = store.saveConfigToPath(testCfg, testPath)
	if err != nil {
		t.Fatalf("Failed to save config: %v", err)
	}

	loadedCfg, err := store.loadConfigFromPath(testPath)
	if err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}

	if loadedCfg.Scheme["test-scheme"].Name != "test-scheme" {
		t.Errorf("Expected scheme name test-scheme, got %s", loadedCfg.Scheme["test-scheme"].Name)
	}

	if loadedCfg.Group["test-group"].Name != "test-group" {
		t.Errorf("Expected group name test-group, got %s", loadedCfg.Group["test-group"].Name)
	}
}
