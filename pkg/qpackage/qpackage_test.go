//go:build !unit

package qpackage

import (
	"context"
	"encoding/json"
	"testing"

	"gitlab.qomolo.com/qomolo_utils/qomolo_get/pkg/qpackage/qnexus"
	"gitlab.qomolo.com/qomolo_utils/qomolo_get/pkg/utils"
)

func TestSchemeInstallDryRun(t *testing.T) {
	qp := Init()
	ctx := context.Background()
	ctx = utils.SetCtxEnvPath(ctx, "1")
	ctx = utils.SetCtxAllToEnvFlag(ctx, true)
	data := `{
		"name": "remote-control-server",
		"version": "0.1.6",
		"type": "alpha",
		"arch": "amd64",
		"release_note": "add ip",
		"modules": [
			{
				"name": "qomolo_data",
				"pkg_name": "qomolo-data",
				"version": "0.1.1-123456",
				"commit_id": "",
				"create_time": 1739358410,
				"labels": [],
				"module_type": "deb",
				"arch": "amd64",
				"repo_name": "alpha"
			},
			{
				"name": "webrtc_sfu",
				"pkg_name": "qomolo-compose-webrtc-sfu",
				"version": "0.1.129-924146",
				"commit_id": "bb0f5cc4f82147b92af98da62b6ba66c18bf6e53",
				"create_time": 1739358410,
				"labels": [
					{
						"key": "module/dpkg-install",
						"value": "true"
					}
				],
				"module_type": "deb",
				"arch": "amd64",
				"repo_name": "alpha"
			},
			{
				"name": "qomolo_service_config",
				"pkg_name": "qomolo-service-config-0",
				"version": "0.0.20-907308",
				"commit_id": "d7ad8eec642724498fcac9f3779b587435a6b421",
				"create_time": 1737343115,
				"labels": [
					{
						"key": "module/dpkg-install",
						"value": "true"
					},
					{
						"key": "module/env-path",
						"value": "0"
					}
				],
				"module_type": "deb",
				"arch": "amd64",
				"repo_name": "alpha"
			},
			{
				"name": "qomolo_service_config",
				"pkg_name": "qomolo-service-config-1",
				"version": "0.0.16-907308",
				"commit_id": "d7ad8eec642724498fcac9f3779b587435a6b421",
				"create_time": 1737343115,
				"labels": [
					{
						"key": "module/dpkg-install",
						"value": "true"
					},
					{
						"key": "module/env-path",
						"value": "1"
					}
				],
				"module_type": "deb",
				"arch": "amd64",
				"repo_name": "alpha"
			},
			{
				"name": "qomolo_service_config",
				"pkg_name": "qomolo-service-config-2",
				"version": "0.0.16-907308",
				"commit_id": "d7ad8eec642724498fcac9f3779b587435a6b421",
				"create_time": 1737343115,
				"labels": [
					{
						"key": "module/dpkg-install",
						"value": "true"
					},
					{
						"key": "module/env-path",
						"value": "2"
					}
				],
				"module_type": "deb",
				"arch": "amd64",
				"repo_name": "alpha"
			},
			{
				"name": "qomolo-profile-server-gw",
				"pkg_name": "qomolo-profile-server-gw",
				"version": "0.0.1-90000",
				"commit_id": "d7ad8eec642724498fcac9f3779b587435a6b421",
				"create_time": 1737343115,
				"labels": [
					{
						"key": "module/dpkg-install-with-system",
						"value": "true"
					}
				],
				"module_type": "deb",
				"arch": "amd64",
				"repo_name": "alpha"
			}
		],
		"targets": [
			{
				"name": "server_gw",
				"type": "server",
				"value": "^server_gw.*$"
			}
		],
		"create_time": 1739358562,
		"update_time": 1739358562,
		"labels": [],
		"status": 1
	}`
	info := &qnexus.NexusIntegration{}
	_ = json.Unmarshal([]byte(data), &info)
	t.Log("env-path", utils.GetCtxEnvPath(ctx))
	res, err := qp.schemeInstallDryRun(ctx, info, false)
	t.Log(err)
	t.Log("installList", res.AptPkgs(true))
	t.Log("dpkgInstallList", res.DpkgPkgs(false, true))
}

func TestGetSchemeVersionInfo(t *testing.T) {
	qp := Init()
	qp.opt.RepoURL = "https://repo-test.qomolo.com"
	ctx := context.Background()
	info, err := qp.GetSchemeVersionInfo(ctx, "welldrive", "3.6.300")
	t.Log(err)
	b, _ := json.Marshal(info)
	t.Log("SchemeVersionInfo:", string(b))
}
