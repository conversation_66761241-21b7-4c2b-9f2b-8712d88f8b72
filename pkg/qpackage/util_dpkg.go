package qpackage

import (
	"bytes"
	"context"
	"fmt"
	"os"
	"strings"

	"github.com/aptly-dev/aptly/deb"

	qlog "gitlab.qomolo.com/infrastructure/sw_architecture/middleware_abstraction/utility/qlog_golang"
	"gitlab.qomolo.com/qomolo_utils/qomolo_get/pkg/qpackage/qnexus"
)

// ControlInfo http://www.fifi.org/doc/libapt-pkg-doc/dpkg-tech.html/ch1.html
type ControlInfo map[string]string

const (
	fieldPackage = "Package"
	fieldLabels  = "Labels"
	fieldVersion = "Version"
	fieldStatus  = "Status"
)

func (c ControlInfo) PackageName() string {
	return c[fieldPackage]
}

func (c ControlInfo) Version() string {
	return c[fieldVersion]
}
func (c ControlInfo) Status() string {
	return c[fieldStatus]
}

func (c ControlInfo) IsInstalled() bool {
	// Status: Want Flag Status
	return c.Status() == "install ok installed"
}

// Labels: key1=value1,value2; key2=value2
func (c ControlInfo) Labels() qnexus.CiModuleLabels {
	s := c[fieldLabels]
	if s == "" {
		return nil
	}
	split := strings.Split(s, ";")
	labels := make(qnexus.CiModuleLabels, len(split))
	for _, v := range split {
		kv := strings.Split(v, "=")
		if len(kv) != 2 {
			continue
		}
		labels = append(labels, qnexus.Label{
			Key:   kv[0],
			Value: kv[1],
		})
	}
	return labels
}

type PackageInfo map[string]ControlInfo

func (p *PackageInfo) FilterRestartLabels() []ControlInfo {
	res := make([]ControlInfo, 0)
	for _, v := range *p {
		if len(v.Labels().GetRequiredRestartProfileLabels()) > 0 || len(v.Labels().GetRequiredRestartProfilePkgs()) > 0 {
			res = append(res, v)
		}
	}
	return res
}

func (p *PackageInfo) FilterInstalled() PackageInfo {
	res := make(PackageInfo)
	for _, v := range *p {
		if v.IsInstalled() {
			res[v.PackageName()] = v
		}
	}
	return res
}

// GetDpkgControl 解析 status，获取 control 文件信息，支持 qomolo_service
func GetDpkgControl(dpkgRoot string, isUnderlay bool) (res PackageInfo, err error) {
	path := dpkgRoot + "/var/lib/dpkg/status"
	var file []byte
	if isUnderlay {
		cmd := execCmd(context.Background(), fmt.Sprintf("cat %s", path), true)
		output, err1 := cmd.CombinedOutput()
		if err1 != nil {
			qlog.Warnf("dpkg get control error:%s output:%s", err1, output)
			return
		}
		file = output
	} else {
		// 文件不存在，不报错
		if _, err = os.Stat(path); os.IsNotExist(err) {
			return res, nil
		}
		file, err = os.ReadFile(path)
		if err != nil {
			return
		}
	}
	return parseDpkgControl(file), nil
}

func parseDpkgControl(control []byte) PackageInfo {
	p := make(PackageInfo)
	// 将 control 空行切割
	lines := strings.Split(string(control), "\n\n")
	for i := range lines {
		if len(lines[i]) == 0 {
			continue
		}
		newReader := bytes.NewReader([]byte(lines[i]))
		reader := deb.NewControlFileReader(newReader, false, false)
		stanza, err := reader.ReadStanza()
		if err != nil {
			continue
		}
		p[stanza[fieldPackage]] = ControlInfo(stanza)
	}
	return p
}
