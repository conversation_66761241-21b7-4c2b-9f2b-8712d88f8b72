package qpackage

import (
	"context"
	"encoding/json"
	"errors"
	"sync"
	"time"

	"github.com/google/uuid"

	"gitlab.qomolo.com/infrastructure/sw_architecture/middleware_abstraction/utility/qlibrary/qutils"
	qlog "gitlab.qomolo.com/infrastructure/sw_architecture/middleware_abstraction/utility/qlog_golang"
	"gitlab.qomolo.com/qomolo_utils/qomolo_get/pkg/qpackage/qhistory"
	"gitlab.qomolo.com/qomolo_utils/qomolo_get/pkg/qpackage/qnexus"
)

const (
	BucketTypeDownloadProgress qhistory.BucketType = "download_progress"
)

// nolint
var (
	PErrorTaskIDNotMatch     = errors.New("task id not match")
	PErrorIsDownloading      = errors.New("is downloading")
	PErrorIsLock             = errors.New("is lock")
	PErrorIsPreCheck         = errors.New("is pre_check")
	PErrorNotEnoughFreeSpace = errors.New("not enough free space")
)

type Progress interface {
	GetPercent() float64
	GetTotal() int64
	GetCurrent() int64
	GetImagePercent() map[string]float64
	GetImageTotal() map[string]int64
	GetImageCurrent() map[string]int64
	GetDebPercent() float64
	GetDebTotal() int64
	GetDebCurrent() int64
}

type Downloader struct {
	Progress
	cancelChan        chan bool
	archivesReadyChan chan bool
	doneChan          chan bool
	status            qhistory.History[DownloaderStatus]
	history           *qhistory.Client[DownloaderStatus]
	isPreCheck        bool
	isStart           bool // 是否开始下载
	lock              sync.Mutex
	mu                sync.RWMutex
}
type DownloadStatus string

const (
	PendingStatus  DownloadStatus = "pending"
	SuccessStatus  DownloadStatus = "success"
	FailedStatus   DownloadStatus = "failed"
	CanceledStatus DownloadStatus = "canceled"
)

type DownloaderStatus struct {
	Total   int64              `json:"total"`
	Current int64              `json:"current"`
	Status  DownloadStatus     `json:"status"`
	Percent float64            `json:"percent"`
	Msg     string             `json:"msg"`
	ErrCode string             `json:"err_code"`
	Images  map[string]float64 `json:"images"`
}

func NewDownloader(filepath string) (*Downloader, error) {
	history, err := qhistory.New[DownloaderStatus](filepath)
	if err != nil {
		return nil, err
	}
	return &Downloader{
		Progress:          EmptyProgress{},
		cancelChan:        make(chan bool, 1),
		archivesReadyChan: make(chan bool, 1),
		doneChan:          make(chan bool, 1),
		history:           history,
	}, nil
}
func (d *Downloader) PreCheck() error {
	// 使用TryLock避免死锁
	if !d.mu.TryLock() {
		return errors.New("another operation is in progress")
	}

	if d.isPreCheck {
		d.mu.Unlock() // 确保在返回前释放锁
		return PErrorIsPreCheck
	}

	d.isPreCheck = true
	d.mu.Unlock() // 设置完标志后立即释放锁
	return nil
}

func (d *Downloader) StopPreCheck() {
	// 如果能保证 PreCheck 之后一定会调用 StopPreCheck，则不需要加锁
	// 直接修改标志位即可
	d.isPreCheck = false
}

func (d *Downloader) start(typ qnexus.GroupType, name, version, taskID string) error {
	defer func() {
		if r := recover(); r != nil {
			qlog.Errorf("downloader recover err %s", r)
		}
	}()
	if !d.lock.TryLock() {
		qlog.Errorf("downloader is lock name:%s version:%s taskID:%s", name, version, taskID)
		return PErrorIsLock
	}
	defer d.lock.Unlock()
	if d.status.Detail.Status == PendingStatus {
		return PErrorIsDownloading
	}
	d.Progress = EmptyProgress{}
	d.status = qhistory.History[DownloaderStatus]{}
	d.status.Detail.Status = PendingStatus
	// 只是准备开始,还未获取总大小
	d.isStart = false
	if taskID == "" {
		d.status.TaskID = uuid.NewString()
	} else {
		d.status.TaskID = taskID
	}
	d.status.Detail.Total = 0
	d.status.Detail.Current = 0
	d.status.Detail.Percent = 0
	d.status.Detail.Images = make(map[string]float64, 0)
	d.status.Bucket = BucketTypeDownloadProgress
	d.status.Type = typ
	d.status.Name = name
	d.status.Version = version
	d.status.StartAt = qutils.Time(time.Now())
	go func() {
		// 定时获取进度
		for {
			select {
			case val, ok := <-d.doneChan:
				qlog.Infof("downloader doneChanval:%v ok:%+v", val, ok)
				return
			default:
				d.status.Detail.Current = d.GetCurrent()
				d.status.Detail.Total = d.GetTotal()
				d.status.Detail.Percent = d.GetPercent()
				d.status.Detail.Images = d.GetImagePercent()
				time.Sleep(time.Second)
			}
		}
	}()
	return nil
}

func (d *Downloader) finish(status DownloadStatus, errMsg string) {
	d.status.Detail.Status = status
	d.status.EndAt = qutils.Time(time.Now())
	d.status.Detail.Percent = d.GetPercent()
	d.status.Detail.Total = d.GetTotal()
	d.status.Detail.Current = d.GetCurrent()
	d.status.Detail.Images = d.GetImagePercent()
	err := d.history.Add(context.Background(), d.status)
	if err != nil {
		qlog.Errorf("save download progress err %s", err.Error())
	}
	d.status.Detail.Msg = errMsg
	d.doneChan <- true
}

func (d *Downloader) Cancel(taskID string) error {
	if d.status.TaskID != taskID {
		// 不是当前任务的,直接返回成功
		return nil
	}
	return d.Stop()
}

func (d *Downloader) Stop() error {
	if !d.lock.TryLock() {
		qlog.Error("downloader is lock")
		return PErrorIsLock
	}
	defer d.lock.Unlock()
	if d.status.Detail.Status != PendingStatus {
		qlog.Infof("task %s is not pending", d.status.TaskID)
		return nil
	}
	d.cancelChan <- true
	d.status.Detail.Status = CanceledStatus
	d.status.EndAt = qutils.Time(time.Now())
	d.status.Detail.Percent = d.GetPercent()
	d.status.Detail.Images = d.GetImagePercent()
	err := d.history.Add(context.Background(), qhistory.History[DownloaderStatus]{})
	if err != nil {
		qlog.Errorf("task stop save history failed err %s", err.Error())
	}
	return nil
}

func (d *Downloader) GetStatus() qhistory.History[DownloaderStatus] {
	return d.status
}

func (d *Downloader) IsStart() bool {
	return d.isStart
}

func (d *Downloader) GetTaskID() string {
	return d.status.TaskID
}

func (d *Downloader) GetTask(taskID string) qhistory.History[DownloaderStatus] {
	if taskID == d.status.TaskID {
		return d.status
	}

	res, err := d.history.Get(context.Background(), BucketTypeDownloadProgress, qhistory.Query{
		TaskID: taskID,
	})
	if err != nil {
		qlog.Errorf("get task failed err %s", err.Error())
		return qhistory.History[DownloaderStatus]{}
	}
	if len(res.List) == 0 {
		qlog.Info("get task empty")
		return qhistory.History[DownloaderStatus]{}
	}
	var ds qhistory.History[DownloaderStatus]
	b, err := json.Marshal(res.List[0])
	if err != nil {
		qlog.Errorf("get task marshal err %s", err.Error())
	}
	err = json.Unmarshal(b, &ds)
	if err != nil {
		qlog.Errorf("get task unmarshal err %s", err.Error())
	}
	return ds
}

func (d *Downloader) NotifyArchivesReadyByTaskID(taskID string, ready bool) error {
	if d.status.TaskID != taskID {
		// 不是当前任务的,直接返回成功
		return nil
	}
	return d.NotifyArchivesReady(ready)
}

func (d *Downloader) NotifyArchivesReady(ready bool) error {
	if !d.lock.TryLock() {
		qlog.Error("downloader is lock")
		return PErrorIsLock
	}
	defer d.lock.Unlock()
	if d.status.Detail.Status != PendingStatus {
		qlog.Infof("task %s is not pending", d.status.TaskID)
		return nil
	}
	d.archivesReadyChan <- ready
	return nil
}
