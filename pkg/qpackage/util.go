package qpackage

import (
	"context"
	"fmt"
	"os"
	"os/exec"

	qlog "gitlab.qomolo.com/infrastructure/sw_architecture/middleware_abstraction/utility/qlog_golang"
	"gitlab.qomolo.com/qomolo_utils/qomolo_get/pkg/utils"
)

func execCmd(ctx context.Context, cmdStr string, isUnderlay bool, env ...string) *exec.Cmd {
	if isUnderlay {
		cmdStr = fmt.Sprintf(`overlayroot-chroot-with-dev bash -c "%s"`, cmdStr)
	}
	qlog.Infof("exec cmd:%s", cmdStr)
	cmd := exec.CommandContext(ctx, "/bin/bash", "-c", cmdStr)
	cmd.Env = append(cmd.Environ(), env...)
	return cmd
}

func checkDpkgPathAndFile(ctx context.Context) error {
	// mkdir -p /data/qomolo_service/.0/var/lib/dpkg
	// touch    /data/qomolo_service/.0/var/lib/dpkg/status
	// mkdir -p /data/qomolo_service/.0/var/lib/dpkg/updates/
	// mkdir -p /data/qomolo_service/.0/var/lib/dpkg/info/
	// touch    /data/qomolo_service/.0/var/lib/dpkg/info/format-new ??
	seq := utils.GetCtxEnvPath(ctx)
	pathList := []string{
		fmt.Sprintf("/data/qomolo_service/.%s/var/lib/dpkg", seq),
		fmt.Sprintf("/data/qomolo_service/.%s/var/lib/dpkg/updates", seq),
		fmt.Sprintf("/data/qomolo_service/.%s/var/lib/dpkg/info", seq),
	}
	fileList := []string{
		fmt.Sprintf("/data/qomolo_service/.%s/var/lib/dpkg/status", seq),
		// fmt.Sprintf("/data/qomolo_service/.%s/var/lib/dpkg/info/format-new", seq),
	}
	var err error

	for _, path := range pathList {
		_, err1 := os.Stat(path)
		if os.IsNotExist(err1) {
			err = os.MkdirAll(path, 0755)
			if err != nil {
				return err
			}
		}
	}

	for _, file := range fileList {
		_, err2 := os.Stat(file)
		if os.IsNotExist(err2) {
			f, err := os.Create(file)
			if err != nil {
				return err
			}
			_ = f.Close()
		}
	}
	return nil
}

func GetDpkgRoot(seq string) string {
	return fmt.Sprintf("/data/qomolo_service/.%s", seq)
}

func GetDpkgRootCtx(ctx context.Context) string {
	seq := utils.GetCtxEnvPath(ctx)
	return GetDpkgRoot(seq)
}
