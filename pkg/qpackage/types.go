package qpackage

import (
	"context"
	"regexp"

	"github.com/samber/lo"

	qlog "gitlab.qomolo.com/infrastructure/sw_architecture/middleware_abstraction/utility/qlog_golang"
	"gitlab.qomolo.com/qomolo_utils/qomolo_get/pkg/qpackage/qnexus"
	"gitlab.qomolo.com/qomolo_utils/qomolo_get/pkg/utils"
)

const MPPackageName = "qomolo-mp-management-plane"

type InstallDryRunRes struct {
	Name            string           `json:"name"`
	LocalVersion    string           `json:"local_version"`
	RequiredVersion string           `json:"required_version"`
	Type            qnexus.GroupType `json:"type"`
	// HasDiff 针对下层所有的 group/scheme，子scheme或者模块是否存在差异。will_install && is_diff
	HasDiff bool `json:"has_diff"`
	// IsDiff 当前层的 group/scheme 是否存在差异
	IsDiff  bool               `json:"is_diff"`
	Msgs    []string           `json:"msg"`
	Schemes []InstallDryRunRes `json:"schemes"`
	Groups  []InstallDryRunRes `json:"groups"`
	// Modules 只包含需要安装的模块
	Modules      []PkgInfo `json:"modules"`
	AllowInstall bool      `json:"allow_install"`
}

func (g *InstallDryRunRes) ShouldReboot() bool {
	return groupShouldReboot(g)
}

func (g *InstallDryRunRes) ShouldGwReload() bool {
	return groupShouldGwReload(g)
}

// ShouldMPRestart 是否需要重启管理平面
func (g *InstallDryRunRes) ShouldMPRestart() bool {
	modules := getGroupAllModules(g)
	_, ok := lo.Find(modules, func(m PkgInfo) bool {
		if m.IsUpdated() && m.PkgName == MPPackageName {
			return true
		}
		return false
	})
	return ok
}

/*
GetRequiredRestartServices 获取需要重启的service列表
指定依赖的配置包，当配置包更新时，是否需要重启service。
支持按标签（主要是对于project等profile，有很多的包名），满足其中一个标签即可。
按包名匹配，支持正则表达式。
示例：
required-restart-profile-labels=__project,__vehicle_type
required-restart-profile-pkg-reg=^qomolo-profile-project-.*$
*/
func (g *InstallDryRunRes) GetRequiredRestartServices(ctx context.Context) (services []string) {
	modules := getGroupAllModules(g)
	// 更新的模块列表
	updatedModules := lo.Filter(modules, func(m PkgInfo, _ int) bool {
		return m.IsUpdated()
	})

	seq := utils.GetCtxEnvPath(ctx)

	// 当前 group 中重启的服务
	services = getRestartServices(modules, updatedModules)

	// 已经安装的deb包中的包含这两个 label 的包
	{
		// 先只考虑 qomolo-service 的包，系统包暂时没有需要重启的，不考虑，因为是重启 qomolo-service 服务。
		moduleFromControl := make([]PkgInfo, 0)
		dpkgRoot := GetDpkgRoot(seq)
		envRes, err := GetDpkgControl(dpkgRoot, false)
		if err != nil {
			qlog.Errorf("GetDpkgControl err:%v", err)
			return
		}
		restartModules := envRes.FilterRestartLabels()
		for _, item := range restartModules {
			moduleFromControl = append(moduleFromControl, PkgInfo{
				PkgName:         item.PackageName(),
				RequiredVersion: item.Version(),
				LocalVersion:    item.Version(),
				Latest:          false,
				RepoName:        "",
				Arch:            "",
				Labels:          item.Labels(),
			})
		}
		services = append(services, getRestartServices(moduleFromControl, updatedModules)...)
	}
	services = lo.Uniq(services)
	return
}

func (g *InstallDryRunRes) DpkgPkgs(isUnderlay, allToEnv bool) []PkgInfo {
	if allToEnv {
		return g.Modules
	}
	return lo.Filter(g.Modules, func(item PkgInfo, _ int) bool {
		return (item.Labels.HasDpkgInstall() || item.Labels.HasDpkgInstallWithSystem()) && !isUnderlay
	})
}

func (g *InstallDryRunRes) AptPkgs(allToEnv bool) []PkgInfo {
	if allToEnv {
		return nil
	}
	return lo.Filter(g.Modules, func(item PkgInfo, _ int) bool {
		return item.AptInstall()
	})
}

func getRestartServices(modules []PkgInfo, updatedModules []PkgInfo) (services []string) {
	labelModuleMap := make(map[string][]PkgInfo)
	pkgModuleMap := make(map[string][]PkgInfo)
	for _, m := range modules {
		pkgs := m.Labels.GetRequiredRestartProfilePkgs()
		labels := m.Labels.GetRequiredRestartProfileLabels()
		for _, label := range labels {
			labelModuleMap[label] = append(labelModuleMap[label], m)
		}
		for _, pkg := range pkgs {
			pkgModuleMap[pkg] = append(pkgModuleMap[pkg], m)
		}
	}
	for _, m := range updatedModules {
		for _, label := range m.Labels {
			if list, ok := labelModuleMap[label.Key]; ok {
				for _, item := range list {
					services = append(services, item.PkgName)
				}
			}
		}

		// 遍历pkgModuleMap，寻找与m.PkgName匹配的正则表达式对应的模块
		for reg, pkgM := range pkgModuleMap {
			// 编译正则表达式
			compile, err := regexp.Compile(reg)
			if err != nil {
				// 如果正则表达式编译失败，则跳过当前循环迭代
				qlog.Errorf("正则表达式编译失败 reg:%s err:%v", reg, err)
				continue
			}
			// 如果m.PkgName与当前正则表达式匹配
			if compile.MatchString(m.PkgName) {
				// 将所有匹配的模块的PkgName添加到services切片中
				for _, _pkgMItem := range pkgM {
					services = append(services, _pkgMItem.PkgName)
				}
			}
		}
	}
	services = lo.Uniq(services)
	return services
}

func groupShouldReboot(res *InstallDryRunRes) bool {
	if res == nil {
		return false
	}
	modules := getGroupAllModules(res)
	for _, m := range modules {
		reboot := m.Labels.IsRequiredReboot()
		if reboot {
			return true
		}
	}
	return false
}

func groupShouldGwReload(res *InstallDryRunRes) bool {
	if res == nil {
		return false
	}
	modules := getGroupAllModules(res)
	for _, m := range modules {
		reboot := m.Labels.IsRequiredGwReload()
		if reboot {
			return true
		}
	}
	return false
}

func getGroupAllModules(res *InstallDryRunRes) []PkgInfo {
	// 递归获取所有模块
	var modules []PkgInfo
	modules = append(modules, res.Modules...)
	for i := range res.Groups {
		modules = append(modules, getGroupAllModules(&res.Groups[i])...)
	}
	for i := range res.Schemes {
		modules = append(modules, getGroupAllModules(&res.Schemes[i])...)
	}
	return modules
}

type ModuleHistory struct {
	Version     string                        `json:"version"`
	Hash        string                        `json:"hash"`
	Time        string                        `json:"time"`
	Detail      qnexus.ModuleDownloadProgress `json:"detail"`
	Status      string                        `json:"status"`
	Size        int64                         `json:"size"`
	PrevVersion string                        `json:"prev_version"`
	AdsName     string                        `json:"ads_name"`
	AdsVersion  string                        `json:"ads_version"`
}
