package qpackage

import (
	"context"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"sync"
	"time"

	"go.uber.org/zap"
	"gopkg.in/yaml.v3"

	"gitlab.qomolo.com/qomolo_utils/qomolo_get/pkg/utils"
)

// getConfigFilePath 根据context获取正确的配置文件路径
func (q *QPackageStore) getConfigFilePath(ctx context.Context) string {
	allToEnv := utils.GetCtxAllToEnvFlag(ctx)
	envPath := utils.GetCtxEnvPath(ctx)

	if allToEnv {
		envDir := fmt.Sprintf("/data/qomolo_service/.%s/MP/agent/package_manager", envPath)
		return filepath.Join(envDir, packageManagerFileName)
	}

	return q.filePath // 默认路径
}

// getConfigFilePathForTest 测试用的方法，允许自定义基础路径
func (q *QPackageStore) getConfigFilePathForTest(ctx context.Context, baseDir string) string {
	allToEnv := utils.GetCtxAllToEnvFlag(ctx)
	envPath := utils.GetCtxEnvPath(ctx)

	if allToEnv {
		envDir := filepath.Join(baseDir, fmt.Sprintf("env-%s", envPath))
		return filepath.Join(envDir, packageManagerFileName)
	}

	return q.filePath // 默认路径
}

// loadConfigFromPath 从指定路径加载配置
func (q *QPackageStore) loadConfigFromPath(filePath string) (*PackageInfoConf, error) {
	// 确保目录存在
	dir := filepath.Dir(filePath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return nil, err
	}

	// 如果文件不存在，创建空文件
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		if _, err := os.Create(filePath); err != nil {
			return nil, err
		}
	}

	file, err := os.ReadFile(filePath)
	if err != nil {
		return nil, err
	}

	cfg := &PackageInfoConf{
		Scheme: map[string]SchemeInfo{},
		Group:  map[string]GroupInfo{},
		Status: PackageStatus{},
	}

	if len(file) > 0 {
		if err := yaml.Unmarshal(file, cfg); err != nil {
			return nil, err
		}
	}

	return cfg, nil
}

// saveConfigToPath 保存配置到指定路径
func (q *QPackageStore) saveConfigToPath(cfg *PackageInfoConf, filePath string) error {
	out, err := yaml.Marshal(cfg)
	if err != nil {
		return err
	}
	return os.WriteFile(filePath, out, 0755)
}

type QPackageStore struct {
	lock     sync.RWMutex
	cfg      *PackageInfoConf
	log      *zap.SugaredLogger
	filePath string
}

func NewQPackageStore(log *zap.SugaredLogger) (*QPackageStore, error) {
	if log == nil {
		return nil, errors.New("NewQPackage log is nil")
	}
	oldFilePath := filepath.Join(packageManagerPath, packageManagerFileName)
	newFilePath := filepath.Join(packageMPManagerPath, packageManagerFileName)
	pkg := &QPackageStore{
		log: log,
		cfg: &PackageInfoConf{
			Scheme: map[string]SchemeInfo{},
			Group:  map[string]GroupInfo{},
			Status: PackageStatus{},
		},
		filePath: newFilePath,
	}
	_, err := os.Stat(pkg.filePath)
	// 如果新的文件不存在,就复制旧的文件,如果旧的文件也不存在,就创建一个空的文件
	if err != nil {
		if !os.IsNotExist(err) {
			return nil, err
		}
		oldInfo, err1 := os.Stat(oldFilePath)
		if err1 != nil {
			if !os.IsNotExist(err1) {
				return nil, err1
			}
			// 忽略文件不存在的错误

		}
		if oldInfo != nil && len(oldInfo.Name()) > 0 {
			_, err = utils.CopyFile(oldFilePath, newFilePath)
			if err != nil {
				log.Error("copy file error: %v", err)
				return nil, err
			}
		} else {
			log.Info("package_manager.yaml not exist, create it")
			err = os.MkdirAll(packageMPManagerPath, 0755)
			if err != nil {
				log.Error("create package_manager path failed")
				return nil, err
			}
			_, err = os.Create(pkg.filePath)
			if err != nil {
				log.Error("create package_manager.yaml failed")
				return nil, err
			}
		}
	}
	err = pkg.ReloadConfig()
	if err != nil {
		return nil, err
	}
	// 初始化时，清除安装锁
	pkg.ClearStatus()
	return pkg, nil
}

func (q *QPackageStore) ReloadConfig() error {
	file, err := os.ReadFile(q.filePath)
	if err != nil {
		return err
	}
	q.lock.RLock()
	defer q.lock.RUnlock()
	return yaml.Unmarshal(file, q.cfg)
}

func (q *QPackageStore) GetPackageInfo() PackageInfoConf {
	return *q.cfg
}

func (q *QPackageStore) GetLocalGroupInfo(ctx context.Context, name string) GroupInfo {
	q.lock.RLock()
	defer q.lock.RUnlock()

	allToEnv := utils.GetCtxAllToEnvFlag(ctx)
	if allToEnv {
		// 从对应环境的配置文件读取
		filePath := q.getConfigFilePath(ctx)
		cfg, err := q.loadConfigFromPath(filePath)
		if err != nil {
			q.log.Errorf("failed to load config from %s: %v", filePath, err)
			return GroupInfo{}
		}
		return cfg.Group[name]
	}

	return q.cfg.Group[name]
}

func (q *QPackageStore) GetLocalSchemeInfo(ctx context.Context, name string) SchemeInfo {
	q.lock.RLock()
	defer q.lock.RUnlock()

	allToEnv := utils.GetCtxAllToEnvFlag(ctx)
	if allToEnv {
		// 从对应环境的配置文件读取
		filePath := q.getConfigFilePath(ctx)
		cfg, err := q.loadConfigFromPath(filePath)
		if err != nil {
			q.log.Errorf("failed to load config from %s: %v", filePath, err)
			return SchemeInfo{}
		}
		return cfg.Scheme[name]
	}

	return q.cfg.Scheme[name]
}

func (q *QPackageStore) SetLocalGroupInfo(ctx context.Context, name, version string, from *SchemeInstallFrom) error {
	q.lock.Lock()
	defer q.lock.Unlock()

	allToEnv := utils.GetCtxAllToEnvFlag(ctx)
	groupInfo := GroupInfo{
		Name:    name,
		Version: version,
		Time:    time.Now(),
		From:    from,
	}

	if allToEnv {
		// 操作对应环境的配置文件
		filePath := q.getConfigFilePath(ctx)
		cfg, err := q.loadConfigFromPath(filePath)
		if err != nil {
			return fmt.Errorf("failed to load config from %s: %v", filePath, err)
		}
		cfg.Group[name] = groupInfo
		return q.saveConfigToPath(cfg, filePath)
	} else {
		q.cfg.Group[name] = groupInfo
		return q.syncToFile()
	}
}
func (q *QPackageStore) RemoveLocalGroupInfo(ctx context.Context, name string) error {
	q.lock.Lock()
	defer q.lock.Unlock()

	allToEnv := utils.GetCtxAllToEnvFlag(ctx)
	if allToEnv {
		// 操作对应环境的配置文件
		filePath := q.getConfigFilePath(ctx)
		cfg, err := q.loadConfigFromPath(filePath)
		if err != nil {
			return fmt.Errorf("failed to load config from %s: %v", filePath, err)
		}
		delete(cfg.Group, name)
		return q.saveConfigToPath(cfg, filePath)
	} else {
		delete(q.cfg.Group, name)
		return q.syncToFile()
	}
}

func (q *QPackageStore) SetLocalSchemeInfo(ctx context.Context, name, version string, from *SchemeInstallFrom) error {
	q.lock.Lock()
	defer q.lock.Unlock()

	allToEnv := utils.GetCtxAllToEnvFlag(ctx)
	schemeInfo := SchemeInfo{
		Name:    name,
		Version: version,
		Time:    time.Now(),
		From:    from,
	}

	if allToEnv {
		// 操作对应环境的配置文件
		filePath := q.getConfigFilePath(ctx)
		cfg, err := q.loadConfigFromPath(filePath)
		if err != nil {
			return fmt.Errorf("failed to load config from %s: %v", filePath, err)
		}
		cfg.Scheme[name] = schemeInfo
		return q.saveConfigToPath(cfg, filePath)
	} else {
		q.cfg.Scheme[name] = schemeInfo
		return q.syncToFile()
	}
}

func (q *QPackageStore) RemoveLocalSchemeInfo(ctx context.Context, name string) error {
	q.lock.Lock()
	defer q.lock.Unlock()

	allToEnv := utils.GetCtxAllToEnvFlag(ctx)
	if allToEnv {
		// 操作对应环境的配置文件
		filePath := q.getConfigFilePath(ctx)
		cfg, err := q.loadConfigFromPath(filePath)
		if err != nil {
			return fmt.Errorf("failed to load config from %s: %v", filePath, err)
		}
		delete(cfg.Scheme, name)
		return q.saveConfigToPath(cfg, filePath)
	} else {
		delete(q.cfg.Scheme, name)
		return q.syncToFile()
	}
}

func (q *QPackageStore) CheckStatus() error {
	q.lock.RLock()
	if q.cfg.Status.Status == Installing {
		return fmt.Errorf("package %s is installing", q.cfg.Status.Name)
	}
	q.lock.RUnlock()
	return nil
}

func (q *QPackageStore) CheckTrySetStatus(status PackageStatus) error {
	q.lock.RLock()
	if q.cfg.Status.Status == Installing {
		// 超过10分钟，释放安装锁
		if time.Since(q.cfg.Status.Time) < time.Minute*10 {
			return fmt.Errorf("package %s is installing", q.cfg.Status.Name)
		}
	}
	q.cfg.Status = status
	_ = q.syncToFile()
	q.lock.RUnlock()
	return nil
}

func (q *QPackageStore) ClearStatus() {
	q.lock.Lock()
	q.cfg.Status = PackageStatus{}
	_ = q.syncToFile()
	q.lock.Unlock()
}

func (q *QPackageStore) GetAllName(typ string) []string {
	q.lock.RLock()
	defer q.lock.RUnlock()
	res := make([]string, 0)
	nameSet := make(map[string]bool) // 用于去重

	if typ == Scheme {
		for s := range q.cfg.Scheme {
			nameSet[s] = true
		}
		// 也需要从各个环境的配置文件中读取
		for _, envPath := range []string{"0", "1", "2"} {
			envFilePath := fmt.Sprintf("/data/qomolo_service/.%s/MP/agent/package_manager/%s", envPath, packageManagerFileName)
			if cfg, err := q.loadConfigFromPath(envFilePath); err == nil {
				for s := range cfg.Scheme {
					nameSet[s] = true
				}
			}
		}
	} else if typ == Group {
		for s := range q.cfg.Group {
			nameSet[s] = true
		}
		// 也需要从各个环境的配置文件中读取
		for _, envPath := range []string{"0", "1", "2"} {
			envFilePath := fmt.Sprintf("/data/qomolo_service/.%s/MP/agent/package_manager/%s", envPath, packageManagerFileName)
			if cfg, err := q.loadConfigFromPath(envFilePath); err == nil {
				for s := range cfg.Group {
					nameSet[s] = true
				}
			}
		}
	}

	// 转换为切片
	for name := range nameSet {
		res = append(res, name)
	}
	sort.Strings(res)
	return res
}

func (q *QPackageStore) syncToFile() error {
	out, err := yaml.Marshal(q.cfg)
	if err != nil {
		return err
	}
	err = os.WriteFile(q.filePath, out, 0755)
	return err
}
