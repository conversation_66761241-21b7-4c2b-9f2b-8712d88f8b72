package qpackage

import (
	"context"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"sync"
	"time"

	"go.uber.org/zap"
	"gopkg.in/yaml.v3"

	"gitlab.qomolo.com/qomolo_utils/qomolo_get/pkg/utils"
)

type QPackageStore struct {
	lock     sync.RWMutex
	cfg      *PackageInfoConf
	log      *zap.SugaredLogger
	filePath string
}

func NewQPackageStore(log *zap.SugaredLogger) (*QPackageStore, error) {
	if log == nil {
		return nil, errors.New("NewQPackage log is nil")
	}
	oldFilePath := filepath.Join(packageManagerPath, packageManagerFileName)
	newFilePath := filepath.Join(packageMPManagerPath, packageManagerFileName)
	pkg := &QPackageStore{
		log: log,
		cfg: &PackageInfoConf{
			Scheme: map[string]SchemeInfo{},
			Group:  map[string]GroupInfo{},
			Env0: &PackageInfoConf{
				Scheme: map[string]SchemeInfo{},
				Group:  map[string]GroupInfo{},
				Status: PackageStatus{},
			},
			Env1: &PackageInfoConf{
				Scheme: map[string]SchemeInfo{},
				Group:  map[string]GroupInfo{},
				Status: PackageStatus{},
			},
			Env2: &PackageInfoConf{
				Scheme: map[string]SchemeInfo{},
				Group:  map[string]GroupInfo{},
				Status: PackageStatus{},
			},
			Status: PackageStatus{},
		},
		filePath: newFilePath,
	}
	_, err := os.Stat(pkg.filePath)
	// 如果新的文件不存在,就复制旧的文件,如果旧的文件也不存在,就创建一个空的文件
	if err != nil {
		if !os.IsNotExist(err) {
			return nil, err
		}
		oldInfo, err1 := os.Stat(oldFilePath)
		if err1 != nil {
			if !os.IsNotExist(err1) {
				return nil, err1
			}
			// 忽略文件不存在的错误

		}
		if oldInfo != nil && len(oldInfo.Name()) > 0 {
			_, err = utils.CopyFile(oldFilePath, newFilePath)
			if err != nil {
				log.Error("copy file error: %v", err)
				return nil, err
			}
		} else {
			log.Info("package_manager.yaml not exist, create it")
			err = os.MkdirAll(packageMPManagerPath, 0755)
			if err != nil {
				log.Error("create package_manager path failed")
				return nil, err
			}
			_, err = os.Create(pkg.filePath)
			if err != nil {
				log.Error("create package_manager.yaml failed")
				return nil, err
			}
		}
	}
	err = pkg.ReloadConfig()
	if err != nil {
		return nil, err
	}
	// 初始化时，清除安装锁
	pkg.ClearStatus()
	return pkg, nil
}

func (q *QPackageStore) ReloadConfig() error {
	file, err := os.ReadFile(q.filePath)
	if err != nil {
		return err
	}
	q.lock.RLock()
	defer q.lock.RUnlock()
	return yaml.Unmarshal(file, q.cfg)
}

func (q *QPackageStore) GetPackageInfo() PackageInfoConf {
	return *q.cfg
}

func (q *QPackageStore) GetLocalGroupInfo(ctx context.Context, name string) GroupInfo {
	q.lock.RLock()
	defer q.lock.RUnlock()

	allToEnv := utils.GetCtxAllToEnvFlag(ctx)
	envPath := utils.GetCtxEnvPath(ctx)

	if allToEnv && envPath == "0" {
		return q.cfg.Env0.Group[name]
	}
	if allToEnv && envPath == "1" {
		return q.cfg.Env1.Group[name]
	}
	if allToEnv && envPath == "2" {
		return q.cfg.Env2.Group[name]
	}

	return q.cfg.Group[name]
}

func (q *QPackageStore) GetLocalSchemeInfo(ctx context.Context, name string) SchemeInfo {
	q.lock.RLock()
	defer q.lock.RUnlock()

	allToEnv := utils.GetCtxAllToEnvFlag(ctx)
	envPath := utils.GetCtxEnvPath(ctx)

	if allToEnv && envPath == "0" {
		return q.cfg.Env0.Scheme[name]
	}
	if allToEnv && envPath == "1" {
		return q.cfg.Env1.Scheme[name]
	}
	if allToEnv && envPath == "2" {
		return q.cfg.Env2.Scheme[name]
	}

	return q.cfg.Scheme[name]
}

func (q *QPackageStore) SetLocalGroupInfo(ctx context.Context, name, version string, from *SchemeInstallFrom) error {
	q.lock.Lock()
	allToEnv := utils.GetCtxAllToEnvFlag(ctx)
	envPath := utils.GetCtxEnvPath(ctx)
	groupInfo := GroupInfo{
		Name:    name,
		Version: version,
		Time:    time.Now(),
		From:    from,
	}
	if allToEnv && envPath == "0" {
		q.cfg.Env0.Group[name] = groupInfo
	} else if allToEnv && envPath == "1" {
		q.cfg.Env1.Group[name] = groupInfo
	} else if allToEnv && envPath == "2" {
		q.cfg.Env2.Group[name] = groupInfo
	} else {
		q.cfg.Group[name] = groupInfo
	}
	err := q.syncToFile()
	q.lock.Unlock()
	return err
}
func (q *QPackageStore) RemoveLocalGroupInfo(ctx context.Context, name string) error {
	q.lock.Lock()
	allToEnv := utils.GetCtxAllToEnvFlag(ctx)
	envPath := utils.GetCtxEnvPath(ctx)
	if allToEnv && envPath == "0" {
		q.cfg.Env0.Group[name] = GroupInfo{}
	} else if allToEnv && envPath == "1" {
		q.cfg.Env1.Group[name] = GroupInfo{}
	} else if allToEnv && envPath == "2" {
		q.cfg.Env2.Group[name] = GroupInfo{}
	} else {
		q.cfg.Group[name] = GroupInfo{}
	}
	err := q.syncToFile()
	q.lock.Unlock()
	return err
}

func (q *QPackageStore) SetLocalSchemeInfo(ctx context.Context, name, version string, from *SchemeInstallFrom) error {
	q.lock.Lock()
	allToEnv := utils.GetCtxAllToEnvFlag(ctx)
	envPath := utils.GetCtxEnvPath(ctx)
	schemeInfo := SchemeInfo{
		Name:    name,
		Version: version,
		Time:    time.Now(),
		From:    from,
	}
	if allToEnv && envPath == "0" {
		q.cfg.Env0.Scheme[name] = schemeInfo
	} else if allToEnv && envPath == "1" {
		q.cfg.Env1.Scheme[name] = schemeInfo
	} else if allToEnv && envPath == "2" {
		q.cfg.Env2.Scheme[name] = schemeInfo
	} else {
		q.cfg.Scheme[name] = schemeInfo
	}
	err := q.syncToFile()
	q.lock.Unlock()
	return err
}

func (q *QPackageStore) RemoveLocalSchemeInfo(ctx context.Context, name string) error {
	q.lock.Lock()
	allToEnv := utils.GetCtxAllToEnvFlag(ctx)
	envPath := utils.GetCtxEnvPath(ctx)
	if allToEnv && envPath == "0" {
		q.cfg.Env0.Scheme[name] = SchemeInfo{}
	} else if allToEnv && envPath == "1" {
		q.cfg.Env1.Scheme[name] = SchemeInfo{}
	} else if allToEnv && envPath == "2" {
		q.cfg.Env2.Scheme[name] = SchemeInfo{}
	} else {
		q.cfg.Scheme[name] = SchemeInfo{}
	}
	err := q.syncToFile()
	q.lock.Unlock()
	return err
}

func (q *QPackageStore) CheckStatus() error {
	q.lock.RLock()
	if q.cfg.Status.Status == Installing {
		return fmt.Errorf("package %s is installing", q.cfg.Status.Name)
	}
	q.lock.RUnlock()
	return nil
}

func (q *QPackageStore) CheckTrySetStatus(status PackageStatus) error {
	q.lock.RLock()
	if q.cfg.Status.Status == Installing {
		// 超过10分钟，释放安装锁
		if time.Since(q.cfg.Status.Time) < time.Minute*10 {
			return fmt.Errorf("package %s is installing", q.cfg.Status.Name)
		}
	}
	q.cfg.Status = status
	_ = q.syncToFile()
	q.lock.RUnlock()
	return nil
}

func (q *QPackageStore) ClearStatus() {
	q.lock.Lock()
	q.cfg.Status = PackageStatus{}
	_ = q.syncToFile()
	q.lock.Unlock()
}

func (q *QPackageStore) GetAllName(typ string) []string {
	q.lock.RLock()
	defer q.lock.RUnlock()
	res := make([]string, 0)
	if typ == Scheme {
		for s := range q.cfg.Scheme {
			res = append(res, s)
		}
	} else if typ == Group {
		for s := range q.cfg.Group {
			res = append(res, s)
		}
		for s := range q.cfg.Env0.Group {
			res = append(res, s)
		}
		for s := range q.cfg.Env1.Group {
			res = append(res, s)
		}
		for s := range q.cfg.Env2.Group {
			res = append(res, s)
		}
	}
	sort.Strings(res)
	return res
}

func (q *QPackageStore) syncToFile() error {
	out, err := yaml.Marshal(q.cfg)
	if err != nil {
		return err
	}
	err = os.WriteFile(q.filePath, out, 0755)
	return err
}
