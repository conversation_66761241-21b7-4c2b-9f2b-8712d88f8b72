package qpackage

import (
	"context"
	"strings"
	"testing"
)

func TestName(t *testing.T) {
	out := `
ii  accountsservice                       0.6.55-0ubuntu12~20.04.7              amd64        query and manipulate user account information
ii  acl                                   2.2.53-6                              amd64        access control list - utilities
ii  adduser                               3.118ubuntu2                          all          add and remove users and groups
rc  adwaita-icon-theme                    3.36.1-2ubuntu0.20.04.2               all          default icon theme of GNOME (small subset)
ii  alsa-topology-conf                    1.2.2-1                               all          ALSA topology configuration files
ii  alsa-ucm-conf                         1.2.2-1ubuntu0.13                     all          ALSA Use Case Manager configuration files
ii  apparmor                              2.13.3-7ubuntu5.3build2               amd64        user-space parser utility for AppArmor
ii  apport                                2.20.11-0ubuntu27.26                  all          automatically generate crash reports for debugging
ii  apport-symptoms                       0.23                                  all          symptom scripts for apport
ii  apt                                   2.0.9                                 amd64        commandline package manager
ii  apt-transport-https                   2.0.10                                all          transitional package for https support
ii  apt-utils                             2.0.9                                 amd64        package management related utility programs
ii  aspell                                0.60.8-1ubuntu0.1                     amd64        GNU Aspell spell-checker
ii  aspell-en                             2018.04.16-0-1                        all          English dictionary for GNU Aspell
ii  at                                    3.1.23-1ubuntu1                       amd64        Delayed job execution and batch processing
ii  at-spi2-core                          2.36.0-2                              amd64        Assistive Technology Service Provider Interface (dbus core)
ii  base-files                            11ubuntu5.7                           amd64        Debian base system miscellaneous files
ii  base-passwd                           3.5.47                                amd64        Debian base system master password and group files
ii  bash                                  5.0-6ubuntu1.2                        amd64        GNU Bourne Again SHell
ii  bash-completion                       1:2.10-1ubuntu1                       all          programmable completion for the bash shell
ii  bc                                    1.07.1-2build1                        amd64        GNU bc arbitrary precision calculator language
ii  bcache-tools                          1.0.8-3ubuntu0.1                      amd64        bcache userspace tools
ii  beep                                  1.4.3-2build1                         amd64        advanced PC-speaker beeper
ii  bind9-dnsutils                        1:9.18.28-0ubuntu0.20.04.1            amd64        Clients provided with BIND 9
ii  bind9-host                            1:9.18.28-0ubuntu0.20.04.1            amd64        DNS Lookup Utility
ii  bind9-libs:amd64                      1:9.18.28-0ubuntu0.20.04.1            amd64        Shared Libraries used by BIND 9`

	res := map[string]string{}
	// 按行遍历，提取安装的包名和对应的版本号
	for _, line := range strings.Split(out, "\n") {
		// 提取包名和版本号
		fields := strings.Fields(line)
		if len(fields) < 3 {
			continue
		}
		pkgName := strings.Fields(line)[1]
		version := strings.Fields(line)[2]
		res[pkgName] = version
	}
	t.Log(res)
}

func TestParsePkgInstallDone(t *testing.T) {
	done := parsePkgInstallDone(context.Background(), "2024-10-11 07:37:44 status installed qomolo-compose-webrtc-proxy:amd64 0.1.52-571243\n")
	t.Log(done)
}
