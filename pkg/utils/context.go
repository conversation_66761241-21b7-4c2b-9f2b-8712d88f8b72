package utils

import (
	"context"

	"go.uber.org/zap"

	qlog "gitlab.qomolo.com/infrastructure/sw_architecture/middleware_abstraction/utility/qlog_golang"
)

const (
	ctxLoggerKey string = "logger"
)

func CtxLogger(ctx context.Context) *zap.Logger {
	logger, ok := ctx.Value(ctxLoggerKey).(*zap.Logger)
	if ok && logger != nil {
		return logger
	}
	return qlog.Logger().WithOptions(zap.AddCallerSkip(-1))
}

type envPathKey struct{}

func SetCtxEnvPath(ctx context.Context, v string) context.Context {
	return context.WithValue(ctx, envPathKey{}, v)
}

func GetCtxEnvPath(ctx context.Context) string {
	if ctx != nil {
		if envPath, ok := ctx.Value(envPathKey{}).(string); ok {
			if envPath != "" {
				return envPath
			}
			return "0"
		}
	}
	return "0"
}

type aptUpdatedFlagKey struct{}

func SetCtxAptUpdatedFlag(ctx context.Context, v bool) context.Context {
	return context.WithValue(ctx, aptUpdatedFlagKey{}, v)
}

// GetCtxAptUpdatedFlag 获取是否需要 apt update，默认需要
func GetCtxAptUpdatedFlag(ctx context.Context) bool {
	if ctx != nil {
		if aptUpdatedFlag, ok := ctx.Value(aptUpdatedFlagKey{}).(bool); ok {
			return aptUpdatedFlag
		}
	}
	return true
}

type imageDomainKey struct{}

func SetCtxImageDomain(ctx context.Context, v string) context.Context {
	return context.WithValue(ctx, imageDomainKey{}, v)
}

func GetCtxImageDomain(ctx context.Context) string {
	if ctx != nil {
		if imageDomain, ok := ctx.Value(imageDomainKey{}).(string); ok {
			return imageDomain
		}
	}
	return ""
}

type allToEnvFlagKey struct{}

func SetCtxAllToEnvFlag(ctx context.Context, v bool) context.Context {
	return context.WithValue(ctx, allToEnvFlagKey{}, v)
}

// 获取是否需要全部安装到env-path，默认不需要
func GetCtxAllToEnvFlag(ctx context.Context) bool {
	if ctx != nil {
		if allToEnvFlag, ok := ctx.Value(allToEnvFlagKey{}).(bool); ok {
			return allToEnvFlag
		}
	}
	return false
}

type ignoreDependsKey struct{}

func SetCtxIgnoreDepends(ctx context.Context, v string) context.Context {
	return context.WithValue(ctx, ignoreDependsKey{}, v)
}

func GetCtxIgnoreDepends(ctx context.Context) string {
	if ctx != nil {
		if ignoreDepends, ok := ctx.Value(ignoreDependsKey{}).(string); ok {
			return ignoreDepends
		}
	}
	return ""
}

// 用于强制移除某些用ignore-depends安装的包，直接remove会报依赖问题
type forceAllFlag struct{}

func SetForceAllFlag(ctx context.Context, v bool) context.Context {
	return context.WithValue(ctx, forceAllFlag{}, v)
}

// GetCtxForceAllFlag 获取是否需要 apt force-all，默认不需要
func GetCtxForceAllFlag(ctx context.Context) bool {
	if ctx != nil {
		if forceAllFlag, ok := ctx.Value(forceAllFlag{}).(bool); ok {
			return forceAllFlag
		}
	}
	return false
}

type ignorePullImagesKey struct{}

func SetCtxIgnorePullImages(ctx context.Context, v bool) context.Context {
	return context.WithValue(ctx, ignorePullImagesKey{}, v)
}

// GetCtxIgnorePullImages 获取是否忽略拉取镜像，默认不忽略
func GetCtxIgnorePullImages(ctx context.Context) bool {
	if ctx != nil {
		if ignorePullImages, ok := ctx.Value(ignorePullImagesKey{}).(bool); ok {
			return ignorePullImages
		}
	}
	return false
}
