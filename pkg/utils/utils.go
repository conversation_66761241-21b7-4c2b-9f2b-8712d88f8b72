package utils

import (
	"bufio"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"os"
	"os/exec"
	"os/user"
	"path/filepath"
	"reflect"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/spf13/viper"

	"gitlab.qomolo.com/infrastructure/sw_architecture/middleware_abstraction/utility/qlibrary/qutils"
	qlog "gitlab.qomolo.com/infrastructure/sw_architecture/middleware_abstraction/utility/qlog_golang"
)

// CheckFileExist check file exist,return false if file not exist or other error, true if file exist
func CheckFileExist(file string) bool {
	_, err := os.Stat(file)
	if err != nil {
		if os.IsNotExist(err) {
			return false
		}
		// 其他问题都返回false阻止后续逻辑当正常处理
		return false
	}
	return true
}

// wc -l
func GetFileLineCount(file string) int {
	if !CheckFileExist(file) {
		return 0
	}

	fh, err := os.Open(file)
	if err != nil {
		return 0
	}
	defer func() {
		_ = fh.Close()
	}()

	reader := bufio.NewReaderSize(fh, 1024*1024)
	var count int
	for {
		_, _, err := reader.ReadLine()
		if err == io.EOF {
			break
		} else if err != nil {
			return 0
		}
		count++
	}
	return count
}

// GetEnv get env value, return default value if env not exist
func GetEnv(key, defaultValue string) string {
	osEnv := os.Getenv(key)
	if osEnv == "" {
		return defaultValue
	}
	return osEnv
}

// HasOverlay check if the system is using overlayfs
func HasOverlay() (bool, error) {
	cmd := exec.Command("/bin/bash", "-c", `mount | grep -E "overlayroot on / type overlay|overlayroot on /tmp type overlay" | wc -l | tr -d '\n'`)
	output, err := cmd.CombinedOutput()
	if err != nil {
		qlog.Errorf("check overlay error:%v, output:%s", err, string(output))
		return false, err
	}
	return string(output) != "0", nil
}

// IsInUnderlay check if the system is using overlayfs and in chroot
func IsInUnderlay() (bool, error) {
	overlay, err := HasOverlay()
	if err != nil {
		return false, err
	}
	chroot, err := IsInChroot()
	if err != nil {
		return false, err
	}
	return overlay && chroot, nil
}

// IsInChroot check if the system is in chroot
func IsInChroot() (bool, error) {
	cmd1Str := `
		if [ "$(stat -c %d:%i /)" != "$(stat -c %d:%i /proc/1/root/.)" ]; then
			echo -n "true"
		fi
`
	cmd1 := exec.Command("/bin/bash", "-c", cmd1Str)
	output, err := cmd1.CombinedOutput()
	if err != nil {
		return false, err
	}
	return string(output) == "true", nil
}

func MustCreateFile(filename string) (*os.File, error) {
	if err := MakeFileDir(filename); err != nil {
		return nil, fmt.Errorf("make dir err:%v", err)
	}
	return os.Create(filename)
}

func MakeFileDir(filename string) error {
	dir := filepath.Dir(filename)
	if CheckFileExist(dir) {
		return nil
	}
	return os.MkdirAll(dir, 0755)
}

func ReadFileWithCreate(filename string) ([]byte, error) {
	if CheckFileExist(filename) {
		return os.ReadFile(filename)
	}
	file, err := MustCreateFile(filename)
	if err != nil {
		return nil, err
	}
	defer func() {
		_ = file.Close()
	}()
	return nil, nil
}

func GetOptsPageSize(opts map[string]string, defaultVal ...int) int {
	if v, ok := opts["page_size"]; ok {
		if n, err := strconv.Atoi(v); err == nil {
			return n
		}
	}
	if len(defaultVal) > 0 {
		return defaultVal[0]
	}
	return 10
}

func GetOptsPageNum(opts map[string]string, defaultVal ...int) int {
	if v, ok := opts["page_num"]; ok {
		if n, err := strconv.Atoi(v); err == nil {
			return n
		}
	}
	if len(defaultVal) > 0 {
		return defaultVal[0]
	}
	return 1
}

func ListPagigation[T any](collection []T, pageNum, pageSize int) []T {
	res := make([]T, 0)
	if len(collection) == 0 {
		return res
	}
	start := (pageNum - 1) * pageSize
	end := pageNum * pageSize
	if end > len(collection) {
		end = len(collection)
	}
	if len(collection) < start {
		res = nil
	} else if len(collection) < end {
		res = collection[start:]
	} else {
		res = collection[start:end]
	}
	return res
}

func GetArch() (string, error) {
	arch, err := exec.Command("/bin/bash", "-c", "dpkg --print-architecture").Output()
	if err != nil {
		return "", fmt.Errorf("get arch error:%v", err)
	}
	archStr := strings.TrimSuffix(string(arch), "\n")
	return archStr, nil
}

func GetLinuxReleaseCodeName() (string, error) {
	codeName, err := exec.Command("/bin/bash", "-c", "cat /etc/lsb-release  | grep CODENAME | awk -F'=' '{print $2}' | tr -d '\n'").Output()
	if err != nil {
		return "", err
	}
	return string(codeName), nil
}

type HostType string

const (
	VehicleGW HostType = "vehicle_gw"
	ServerGW  HostType = "server_gw"
	DCU       HostType = "dcu"
)

const LabelModelFilePath = "/etc/miivii_release"

type DCUData = string

const (
	XAVIERIIPlus DCUData = "MIIVII APEX XAVIER II PLUS"
	XAVIERII     DCUData = "MIIVII APEX XAVIER II"
	XAVIERAD10   DCUData = "MIIVII APEX AD10"
)

type ModelDCU string

const (
	Xavieriiplus ModelDCU = "xav-iiplus"
	Xavierii     ModelDCU = "xav-ii"
	Xavierad10   ModelDCU = "orin-ad10"
)

type DCUInfo struct {
	Type    DCUType    `json:"type"`
	Model   ModelDCU   `json:"model"`
	Version DCUVersion `json:"version"`
}

type DCUType string
type DCUVersion string

const (
	Orin   DCUType = "orin"
	Xavier DCUType = "xavier"
)
const (
	JP45 DCUVersion = "4.5"
	JP51 DCUVersion = "5.1"
)

func (d ModelDCU) GetType() DCUType {
	switch d {
	case Xavieriiplus, Xavierii:
		return Xavier
	case Xavierad10:
		return Orin
	default:
		return ""
	}
}

func GetDCUInfo() (info DCUInfo) {
	data, err := os.ReadFile(LabelModelFilePath)
	if err != nil {
		return info
	}
	s := string(data)
	return getDCUInfo(s)
}

func getDCUInfo(s string) (info DCUInfo) {
	s = strings.ToUpper(s)
	if strings.HasPrefix(s, XAVIERIIPlus) {
		info.Model = Xavieriiplus
		info.Type = Xavier
	} else if strings.HasPrefix(s, XAVIERII) {
		info.Model = Xavierii
		info.Type = Xavier
	} else if strings.HasPrefix(s, XAVIERAD10) {
		info.Model = Xavierad10
		info.Type = Orin
	}
	reg := regexp.MustCompile(`\d\.\d`)
	matches := reg.FindStringSubmatch(s)
	if len(matches) > 0 {
		info.Version = DCUVersion(matches[0])
	}
	return info
}

func GetModelDCU() ModelDCU {
	return GetDCUInfo().Model
}

const ProjectProfile = "/etc/qomolo/profile/project/profile.yaml"
const VehTypeProfile = "/etc/qomolo/profile/vehicle_type/profile.yaml"
const GlobalProfile = "/etc/qomolo/profile/global/profile.yaml"
const ChassisProfile = "/etc/qomolo/profile/chassis/profile.yaml"
const HaConf = "/etc/qomolo/utils/qomolo_gateway/config/50-ha"

func GetProjectName() (string, error) {
	v := viper.New()
	v.SetConfigFile(ProjectProfile)
	err := v.ReadInConfig()
	if err != nil {
		return "", fmt.Errorf("error reading project profile, %v", err)
	}
	return v.GetString("profile_name"), err
}

type VehicleType string

func (v VehicleType) Category() VehicleCategory {
	return GetVehicleCategory(string(v))
}
func GetVehicleType() (VehicleType, error) {
	v := viper.New()
	v.SetConfigFile(VehTypeProfile)
	err := v.ReadInConfig()
	if err != nil {
		return "", fmt.Errorf("error reading vehicle type profile, %v", err)
	}
	return VehicleType(v.GetString("profile_name")), err
}

func GetHostType() (HostType, error) {
	v := viper.New()
	v.SetConfigFile(GlobalProfile)
	err := v.ReadInConfig()
	if err != nil {
		return "", fmt.Errorf("error reading global profile, %v", err)
	}
	return HostType(v.GetString("host_type")), err
}

func GetChassis() (string, error) {
	v := viper.New()
	v.SetConfigFile(ChassisProfile)
	err := v.ReadInConfig()
	if err != nil {
		return "", fmt.Errorf("error reading global profile, %v", err)
	}
	return v.GetString("profile_name"), err
}

func GetHostID() (int, error) {
	v := viper.New()
	v.SetConfigFile(GlobalProfile)
	err := v.ReadInConfig()
	if err != nil {
		return 0, fmt.Errorf("error reading global profile, %v", err)
	}
	return v.GetInt("host_id"), err
}
func HasRootPrivilege() error {
	currentUser, err := user.Current()
	if err != nil {
		return err
	}
	if currentUser.Uid != "0" {
		return errors.New("root privilege needed")
	}
	return nil
}

func PackToZip(file, path string) error {
	cmdStr := fmt.Sprintf("zip -jqr %s %s", file, path)
	fmt.Println(cmdStr)
	command := exec.Command("/bin/bash", "-c", cmdStr)
	command.Stdout = os.Stdout
	command.Stderr = os.Stderr
	return command.Run()
}

func UnpackFromZip(file, path string) error {
	cmdStr := fmt.Sprintf("unzip -o -d %s %s", path, file)
	fmt.Println(cmdStr)
	command := exec.Command("/bin/bash", "-c", cmdStr)
	command.Stdout = os.Stdout
	command.Stderr = os.Stderr
	return command.Run()
}

func CleanTempDownloadFiles(path string) error {
	_, err := os.Stat(path)
	if !os.IsNotExist(err) {
		err := os.RemoveAll(path)
		if err != nil {
			return err
		}
	}
	err = os.MkdirAll(path, 0755)
	if err != nil {
		return err
	}
	return nil
}

func AppendToFile(file, content string) error {
	f, err := os.OpenFile(file, os.O_WRONLY|os.O_CREATE, 0644)
	if err != nil {
		qlog.Errorf("append to file: %s, err: %v", file, err)
	} else {
		n, _ := f.Seek(0, io.SeekEnd)
		_, err = f.WriteAt([]byte(content+"\n"), n)
	}
	defer func(f *os.File) {
		_ = f.Close()
	}(f)
	return err
}

func FindFileInDir(dir, fileName string) (string, error) {
	fullPkgName := ""
	err := filepath.Walk(dir, func(_ string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if !info.IsDir() && strings.Contains(info.Name(), fileName) {
			fullPkgName = info.Name()
			return filepath.SkipDir
		}
		return nil
	})
	if err != nil && !errors.Is(err, filepath.SkipDir) {
		return "", fmt.Errorf("not found match %s", fileName)
	}
	return fullPkgName, nil
}

func WriteToJSON(file string, content interface{}) error {
	bytes, err := json.Marshal(content)
	if err != nil {
		return err
	}
	qlog.Infof("dump info to: %s", file)
	return os.WriteFile(file, bytes, 0755)
}

func LoadFromJSON(file string, T interface{}) (interface{}, error) {
	// T 指针类型
	if !isPointerValue(T) {
		return nil, fmt.Errorf("need point value")
	}
	content, err := os.ReadFile(file)
	if err != nil {
		qlog.Errorf("error reading json, %v", err)
		return nil, err
	}
	if err := json.Unmarshal(content, T); err != nil {
		qlog.Errorf("error unmarshal json, %v", err)
		return nil, err
	}
	return T, nil
}

func isPointerValue(i interface{}) bool {
	val := reflect.ValueOf(i)
	return val.Kind() == reflect.Ptr
}

func CmdExecStdoutPipe(cmdStr string) (err error) {
	if viper.GetBool("QLOG_CMD_ENABLE") {
		qlog.Info(cmdStr)
	} else {
		qlog.Debug(cmdStr)
	}
	command := exec.Command("/bin/bash", "-c", cmdStr)
	command.Stdout = os.Stdout
	command.Stderr = os.Stderr
	return command.Run()
}

func GetClusterIps() ([]string, error) {
	v := viper.New()
	v.SetConfigFile(HaConf)
	v.SetConfigType("env")
	err := v.ReadInConfig()
	if err != nil {
		return nil, fmt.Errorf("error reading 50-ha, %v", err)
	}
	corosyncNodeInfo := v.GetString("COROSYNC_NODE_INFO")
	corosyncNodeInfo = strings.TrimSpace(corosyncNodeInfo)
	ips, err := parseClusterIps(corosyncNodeInfo)
	if err != nil {
		return nil, err
	}

	return ips, nil

	// hostType, _ := GetHostType()
	// res := make([]string, 0)
	// if hostType == ServerGW {
	// 	for i := range ips {
	// 		// QMU: **************/12/13
	// 		res = append(res, fmt.Sprintf("*************%d", i+1))
	// 	}
	// }
	// // 单车先不做，等后续上了域控方案再加
	// return res, nil
}

func parseClusterIps(raw string) ([]string, error) {
	if raw == "" {
		qlog.Debug("Input string is empty")
		return nil, nil
	}

	// 移除所有以 # 开头的行
	var lines []string
	for _, line := range strings.Split(raw, "\n") {
		trimmedLine := strings.TrimSpace(line)
		if !strings.HasPrefix(trimmedLine, "#") {
			lines = append(lines, line)
		}
	}

	clusterIps := make([]string, 0, len(lines))
	for _, nodeInfo := range lines {
		split := strings.Split(nodeInfo, ",")
		if len(split) < 2 {
			qlog.Debugf("Invalid node info: %s", nodeInfo)
			continue
		}
		ip := split[1]
		clusterIps = append(clusterIps, ip)
	}
	qlog.Debugf("Parsed %d cluster IPs", len(clusterIps))
	// 将 clusterIps ************* 的最后一位减10，改为 *************,才能互相连接
	for i, ip := range clusterIps {
		split := strings.Split(ip, ".")
		if len(split) != 4 {
			qlog.Debugf("Invalid IP format: %s", ip)
			continue
		}
		parseInt, _ := strconv.Atoi(split[3])
		last := parseInt - 10
		lastStr := strconv.Itoa(last)
		split[3] = lastStr
		clusterIps[i] = strings.Join(split, ".")
	}
	return clusterIps, nil
}

const (
	qpilotParamDir          = "/opt/qomolo/qpilot/share/qpilot_parameters/"
	qpilotOrinParamDir      = "/opt/qomolo/qpilot-orin/share/qpilot_parameters/"
	qpilotTempParamDir      = "/opt/qomolo/qpilot/temp/parameter/"
	QomoloResourceDir       = "/data/qomolo_resource"
	ModuleDownloadLocalPath = QomoloResourceDir + "/integration/module"
	DataTmpDir              = "/data/tmp"
)

func ResourceDir(name, version string) string {
	return fmt.Sprintf("%s/%s/%s", ModuleDownloadLocalPath, name, version)
}

func CleanParamDir(pkgName string) error {
	if pkgName == "qpilot" {
		cmdStr := "apt purge -y qpilot || true"
		// nolint
		command := exec.Command("/bin/bash", "-c", cmdStr)
		command.Stdout = os.Stdout
		command.Stderr = os.Stderr
		err := command.Run()
		if err != nil {
			return err
		}
		return os.RemoveAll(qpilotParamDir)
	}

	if pkgName == "qpilot-orin" {
		cmdStr := "apt purge -y qpilot-orin || true"
		command := exec.Command("/bin/bash", "-c", cmdStr)
		command.Stdout = os.Stdout
		command.Stderr = os.Stderr
		err := command.Run()
		if err != nil {
			return err
		}
		return os.RemoveAll(qpilotOrinParamDir)
	}

	if pkgName == "qpilot-temp-parameter" {
		cmdStr := "apt purge -y qpilot-temp-parameter || true"
		command := exec.Command("/bin/bash", "-c", cmdStr)
		command.Stdout = os.Stdout
		command.Stderr = os.Stderr
		err := command.Run()
		if err != nil {
			return err
		}
		return os.RemoveAll(qpilotTempParamDir)
	}
	return nil
}

type MetaData struct {
	Time      qutils.Time `json:"time"`
	Checksums []Checksum  `json:"checksums"`
}

func (meta MetaData) FileMatchHash(fileName, hashPassIn string, hashTypePassIn HashType) bool {
	for _, checksum := range meta.Checksums {
		hashType, hash := checksum.Hash.parseHash()
		if checksum.File == fileName && hashTypePassIn == hashType && hashPassIn == hash {
			return true
		}
	}
	return false
}

type HashType string

const (
	HashTypeMd5    HashType = "md5"
	HashTypeSha256 HashType = "sha256"
	HashTypeETag   HashType = "etag"
)

func (ht HashType) String() string {
	switch ht {
	case "md5":
		return string(HashTypeMd5)
	case "sha256":
		return string(HashTypeSha256)
	case "etag":
		return string(HashTypeETag)
	default:
		return ""
	}
}

func (ht HashType) GetFileHash(file string) string {
	switch ht {
	case "md5":
		fileHash, _ := GetMD5(file)
		return fileHash
	case "sha256":
		fileHash, _ := GetSHA256(file)
		return fileHash
	case "etag":
		return GetFileNginxETag(file)
	default:
		return ""
	}
}

type Hash string

type Checksum struct {
	File string `json:"file"`
	Hash Hash   `json:"hash"`
}

func (h Hash) parseHash() (HashType, string) {
	l := strings.Split(string(h), ":")
	if len(l) == 2 {
		hashType := HashType(strings.Split(string(h), ":")[0])
		hash := strings.Split(string(h), ":")[1]
		return hashType, hash
	}
	return "", ""
}

func NewHash(hashType HashType, hash string) Hash {
	return Hash(fmt.Sprintf("%s:%s", hashType, hash))
}

type ServerCapability struct {
	SupportRange bool
	FileSize     int64
}

type DownloadTask struct {
	Start int64
	End   int64
	Index int
}

type RawFile struct {
	Name   string    `json:"name"`
	Path   string    `json:"path"`
	Remote string    `json:"remote"`
	Size   int64     `json:"size"`
	Sha256 string    `json:"sha256"`
	Md5    string    `json:"md5"`
	ETag   string    `json:"etag"`
	Mtime  time.Time `omitempty,json:"mtime"`
}

const (
	ChunkSize  = 5 * 1024 * 1024 // 5MB 分片
	MaxRetries = 3               // 分片下载重试次数
	NumWorkers = 3               // 下载并发数
)

type FileDownloadProgress struct {
	Current      int64     `json:"current"`
	Total        int64     `json:"total"`
	Name         string    `json:"name"`
	Path         string    `json:"path"`
	Remote       string    `json:"remote"`
	Size         int64     `json:"size"`
	Sha256       string    `json:"sha256"`
	Md5          string    `json:"md5"`
	ETag         string    `json:"etag"`
	Mtime        time.Time `omitempty,json:"mtime"`
	LastUpdate   int64     `json:"last_update"`
	SkipDownload bool      `json:"skip_download"`
}

func (fdp *FileDownloadProgress) Write(p []byte) (n int, err error) {
	n = len(p)
	fdp.Current += int64(n)
	fdp.LastUpdate = time.Now().UnixMilli()
	return
}

func DownloadWithResumeAndChunkSingleRawFile(baseUrl, downloadDir string, filePathWithFileName bool, fdp *FileDownloadProgress) error {
	if _, err := os.Stat(downloadDir); os.IsNotExist(err) {
		err1 := os.MkdirAll(downloadDir, 0755)
		if err1 != nil {
			return err1
		}
	}
	url := baseUrl + fdp.Remote
	filePath := fdp.Path
	if !filePathWithFileName {
		url = baseUrl + fdp.Path + "/" + fdp.Name
		filePath = fdp.Path + "/" + fdp.Name
	}
	client := resty.New()
	client.SetRetryCount(MaxRetries)
	client.SetRetryWaitTime(3 * time.Second)
	client.SetDoNotParseResponse(true)

	_ = os.MkdirAll(filepath.Join(downloadDir, filepath.Dir(filePath)), 0755)
	out, err := os.Create(filepath.Join(downloadDir, filePath))
	if err != nil {
		return err
	}

	resp, err := client.Clone().R().Get(url)

	if err != nil {
		return err
	}
	defer func(resp *resty.Response) {
		_ = resp.RawBody().Close()
	}(resp)

	if resp.StatusCode() != http.StatusOK {
		_ = os.Remove(filepath.Join(downloadDir, filePath))
		return fmt.Errorf("request %s failed, code %v", url, resp.StatusCode())
	}

	if _, err = io.Copy(out, io.TeeReader(resp.RawBody(), fdp)); err != nil {
		return err
	}
	return nil
}

// 检查服务器能力
func ProbeServer(url string) (ServerCapability, error) {
	client := resty.New()
	resp, err := client.R().Head(url)
	if err != nil {
		return ServerCapability{}, err
	}

	capability := ServerCapability{
		SupportRange: resp.Header().Get("AcceptRanges") == "bytes",
	}

	if contentLength := resp.Header().Get("ContentLength"); contentLength != "" {
		if size, err := strconv.ParseInt(contentLength, 10, 64); err == nil {
			capability.FileSize = size
		}
	}

	return capability, nil
}

func ParseMetaDataJson(path string) (*MetaData, error) {
	metaDataJson := path + "/metadata.json"
	f, err := os.Open(metaDataJson)
	if err != nil {
		return nil, err
	}
	defer func(f *os.File) {
		_ = f.Close()
	}(f)
	metaData := &MetaData{}
	decoder := json.NewDecoder(f)
	if err := decoder.Decode(&metaData); err != nil {
		return nil, fmt.Errorf("json decode %s failed", metaDataJson)
	}
	return metaData, nil
}

func CheckAllFileHash(path string) (ok bool, err error) {
	metaData := &MetaData{}
	filesCheckNotPassed := make([]string, 0)
	exist := CheckFileExist(path + "/metadata.json")
	if exist {
		metaData, err = ParseMetaDataJson(path)
		if err != nil {
			qlog.Debugf("parse metadata json failed, err: %v", err)
			return false, err
		}
		for _, checksum := range metaData.Checksums {
			fullFilePath := path + "/" + checksum.File
			hashType, hash := checksum.Hash.parseHash()
			fileHash := hashType.GetFileHash(fullFilePath)
			if hash != fileHash {
				qlog.Debugf("%v %v", checksum, fileHash)
				filesCheckNotPassed = append(filesCheckNotPassed, checksum.File)
			}
		}
		if len(filesCheckNotPassed) > 0 {
			qlog.Debugf("files check hash not passed, %s", strings.Join(filesCheckNotPassed, ""))
			return false, fmt.Errorf("files check hash not passed, %s", strings.Join(filesCheckNotPassed, ""))
		}
	} else {
		qlog.Debugf("%s/metadata.json not exist", path)
		return false, fmt.Errorf("%s/metadata.json not exist", path)
	}
	return true, nil
}

func CheckModuleHash(name, version string) (bool, error) {
	dir := ResourceDir(name, version)
	return CheckAllFileHash(dir)
}

// 分片下载 worker
func downloadChunk(client *resty.Client, url, fileName, downloadDir string, task DownloadTask, wg *sync.WaitGroup, maxRetries int) {
	defer wg.Done()

	tempFile := filepath.Join(downloadDir, fmt.Sprintf("%s-part%d.tmp", fileName, task.Index))

	// 检查已有分片
	if info, err := os.Stat(tempFile); err == nil {
		if info.Size() == (task.End - task.Start + 1) {
			return
		}
		os.Remove(tempFile)
	}

	// 设置 Range 头
	rangeHeader := fmt.Sprintf("bytes=%d%d", task.Start, task.End)
	if task.End == 0 {
		rangeHeader = fmt.Sprintf("bytes=%d", task.Start)
	}

	var resp *resty.Response
	var err error
	for attempt := 0; attempt < maxRetries; attempt++ {
		resp, err = client.Clone().R().
			SetHeader("Range", rangeHeader).
			SetOutput(tempFile).
			Get(url)

		if err == nil && (resp.StatusCode() == http.StatusOK || resp.StatusCode() == http.StatusPartialContent) {
			return
		}

		time.Sleep(time.Duration(attempt+1) * time.Second)
	}

	if err != nil {
		fmt.Printf("分片 %d 下载失败: %vn", task.Index, err)
		os.Remove(tempFile)
	}
}

// 合并临时文件
func mergeFiles(downloadDir, fileName, outputPath string, numChunks int) error {
	outFile, err := os.Create(outputPath)
	if err != nil {
		return err
	}
	defer outFile.Close()

	for i := 0; i < numChunks; i++ {
		tempFile := filepath.Join(downloadDir, fmt.Sprintf("%s-part%d.tmp", fileName, i))
		inFile, err := os.Open(tempFile)
		if err != nil {
			return err
		}

		if _, err = io.Copy(outFile, inFile); err != nil {
			inFile.Close()
			return err
		}
		inFile.Close()
		os.Remove(tempFile)
	}

	return nil
}

func RemoveEmpty(s1 []string) []string {
	s2 := make([]string, 0)
	for _, v := range s1 {
		if v != "" {
			s2 = append(s2, v)
		}
	}
	return s2
}

type installDbgPackageKey struct{}

func SetCtxInstallDbgPackage(ctx context.Context, installDbgPackage bool) context.Context {
	return context.WithValue(ctx, installDbgPackageKey{}, installDbgPackage)
}

func GetCtxInstallDbgPackage(ctx context.Context) bool {
	installDbgPackage, ok := ctx.Value(installDbgPackageKey{}).(bool)
	if !ok {
		return false
	}
	return installDbgPackage
}
