package utils

import (
	"context"
	"crypto/md5"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"syscall"
	"time"

	"golang.org/x/sys/unix"

	qlog "gitlab.qomolo.com/infrastructure/sw_architecture/middleware_abstraction/utility/qlog_golang"
)

// GetFileSHA256Hash 函数用于获取文件的SHA256哈希值
func GetFileSHA256Hash(filePath string) (string, error) {
	// 打开文件
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer func(file *os.File) {
		_ = file.Close()
	}(file)
	return GetSHA256Hash(file)
}

// GetSHA256Hash 函数用于获取reader的SHA256哈希值
func GetSHA256Hash(reader io.Reader) (string, error) {
	// 创建一个新的SHA256哈希对象
	hash := sha256.New()

	// 将文件内容写入哈希对象
	if _, err := io.Copy(hash, reader); err != nil {
		return "", err
	}

	return hex.EncodeToString(hash.Sum(nil)), nil
}

func GetSHA256(filePath string) (string, error) {
	// 打开文件
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer func(file *os.File) {
		_ = file.Close()
	}(file)
	// 创建一个新的sha256哈希对象
	hash := sha256.New()
	// 将文件内容写入哈希对象
	if _, err := io.Copy(hash, file); err != nil {
		return "", err
	}
	// 获取哈希值的字节数组
	hashBytes := hash.Sum(nil)
	// 将字节数组转换为十六进制字符串
	return hex.EncodeToString(hashBytes), nil
}

func GetMD5(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer func(file *os.File) {
		_ = file.Close()
	}(file)

	hasher := md5.New()
	if _, err := io.Copy(hasher, file); err != nil {
		return "", err
	}

	hashBytes := hasher.Sum(nil)
	hashString := hex.EncodeToString(hashBytes)
	return hashString, nil
}
func GetMD5Ctx(ctx context.Context, filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	// nolint
	defer file.Close()

	hasher := md5.New()
	buf := make([]byte, 4096) // 使用缓冲区分块读取文件
	for {
		select {
		case <-ctx.Done():
			return "", ctx.Err()
		default:
			n, err := file.Read(buf)
			if err != nil {
				if err == io.EOF {
					hashBytes := hasher.Sum(nil)
					hashString := hex.EncodeToString(hashBytes)
					// 文件读取完毕
					return hashString, nil
				}
				return "", err
			}
			hasher.Write(buf[:n])
		}
	}
}

// GetFileNginxETag
// ETag: "5cbee66d-264"
// Last-Modified 与 Content-Length 十六进制组合
func GetFileNginxETag(localPath string) string {
	fileInfo, err := os.Stat(localPath)
	if err != nil {
		return ""
	}
	return CalNginxETag(fileInfo.ModTime(), fileInfo.Size())
}

func CalNginxETag(mtime time.Time, size int64) string {
	return fmt.Sprintf(`"%x-%x"`, mtime.Unix(), size)
}

// GetFolderSize 函数用于获取文件夹的大小，只统计当前层，不递归处理
func GetFolderSize(path string) (int64, error) {
	var size int64
	err := filepath.Walk(path, func(_ string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if !info.IsDir() {
			size += info.Size()
		}
		return err
	})
	if err != nil {
		return 0, err
	}
	return size, nil
}

func CheckDiskFree(path string) (int64, error) {
	var stat unix.Statfs_t
	err := unix.Statfs(path, &stat)
	if err != nil {
		return 0, err
	}
	return int64(stat.Bavail * uint64(stat.Bsize)), nil
}

// CleanFilesInDirBySuffix 清理指定目录下指定后缀的文件,按access时间清理
func CleanFilesInDirBySuffix(dir, suffix string, duration time.Duration) error {
	fileInfos, err := os.ReadDir(dir)
	if err != nil {
		return err
	}
	for _, fileInfo := range fileInfos {
		fInfo, err := fileInfo.Info()
		if err != nil {
			return err
		}
		if !strings.HasSuffix(fInfo.Name(), suffix) {
			continue
		}
		accessSeconds, _ := fInfo.Sys().(*syscall.Stat_t).Atim.Unix()
		diff := time.Now().Unix() - accessSeconds
		if float64(diff) > duration.Seconds() {
			qlog.Infof("Not accessed for more than %f hours, deleting %s\n", duration.Hours(), fInfo.Name())
			_ = os.Remove(dir + fInfo.Name())
		}
	}
	return err
}

func CopyFile(srcName, dstName string) (written int64, err error) {
	stat, err := os.Stat(srcName)
	if err != nil {
		return
	}
	src, err := os.Open(srcName)
	if err != nil {
		return
	}
	// nolint
	defer src.Close()
	dst, err := os.OpenFile(dstName, os.O_WRONLY|os.O_CREATE, stat.Mode())
	if err != nil {
		return
	}
	// nolint
	defer dst.Close()
	return io.Copy(dst, src)
}
