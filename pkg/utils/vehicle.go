package utils

import "regexp"

type VehicleCategory string

const (
	VehicleTypeCategoryQthd VehicleCategory = "qthd"
	VehicleTypeCategoryQbus VehicleCategory = "qbus"
	VehicleTypeCategoryIgv  VehicleCategory = "igv"
	VehicleTypeCategoryBus  VehicleCategory = "bus"
	VehicleTypeCategoryTug  VehicleCategory = "tug"
	VehicleTypeCategoryQt   VehicleCategory = "qt"
	VehicleTypeCategoryWs   VehicleCategory = "ws"
	VehicleTypeCategoryEt   VehicleCategory = "et"
)

var VehicleTypeCategories = []VehicleCategory{
	VehicleTypeCategoryQt, VehicleTypeCategoryIgv, VehicleTypeCategoryQthd, VehicleTypeCategoryQbus,
	VehicleTypeCategoryBus, VehicleTypeCategoryTug, VehicleTypeCategoryWs, VehicleTypeCategoryEt,
}

type VehicleTypeCategoryPattern struct {
	Pattern string
	Type    VehicleCategory
}

var abbrPatterns = []VehicleTypeCategoryPattern{
	{
		Pattern: "^qthd*",
		Type:    VehicleTypeCategoryQthd,
	},
	{
		Pattern: "^qbus*",
		Type:    VehicleTypeCategoryQbus,
	},
	{
		Pattern: "^igv*",
		Type:    VehicleTypeCategoryIgv,
	},
	{
		Pattern: "^bus*",
		Type:    VehicleTypeCategoryBus,
	},
	{
		Pattern: "^tug*",
		Type:    VehicleTypeCategoryTug,
	},
	{
		Pattern: "^qt*",
		Type:    VehicleTypeCategoryQt,
	},
	{
		Pattern: "^ws*",
		Type:    VehicleTypeCategoryWs,
	},
	{
		Pattern: "^et*",
		Type:    VehicleTypeCategoryEt,
	},
}

func GetVehicleCategory(vehicleType string) VehicleCategory {
	for _, item := range abbrPatterns {
		matched, _ := regexp.MatchString(item.Pattern, vehicleType)
		if matched {
			return item.Type
		}
	}
	return ""
}
