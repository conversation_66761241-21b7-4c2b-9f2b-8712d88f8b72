package utils

import (
	"reflect"
	"testing"
)

func Test_getDCUInfo(t *testing.T) {
	type args struct {
		s string
	}
	tests := []struct {
		name     string
		args     args
		wantInfo DCUInfo
	}{
		{
			name: "orin 5.1",
			args: args{
				s: "MIIVII APEX AD10 5.1.1-2.0.0.9",
			},
			wantInfo: DCUInfo{
				Model:   Xavierad10,
				Type:    Orin,
				Version: JP51,
			},
		},
		{
			name: "xavier 51",
			args: args{
				s: "miivii apex xavier ii 5.1.1-4.0.0.33",
			},
			wantInfo: DCUInfo{
				Model:   <PERSON><PERSON>,
				Type:    <PERSON>,
				Version: JP51,
			},
		}, {
			name: "xavier 45",
			args: args{
				s: "MIIVII APEX XAVIER II 4.5-2.7.2",
			},
			wantInfo: DCUInfo{
				Model:   Xavierii,
				Type:    Xavier,
				Version: JP45,
			},
		},
		{
			name: "xavier 同时包含5.1 4.5",
			args: args{
				s: "MIIVII APEX XAVIER II 5.1-4.5",
			},
			wantInfo: DCUInfo{
				Model:   <PERSON><PERSON>,
				Type:    <PERSON>,
				Version: JP51,
			},
		},
		{
			name: "empty",
			args: args{
				s: "",
			},
			wantInfo: DCUInfo{
				Model:   "",
				Type:    "",
				Version: "",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if gotInfo := getDCUInfo(tt.args.s); !reflect.DeepEqual(gotInfo, tt.wantInfo) {
				t.Errorf("getDCUInfo() = %v, want %v", gotInfo, tt.wantInfo)
			}
		})
	}
}

// TestParseClusterIps 测试 parseClusterIps 函数
func TestParseClusterIps(t *testing.T) {
	tests := []struct {
		name     string
		raw      string
		expected []string
		wantErr  bool
	}{
		{
			name: "Normal input",
			raw: `
			#!/bin/bash
CLUSTER_NAME="TOMTEST"
COROSYNC_CONSENSUS=8000

LAN0_HA_IP=************
MANG0_HA_IP=*************
BUS0_HA_IP=************

WAN0_HA_IP="
**************
**************
**************
**************
"
#WAN0_HA_IP=**************
# WAN1_HA_IP=************
# WAN2_HA_IP=************
# WAN3_HA_IP=************

COROSYNC_NODE_INFO="
WL-test-gw-01,*************
WL-test-gw-02,*************
WL-test-gw-03,*************
"
USE_SERVICE_WS=true
			`,
			expected: []string{"************1", "************2", "************3"},
			wantErr:  false,
		},
		{
			name:     "Empty input",
			raw:      "",
			expected: []string{},
			wantErr:  false,
		},
		{
			name: "Comment input",
			raw: `
			#!/bin/bash
CLUSTER_NAME="TOMTEST"
COROSYNC_CONSENSUS=8000

LAN0_HA_IP=************
MANG0_HA_IP=*************
BUS0_HA_IP=************

WAN0_HA_IP="
**************
**************
**************
**************
"
#WAN0_HA_IP=**************
# WAN1_HA_IP=************
# WAN2_HA_IP=************
# WAN3_HA_IP=************

#COROSYNC_NODE_INFO="
#WL-test-gw-01,*************
#WL-test-gw-02,*************
#WL-test-gw-03,*************
#"

COROSYNC_NODE_INFO="
WL-test-gw-01,*************
WL-test-gw-02,*************
WL-test-gw-03,*************
"
USE_SERVICE_WS=true
			`,
			expected: []string{"*************", "*************", "*************"},
			wantErr:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := parseClusterIps(tt.raw)
			if (err != nil) != tt.wantErr {
				t.Errorf("parseClusterIps() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr && !equalSlice(got, tt.expected) {
				t.Errorf("parseClusterIps() = %v, want %v", got, tt.expected)
				return
			}
			t.Logf("parseClusterIps() = %v", got)
		})
	}
}

// equalSlice 判断两个字符串切片是否相等
func equalSlice(a, b []string) bool {
	if len(a) != len(b) {
		return false
	}
	for i := range a {
		if a[i] != b[i] {
			return false
		}
	}
	return true
}
