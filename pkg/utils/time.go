package utils

import (
	"fmt"
	"time"
)

// time.RFC3339
type Time time.Time

func (t *Time) UnmarshalJSON(data []byte) (err error) {
	parsedTime, err := time.Parse(fmt.Sprintf(`"%s"`, time.RFC3339), string(data))
	*t = Time(parsedTime)
	return
}

func (t Time) MarshalJSON() ([]byte, error) {
	formatted := fmt.Sprintf("\"%s\"", time.Time(t).Format(time.RFC3339))
	return []byte(formatted), nil
}

func (t Time) String() string {
	return time.Time(t).Format(time.RFC3339)
}
func (t Time) Time() time.Time {
	return time.Time(t)
}
