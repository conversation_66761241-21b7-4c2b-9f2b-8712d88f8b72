package qpilot

import (
	"fmt"
	"os"
	"path/filepath"

	"gitlab.qomolo.com/qomolo_utils/qomolo_get/pkg/utils"

	"gopkg.in/yaml.v3"
)

const profileDirPath = "/etc/qomolo/profile/ads/qpilot/"

func GetQpilotMap() (*QpilotResource, error) {
	resource, exist, err := GetQpilotResource()
	if err != nil {
		return nil, fmt.Errorf("读取qpilot配置失败，err:%s", err)
	}
	if !exist {
		return nil, nil
	}
	return &resource, nil
}

func GetQpilotResource() (res QpilotResource, exist bool, err error) {
	filePath := filepath.Join(profileDirPath, "profile.yaml")
	exist = utils.CheckFileExist(filePath)
	if !exist {
		return res, exist, nil
	}
	content, err := os.ReadFile(filePath)
	if err != nil {
		return
	}
	err = yaml.Unmarshal(content, &res)
	if err != nil {
		return
	}
	return
}
