package qpilot

/*
QpilotResource
地图资源参数格式
osm_map:

	module_name: qomolo-resource-osm-map-cnwha-igv
	time: "2025-06-13T11:26:02+08:00"
	version: 0.0.2-1749778906

pcd_map:

	module_name: qomolo-resource-pcd-map-cnwha
	time: "2025-06-13T12:26:26+08:00"
	version: 0.1.1-1749181599
*/
type QpilotResource struct {
	PcdMap Module `yaml:"pcd_map"`
	OsmMap Module `yaml:"osm_map"`
}

type Module struct {
	ModuleName string `yaml:"module_name"`
	Version    string `yaml:"version"`
	Time       string `yaml:"time"`
}

func (r QpilotResource) GetPcdMap() Module {
	return r.PcdMap
}

func (r QpilotResource) GetOsmMap() Module {
	return r.OsmMap
}
