---
description:
globs:
alwaysApply: false
---
# Code Review MDC 规则
# 全面的代码审查指导原则和检查清单

## 代码审查核心目标
- 🔍 **质量保证**: 确保代码符合项目标准和最佳实践
- 🔒 **安全防护**: 识别潜在的安全漏洞和风险点
- 🚀 **性能优化**: 发现性能问题和改进机会
- 📚 **知识共享**: 团队成员间的技术交流和学习
- 🛠 **可维护性**: 确保代码易于理解、修改和扩展

## 代码审查检查清单

### 1. 架构和设计审查
- [ ] **单一职责原则**: 每个函数/类只负责一个功能
- [ ] **依赖倒置**: 高层模块不依赖低层模块，都依赖抽象
- [ ] **接口设计**: 接口小而精，遵循ISP原则
- [ ] **模块划分**: 合理的包结构和模块边界
- [ ] **设计模式**: 合适的设计模式应用，避免过度设计
- [ ] **SOLID原则**: 遵循面向对象设计原则
- [ ] **领域建模**: 业务逻辑清晰，领域概念准确

### 2. 代码质量审查
- [ ] **命名规范**: 变量、函数、类型名称有意义且一致
- [ ] **代码复用**: 避免重复代码，提取公共逻辑
- [ ] **函数长度**: 函数不超过50行，复杂度合理
- [ ] **圈复杂度**: 避免过于复杂的条件判断和嵌套
- [ ] **Magic Number**: 使用常量替代魔法数字
- [ ] **注释质量**: 注释准确、有用，解释"为什么"而非"是什么"
- [ ] **代码风格**: 遵循Go语言规范和团队约定

### 3. 错误处理和异常安全
- [ ] **错误检查**: 所有可能的错误都得到处理
- [ ] **错误信息**: 错误消息包含足够的上下文信息
- [ ] **错误传播**: 合理的错误包装和传播链
- [ ] **恢复机制**: 关键操作有适当的重试和回滚
- [ ] **资源清理**: 使用defer确保资源正确释放
- [ ] **边界条件**: 处理空值、空字符串、边界值等特殊情况
- [ ] **超时控制**: 网络请求和长时间操作有超时机制

### 4. 安全性审查
- [ ] **输入验证**: 所有外部输入都经过验证和清理
- [ ] **SQL注入**: 使用参数化查询，避免SQL注入
- [ ] **XSS防护**: Web接口有适当的输出编码
- [ ] **权限控制**: 访问控制和权限验证
- [ ] **敏感数据**: 密码、密钥等敏感信息安全处理
- [ ] **加密算法**: 使用安全的加密算法和密钥管理
- [ ] **日志安全**: 不在日志中记录敏感信息
- [ ] **依赖安全**: 第三方依赖的安全性检查

### 5. 性能和效率审查
- [ ] **算法复杂度**: 时间和空间复杂度合理
- [ ] **数据库查询**: 避免N+1查询，使用合适的索引
- [ ] **内存使用**: 避免内存泄漏，合理使用内存池
- [ ] **并发安全**: 正确使用锁，避免死锁和竞态条件
- [ ] **缓存策略**: 合理使用缓存，避免缓存击穿
- [ ] **网络优化**: 减少网络请求次数，使用连接池
- [ ] **I/O操作**: 避免阻塞I/O，使用异步处理
- [ ] **资源池化**: 复用昂贵资源，如数据库连接

### 6. 并发和线程安全
- [ ] **竞态条件**: 识别和避免数据竞争
- [ ] **死锁预防**: 锁的获取顺序一致，避免循环等待
- [ ] **原子操作**: 使用atomic包进行原子操作
- [ ] **Channel使用**: 正确使用channel进行goroutine通信
- [ ] **Context传递**: 使用context进行取消和超时控制
- [ ] **goroutine泄漏**: 确保goroutine能够正常退出
- [ ] **同步原语**: 合理选择sync.Mutex, sync.RWMutex等

### 7. 测试覆盖和质量
- [ ] **单元测试**: 核心逻辑有完整的单元测试
- [ ] **测试覆盖率**: 代码覆盖率达到80%以上
- [ ] **边界测试**: 测试边界条件和异常情况
- [ ] **Mock使用**: 外部依赖使用mock进行隔离
- [ ] **集成测试**: 关键业务流程有集成测试
- [ ] **性能测试**: 性能敏感的代码有基准测试
- [ ] **测试数据**: 测试数据真实有效，覆盖多种场景
- [ ] **测试维护**: 测试代码同样需要维护和重构

### 8. OTA特定审查要点
- [ ] **版本兼容性**: 向前和向后兼容性检查
- [ ] **更新原子性**: 更新操作的原子性保证
- [ ] **回滚机制**: 失败时的回滚策略
- [ ] **进度报告**: 更新进度的实时反馈
- [ ] **完整性校验**: 文件和数据的完整性验证
- [ ] **签名验证**: 更新包的数字签名验证
- [ ] **网络重试**: 网络不稳定时的重试策略
- [ ] **存储管理**: 磁盘空间检查和清理

### 9. 文档和可维护性
- [ ] **API文档**: 公开接口有完整的文档
- [ ] **变更记录**: 重要变更有说明和记录
- [ ] **配置文档**: 配置项有详细说明
- [ ] **部署指南**: 部署和运维文档完整
- [ ] **故障排查**: 常见问题的排查指南
- [ ] **监控指标**: 关键指标的监控和告警
- [ ] **日志规范**: 结构化日志，便于分析
- [ ] **代码注释**: 复杂逻辑有清晰的注释

### 10. 代码规范和风格
- [ ] **格式化**: 使用gofmt和goimports格式化
- [ ] **静态检查**: 通过golangci-lint检查
- [ ] **命名约定**: 遵循Go语言命名规范
- [ ] **包组织**: 合理的包结构和导入顺序
- [ ] **常量定义**: 使用const和iota定义常量
- [ ] **接口设计**: 小而精的接口设计
- [ ] **类型安全**: 避免类型断言，使用类型开关
- [ ] **零值有用**: 类型的零值应该是有用的

## 审查优先级分级

### 🔴 高优先级 (必须修复)
- 安全漏洞
- 数据丢失风险
- 内存泄漏
- 死锁和竞态条件
- 严重的性能问题

### 🟡 中优先级 (建议修复)
- 代码重复
- 命名不清晰
- 缺少测试
- 文档不完整
- 轻微的性能问题

### 🟢 低优先级 (可选修复)
- 代码风格问题
- 注释格式
- 微小的重构机会
- 非关键的优化

## 审查工具和自动化

### 静态分析工具
- `golangci-lint`: 综合性的Go语言linter
- `go vet`: Go官方的静态分析工具
- `staticcheck`: 高质量的静态分析工具
- `gosec`: 安全性分析工具
- `ineffassign`: 检查无效赋值
- `misspell`: 检查拼写错误

### 测试工具
- `go test`: 运行测试和基准测试
- `go test -race`: 竞态条件检测
- `go test -cover`: 测试覆盖率分析
- `testify`: 测试断言和mock框架
- `gomock`: Mock代码生成工具

### 代码质量工具
- `gocyclo`: 圈复杂度分析
- `dupl`: 重复代码检测
- `gocognit`: 认知复杂度分析
- `golines`: 长行检测和格式化

## 审查清单使用指南

### 审查前准备
1. 理解需求和设计意图
2. 检查相关的测试用例
3. 了解变更的影响范围
4. 准备测试环境

### 审查过程
1. 先宏观后微观: 先看整体架构，再看具体实现
2. 重点关注: 安全性、性能、可维护性
3. 逐一检查: 按照清单逐项审查
4. 记录问题: 详细记录发现的问题和建议

### 审查后跟进
1. 与作者讨论问题和解决方案
2. 确认修复质量
3. 更新文档和测试
4. 总结经验和改进点

## 常见问题和解决方案

### 性能问题
- **问题**: 频繁的内存分配
- **解决**: 使用对象池或预分配内存

- **问题**: 低效的字符串操作
- **解决**: 使用strings.Builder或bytes.Buffer

### 安全问题
- **问题**: 未验证的用户输入
- **解决**: 添加输入验证和清理

- **问题**: 敏感信息泄漏
- **解决**: 使用安全的日志记录和错误处理

### 并发问题
- **问题**: 数据竞争
- **解决**: 使用mutex或channel进行同步

- **问题**: goroutine泄漏
- **解决**: 确保goroutine有退出机制

## 审查记录模板

```markdown
## Code Review 记录

**审查人**: [姓名]
**被审查人**: [姓名]
**提交Hash**: [git commit hash]
**审查日期**: [日期]

### 总体评价
- [ ] 功能完整性
- [ ] 代码质量
- [ ] 测试覆盖
- [ ] 文档完整性

### 发现的问题
1. **安全性问题**: [描述]
2. **性能问题**: [描述]
3. **可维护性问题**: [描述]

### 改进建议
1. [具体建议]
2. [具体建议]

### 审查结果
- [ ] 通过 (无问题或minor问题)
- [ ] 有条件通过 (需要修复指定问题)
- [ ] 不通过 (需要重大修改)
```

记住: 代码审查不仅仅是找问题，更是团队学习和改进的机会！
