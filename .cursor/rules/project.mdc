---
description:
globs:
alwaysApply: true
---
# Golang 项目 Cursor Rules
# 专注于代码质量、Code Review和可维护性

## 项目概述
这是一个Golang功能项目，重点关注代码质量、可维护性和安全性。

## 核心原则
- 优先考虑代码的可读性和可维护性
- 遵循Go语言最佳实践和惯用法
- 确保OTA功能的安全性和可靠性
- 编写易于测试和重构的代码

## 代码质量标准

### Go语言规范
- 严格遵循 `gofmt` 格式化标准
- 使用 `golangcilint` 和 `go vet` 进行静态检查
- 遵循Go语言命名约定（PascalCase for exported, camelCase for unexported）
- 包名使用小写单词，避免下划线和驼峰命名
- 接口名以 -er 结尾（如 Reader, Writer, Updater）

### 代码结构
- 每个函数应该职责单一，长度不超过50行
- 复杂逻辑拆分为多个小函数
- 使用依赖注入提高可测试性
- 避免深层嵌套，最多3层
- 使用early return减少嵌套

### 错误处理
- 明确处理所有错误，不允许忽略错误
- 使用具有上下文信息的错误消息
- 优先使用 `fmt.Errorf` 包装错误
- 为关键操作定义自定义错误类型
- 在适当的层级记录错误日志

### OTA特定要求
- 所有下载和更新操作必须进行完整性校验
- 实现重试机制和回滚功能
- 添加详细的操作日志和监控点
- 确保原子性操作，避免部分更新状态
- 实现进度报告和状态追踪

## Code Review要求

### 安全性检查
- 验证所有外部输入
- 确保文件路径安全，防止路径遍历攻击
- 检查权限控制和访问限制
- 验证签名和证书
- 确保敏感信息不被记录

### 性能考虑
- 避免在循环中进行重复的重开销操作
- 合理使用goroutine和channel
- 注意内存泄漏，及时释放资源
- 使用context进行超时控制
- 考虑并发安全
### 测试覆盖
- 新功能必须包含单元测试
- 测试覆盖率不低于80%
- 包含错误场景的测试用例
- 集成测试覆盖关键路径
- Mock外部依赖进行测试

## 文档和注释

### 注释规范
- 所有导出的函数、类型和变量必须有注释
- 注释以类型或函数名开头
- 复杂算法添加实现思路说明
- 标注TODO、FIXME、HACK等特殊标记
- 包级别注释说明包的用途

### 文档要求
- README包含项目概述、安装和使用说明
- API文档使用godoc格式
- 重要配置项有详细说明
- 部署和运维文档完整

## 可维护性原则

### 依赖管理
- 使用go modules管理依赖
- 定期更新依赖，关注安全补丁
- 避免循环依赖
- 最小化外部依赖

### 配置管理
- 使用配置文件而非硬编码
- 支持环境变量覆盖
- 配置验证和默认值
- 敏感配置加密存储

### 日志和监控
- 使用结构化日志（JSON格式）
- 设置合适的日志级别

## 具体编码建议

### 函数设计
- 参数不超过3个，复杂参数使用struct
- 返回值明确，避免naked return
- 使用context.Context作为第一个参数
- 优先返回接口类型而非具体类型

### 并发编程
- 使用sync包的原语保证并发安全
- 避免共享可变状态
- 使用channel进行goroutine通信
- 实现graceful shutdown

### 文件操作
- 使用defer确保文件关闭
- 实现原子写入操作
- 验证文件权限和磁盘空间
- 处理文件锁定冲突

## 禁止事项
- 不使用panic处理可恢复错误
- 不在生产代码中使用fmt.Print*进行调试
- 不直接操作unsafe包
- 不使用全局变量存储状态
- 不忽略context取消信号
