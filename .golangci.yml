# Refer to golangci-lint's example config file for more options and information:
# https://github.com/golangci/golangci-lint/blob/master/.golangci.reference.yml

run:
  timeout: 3m
  tests: false

linters-settings:
  revive:
    rules:
      - name: add-constant
        arguments:
          - maxLitCount: "10"
            allowStrs: '"",/,"name","0"," ","version","cmd"'
            allowInts: "0,1,2,3,4,5,8,10,32,64,100"
            allowFloats: "0.0, 0."
            ignoreFuncs: "os\\.*,make"
      - name: blank-imports
      - name: context-as-argument
      - name: context-keys-type
      - name: dot-imports
      - name: empty-block
      - name: error-naming
      - name: error-return
      - name: error-strings
      - name: errorf
      - name: if-return
      - name: increment-decrement
      - name: indent-error-flow
      - name: range
      - name: receiver-naming
      - name: redefines-builtin-id
      - name: superfluous-else
      - name: time-naming
      - name: unexported-return
      - name: unreachable-code
      - name: unused-parameter
      - name: var-declaration
      - name: var-naming
linters:
  enable:
    - errcheck
    - goimports
    - govet
    - staticcheck
    - ineffassign
    - revive
    - bodyclose
    - loggercheck

issues:
  exclude-dirs:
  - pkg
  exclude-use-default: false
  max-issues-per-linter: 0
  max-same-issues: 0
