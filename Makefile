PATH := $(HOME)/.gvm/gos/go1.21/bin/:$(HOME)/go/bin/:/opt/bin/:${PATH}
SHELL := env PATH=$(PATH) /bin/bash
PACKAGE=gitlab.qomolo.com/qomolo_utils/qomolo_get
VERSION=$(shell git describe --tags --always)-dev
GO_BUILD_ARGS=-trimpath -ldflags "-w -s -X $(PACKAGE)/internal/lib.Version=$(VERSION)"

.PHONY: build
build:
	@GOOS=linux GOARCH=${PACKAGE_ARCHITECTURE} go build $(GO_BUILD_ARGS)  -o _bin/ ./internal
	@mv _bin/internal _bin/qomolo-get
