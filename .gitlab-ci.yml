include:
  - project: "cicd/templates/devops_template"
    file: "/workflow/distdev_default.yml"
  # - project: "cicd/templates/devops_template"
  #   file: "/pipeline/docker.yml"

stages:
  - prepare
  - build
  - archive
  - test
  - release

variables:
  DEVOPS_APP_LANGUAGE: "golang"
  DEVOPS_CI_CUSTOM_SCRIPT_TEST: "true"
  DEVOPS_CI_CUSTOM_SCRIPT_BUILD: "true"
  CODE_QUALITY_DISABLED: "true"

.local:ci_vars:
  variables:
    PACKAGE_PREFIX: "qomolo-"
    PACKAGE_NAME: "get"
    PACKAGE_INSTALL_PATH: "/opt/qomolo/utils"
    PACKAGE_MAINTAINER: "<EMAIL>"
    PACKAGE_DESCRIPTION: "This is a qomolo package manager"

.local:parallel_matrix:
  parallel:
    matrix:
      - PACKAGE_ARCHITECTURE: ["amd64"]
      - PACKAGE_ARCHITECTURE: ["arm64"]

regression_test:
  extends: .local_rules_regression_test_single
  image: harbor.qomolo.com/arm64/xvaier-focal-runtime
  stage: test
  variables:
    PACKAGE_ARCHITECTURE: arm64
  allow_failure: false
  tags:
    - arm64
  dependencies:
    - deb_pkg
  script:
    - cd regression_test
    - bash test.sh

unit_test:
  rules:
    - when: never

sast-analyzer:
  rules:
    - when: never
    
sca_analyzer:
  rules:
    - when: never