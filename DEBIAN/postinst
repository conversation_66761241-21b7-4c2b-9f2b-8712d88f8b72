#!/usr/bin/env bash
echo "do postinst jobs"


olddir_installed_version=0.4.104-1267367

if [[ $2 == "" ]];then
    echo 新安装
else
    if [[ $(dpkg --compare-versions $2 lt $olddir_installed_version && echo true) == "true" ]];then
        echo 这是升级
		# log dir
		if [ ! -d "/data/code/all_ws/ws/qomolo-get" ]; then
            echo "旧版本残留目录，删除"
        else    
            cp -rf /data/code/all_ws/ws/qomolo-get/* /data/qomolo-get
            rm -rf /data/code/all_ws/ws/qomolo-get
		fi
    fi
    if [[ $(dpkg --compare-versions $2 gt $olddir_installed_version && echo true) == "true" ]];then
        echo 这是降级
    fi
    if [[ $(dpkg --compare-versions $2 eq $olddir_installed_version && echo true) == "true" ]];then
        echo 这是同版本重装或残留相同版本配置文件
    fi
fi


# log dir
if [ ! -d "/data/qomolo-get" ]; then
	mkdir -p /data/qomolo-get
	chmod 777 /data/qomolo-get
fi

if [ ! -d "/data/MP/agent/package_manager/" ]; then
	mkdir -p /data/MP/agent/package_manager/
	chmod 777 /data/MP/agent/package_manager/
fi

# service restart
systemctl daemon-reload || true
systemctl enable qomolo_get.service || true
systemctl restart qomolo_get.service || true
